<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="720dp"
    android:height="720dp"
    android:viewportWidth="720"
    android:viewportHeight="720">
  <group>
    <clip-path
        android:pathData="M82,71l578,0l0,578l-578,0z"/>
    <path
        android:pathData="M660,360C660,519.61 530.61,649 371,649C211.39,649 82,519.61 82,360C82,200.39 211.39,71 371,71C530.61,71 660,200.39 660,360"
        android:strokeWidth="1"
        android:fillColor="#EEE4FF"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M498.5,273.31L487.74,280.92C485.07,282.8 481.35,282.16 479.46,279.5L479.09,278.97C477.2,276.3 477.84,272.58 480.51,270.69L491.26,263.08C493.93,261.2 497.66,261.84 499.54,264.51L499.91,265.03C501.8,267.7 501.16,271.42 498.5,273.31"
      android:strokeWidth="1"
      android:fillColor="#9D6EE5"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M518.33,276.12L510.17,281.85C507.33,283.85 503.36,283.17 501.35,280.35L485.15,257.63C483.15,254.81 483.83,250.87 486.67,248.88L494.83,243.15C497.67,241.15 501.64,241.83 503.65,244.65L519.84,267.37C521.85,270.19 521.17,274.13 518.33,276.12"
      android:strokeWidth="1"
      android:fillColor="#CFAFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M501.52,246.07C499.55,245.28 490.74,251.7 489.1,252.93C484.53,256.35 486.5,255.98 486.06,255.99C487.64,255.95 488.09,254.96 494.02,251.65C497.74,249.57 503.74,246.94 501.52,246.07"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M506.37,251.03C503.74,250.15 478.08,270.93 499.41,257.43C501.99,255.79 509.14,251.96 506.37,251.03"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M509.69,258.01C508.11,258.11 492.47,268.92 495.36,267.94C501.1,265.98 515.37,257.67 509.69,258.01"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M514.48,263.03C512.08,262.26 491.12,278.55 504.34,271C507.81,269.02 517.32,263.94 514.48,263.03"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M519.36,269C517.37,268.9 507.81,276.02 506.04,278.85C505.04,280.43 523.65,269.22 519.36,269"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M518.97,269.63C518.61,267.9 511.99,257.79 511.9,257.64C506.86,249.98 502.73,241.11 496.48,244.91C495.91,245.26 473.28,261.5 496.35,246.6C502.22,242.81 502.83,247.91 513.72,265.9C514.7,267.53 515.57,269.5 517.1,270.71C517.96,271.41 519.21,270.77 518.97,269.63"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M500.22,251C495.63,251.23 489.71,259.09 491.25,257.87C492.36,257 504.08,250.81 500.22,251"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M503.29,258C500.6,258.07 492.3,263.6 497.9,261.54C498.7,261.25 506.33,257.93 503.29,258"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M505.4,264.04C503.3,263.51 498.55,268.15 500.43,268C501.49,267.91 507.98,264.7 505.4,264.04"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M509.43,272C505.67,271.88 503.41,276.51 504.13,275.95C507.39,273.45 511.53,272.07 509.43,272"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M515.55,275.06C512.31,276.93 506.45,282.94 510.23,280.37C517.42,275.47 516.07,274.76 515.55,275.06"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M484.02,270.94C483.65,271.28 488.85,270.18 489.73,269.04C490.93,267.49 487.93,267.33 484.02,270.94"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M493.47,274.03C490.85,273.61 488.76,277.59 490.86,276.92C491.95,276.97 495.28,274.32 493.47,274.03"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M461.99,554.4C450.55,518.46 457.16,483.48 479.68,460.82C492.68,447.73 509.33,440.63 525.71,440.63C531.13,440.63 536.52,441.41 541.74,443.01C554.46,446.91 565.45,455.2 574.32,467.35C582.5,490.12 587.28,512.04 586.5,531.43C585.08,566.86 552.24,593.09 522.62,597.06C493.94,600.9 471.85,585.36 461.99,554.4M614.82,517.02C609.84,490.37 601.29,471.14 591.55,457.26C564.07,383.3 505.49,304.33 479.13,268.8C473.28,260.91 468.66,254.68 466.98,252L450.1,262.52C452.2,265.88 456.55,271.75 463.14,280.63C484.59,309.54 528.08,368.17 557.25,427.65C553.76,426.03 550.5,424.81 547.56,423.91C519.54,415.32 488.18,424.05 465.7,446.7C437.94,474.68 429.54,517.09 443.23,560.15C445.52,567.33 448.36,573.9 451.72,579.82C434.84,560.92 419.29,529.23 411.7,476.38L392,479.2C405.73,574.85 445.45,609.08 476.36,620.94C486.96,625.01 498.18,627 509.55,627C533.85,627 558.77,617.93 579.44,600.75C606.69,578.11 620.24,546.03 614.82,517.02"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M587.18,577.12C545.27,630.13 433.83,652.55 402.01,486.11C401.91,485.61 402.63,485.4 402.78,485.9C415.76,529.5 429.09,582.59 474.77,603.31C542.54,634.05 618.42,562.14 599.71,501.23C547.57,331.53 392.78,505.27 470.56,577.23C473.37,579.82 469.17,584 466.37,581.42C391.29,512.02 514.88,359.77 582.68,451.7C613.79,493.89 619.83,535.83 587.18,577.12"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M567.87,413.14C556.41,379.6 446.51,238.2 458.99,260.34C483.55,303.95 529.39,358.84 549.31,390.41C567.17,418.7 561.31,421.94 567.52,432.91C569.61,436.62 573.99,431.03 567.87,413.14"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M482.13,592.09C511.68,596.57 540.62,593.66 562.38,583.63C577.45,576.69 601.83,560.25 595.78,528.23C588.24,488.37 567.18,474.1 550.84,469.15C526.91,461.89 497.9,470.35 478.68,490.19C467.8,501.42 451.31,525.66 463.09,561.75C467.4,574.93 474.1,584.86 482.13,592.09M525.79,628C509.34,628 493.83,623.89 480.48,615.92C477.71,614.27 475.05,612.46 472.52,610.5C466.95,609.49 461.38,608.25 455.81,606.78C422.97,598.08 375.53,577.28 336,527.51L351.6,515.15C383.7,555.56 421.41,575.06 450.41,584.46C447.54,579.27 445.12,573.69 443.17,567.76C432.13,534.23 439.53,500.95 463.47,476.43C487.81,451.51 524.96,441.02 555.91,450.32C586.55,459.53 607.48,485.86 614.84,524.47C615.59,528.41 616,532.3 616.09,536.12C617.79,530.22 619.06,523.95 619.85,517.32C631.24,421.23 610.22,313.89 596,265.61L615.11,260C629.75,309.73 651.4,420.37 639.62,519.66C631.59,587.42 580.24,623.06 535.92,627.49C532.51,627.83 529.13,628 525.79,628"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M616.2,299.65C641.31,515.72 640.97,559.2 567.68,600.73C553.82,608.58 538.75,615.82 522.64,617.16C463.55,622.05 427.23,537.08 459.83,492.65C489.13,452.71 570.42,445.33 594.99,493.6C621.99,546.65 574.65,604.73 507.5,594.85C503.1,594.2 501.18,600.89 505.64,601.59C562.57,610.4 613.93,568.05 606.63,514.89C598.19,453.41 527.73,442.3 480.39,468.75C423.84,500.34 437.36,579.32 481.61,610.47C528.32,643.34 611.11,593.51 628.1,540.17C649.84,471.94 612.96,271.75 616.2,299.65"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M450.53,591.03C441.28,582.81 409.81,585.87 354.52,532.99C346.57,525.38 369.81,563.28 423.84,586.3C431.2,589.44 441.68,595.3 449.81,593.74C450.99,593.51 451.41,591.81 450.53,591.03"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M612.76,539.25C594.95,578.89 549.72,604.15 526.95,605.89C512.24,607.02 500.54,598.22 487.58,593.04C485.23,592.1 484.05,595.65 485.94,596.92C514.55,615.98 536.26,610.44 564.31,594.46C609.56,568.69 614.29,535.83 612.76,539.25"
      android:strokeWidth="1"
      android:fillColor="#301884"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M462.93,603.83C449.58,589.89 453.57,590.82 449.43,587.33C447.67,585.84 446.2,589.46 453.54,597.4C458.53,602.79 463.67,604.6 462.93,603.83"
      android:strokeWidth="1"
      android:fillColor="#301884"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M619.01,292C617.1,291.91 609.42,294.22 607.59,296.11C604.09,299.73 617.11,297.45 620.38,295.08C621.82,294.05 620.5,292.08 619.01,292"
      android:strokeWidth="1"
      android:fillColor="#301884"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M383.97,139.38L373.42,146.71C370.19,148.95 365.69,148.19 363.41,145.03L349.31,125.45C347.03,122.28 347.8,117.86 351.03,115.62L361.58,108.29C364.8,106.05 369.31,106.81 371.59,109.97L385.69,129.55C387.97,132.72 387.2,137.14 383.97,139.38"
      android:strokeWidth="1"
      android:fillColor="#905DDD"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M470.97,262.38L460.42,269.71C457.2,271.95 452.69,271.19 450.41,268.03L436.31,248.45C434.03,245.28 434.8,240.86 438.03,238.62L448.58,231.29C451.8,229.05 456.31,229.81 458.59,232.97L472.69,252.55C474.97,255.72 474.2,260.14 470.97,262.38"
      android:strokeWidth="1"
      android:fillColor="#8E61D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M452.23,160.03C476.42,194.17 477.35,235.05 454.3,251.32C431.25,267.59 392.96,253.11 368.77,218.97C344.58,184.83 343.65,143.96 366.7,127.68C389.74,111.41 428.04,125.89 452.23,160.03"
      android:strokeWidth="1"
      android:fillColor="#AB7CF2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M454.38,251.34C437.83,263.04 413.38,258.79 392,242.73C411.13,253.96 431.49,255.85 445.88,245.68C468.82,229.44 467.9,188.71 443.81,154.67C437.1,145.18 429.29,137.2 421.02,131C432.39,137.66 443.33,147.63 452.31,160.34C476.4,194.37 477.32,235.11 454.38,251.34"
      android:strokeWidth="1"
      android:fillColor="#905DDD"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M427.88,246.39C388.38,237.3 363.64,188.51 364.75,160.22C365.36,144.82 373.15,141.59 377.26,135.14C382.63,126.72 358.94,122.72 353.72,151.95C347.6,186.24 381.15,251.84 427.84,246.72C428.01,246.7 428.08,246.43 427.88,246.39"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M364.06,111.02C360.65,111.56 351.88,117.26 351.15,119.54C349.75,123.93 358.43,132.92 358.05,130.64C356.9,123.82 353.43,122.17 356.51,119.5C359.47,116.93 363.34,116.45 365.75,113.13C366.57,111.99 365.21,110.84 364.06,111.02"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M459.71,247.12C458.1,247.91 448.88,254.83 447.1,256.68C446.33,257.48 450.19,256.89 455.01,253.63C463.43,247.96 460.87,246.55 459.71,247.12"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M467.74,257.01C461.43,257.37 457.37,263.46 455.56,263.15C454.42,262.96 448.92,258.52 450.19,260.7C459.61,276.97 476.01,256.54 467.74,257.01"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M639,247.84l-70,18.16l2.1,-47.96l42.55,-11.04z"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M644.67,283.4L581.26,299.77C577.47,300.75 573.56,298.48 572.57,294.72L563.23,259.21C562.24,255.46 564.54,251.58 568.33,250.6L631.74,234.23C635.53,233.25 639.44,235.52 640.43,239.28L649.77,274.79C650.76,278.54 648.46,282.42 644.67,283.4"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M641.96,155.74C633.26,122.35 599.13,102.33 565.74,111.04C532.35,119.74 512.33,153.87 521.04,187.26C529.74,220.66 563.87,240.67 597.26,231.96C630.65,223.26 650.67,189.13 641.96,155.74"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M632.29,158.26C624.98,130.21 596.31,113.4 568.26,120.71C540.21,128.02 523.4,156.69 530.71,184.74C538.02,212.79 566.69,229.6 594.74,222.29C622.79,214.98 639.6,186.31 632.29,158.26"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M594.74,222.29C574.32,227.61 553.58,220.17 541,204.9C553.55,215.21 570.68,219.57 587.61,215.16C615.67,207.87 632.47,179.2 625.16,151.15C623.16,143.51 619.6,136.72 614.87,131C623.12,137.79 629.38,147.15 632.29,158.28C639.6,186.33 622.79,214.99 594.74,222.29"
      android:strokeWidth="1"
      android:fillColor="#F2EAFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M580.69,126L581,135.28C569.98,134.73 558.79,138.82 550.6,147.44C535.93,162.88 536.37,187.13 551.43,202.04L549.44,204C531.92,186.57 531.43,158.29 548.55,140.26C557.28,131.07 568.93,126.29 580.69,126"
      android:strokeWidth="1"
      android:fillColor="#019FAC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M613,138.44L605.89,146C598.93,139.31 590.17,135.77 581.3,135.33L581,126.01C592.45,125.72 604,129.84 613,138.44"
      android:strokeWidth="1"
      android:fillColor="#EF5736"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M590.65,190l-10.65,-1.11l11.54,-54.89z"
      android:strokeWidth="1"
      android:fillColor="#F2EAFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M594.96,174.54C594.51,178.54 590.81,181.43 586.7,181C582.58,180.57 579.6,176.98 580.04,172.98C580.49,168.98 584.19,166.09 588.31,166.52C592.42,166.95 595.4,170.54 594.96,174.54"
      android:strokeWidth="1"
      android:fillColor="#F2EAFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M591.52,174.68C591.31,176.52 589.61,177.86 587.71,177.66C585.81,177.46 584.43,175.8 584.64,173.96C584.84,172.12 586.55,170.78 588.45,170.98C590.35,171.18 591.72,172.84 591.52,174.68"
      android:strokeWidth="1"
      android:fillColor="#F2EAFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M585.15,188l-10.15,-1.13l11,-55.87z"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M588.96,172.3C588.51,176.42 584.81,179.4 580.7,178.96C576.58,178.51 573.6,174.81 574.04,170.7C574.49,166.58 578.19,163.6 582.3,164.04C586.42,164.49 589.4,168.19 588.96,172.3"
      android:strokeWidth="1"
      android:fillColor="#351D87"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M584.98,171.88C584.77,173.8 583.05,175.19 581.12,174.98C579.2,174.77 577.81,173.05 578.02,171.13C578.23,169.2 579.95,167.81 581.87,168.02C583.8,168.23 585.19,169.95 584.98,171.88"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M593.3,115.53C583.39,109.53 561.71,115.33 551.46,121.23C506.68,147.02 516.01,214.1 558.84,225.96C563.02,227.11 513.14,203.43 527.69,156.56C541.44,112.26 589.15,118.88 592.95,118.21C594.26,117.98 594.31,116.13 593.3,115.53"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M635.68,149.23C638.76,169.43 647.78,192.44 608.16,222.74C605.94,224.44 626.93,218.29 637.83,193.27C651.22,162.52 634.2,139.59 635.68,149.23"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M634.35,238C624.53,238.04 613.76,242.5 604.25,244.93C577.42,251.77 566.57,250.01 569.45,265.33C571.13,274.27 572.95,285.33 578.5,292.74C580.72,295.72 575.24,272.03 575.21,271.89C574.22,267.02 570.98,259.78 576.82,256.51C579.97,254.74 627.48,245.23 635.19,241.11C636.67,240.32 635.96,238 634.35,238"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M642.35,267.87C641.56,266.28 638.96,267.04 639.03,268.77C639.25,274.49 640.92,276.25 637.77,276.74C634.44,278.93 623.71,279.52 604.17,289.9C603.26,290.38 604.91,289.44 638.34,280.82C644.74,279.17 645.15,273.55 642.35,267.87"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M618.43,252.33C611.97,257.2 611.04,263.57 593.29,276.65C591.22,278.18 600.72,274.57 608.79,267.78C622.22,256.49 620.86,250.49 618.43,252.33"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M625.1,259.29C622.67,261.41 620.81,264.27 618.45,266.51C616.33,268.53 613.73,270.36 612.05,272.74C611.13,274.02 623.9,270.71 626.95,260.35C627.28,259.25 625.89,258.59 625.1,259.29"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M616.86,233C611.24,233.25 567.98,242.8 577.32,241.95C580.25,241.68 611.3,237.39 617.18,235.42C618.39,235.01 618.24,232.94 616.86,233"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M582.84,152.11C582.75,150.77 581.02,162.3 581.01,162.45C580.86,163.65 583.69,163.92 582.84,152.11"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M579.37,183.16C582.73,170.53 574.83,185 577.6,185.52C580.15,186 582.29,186.53 582.91,184.88C583.27,183.93 582.6,182.6 579.37,183.16"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M61,150l432,0l0,436l-432,0z"/>
    <path
        android:pathData="M488.69,429.74L286.91,572.93C254.92,595.63 210.64,588.01 188,555.92L74.03,394.28C51.4,362.19 58.99,317.76 90.99,295.06L292.77,151.88C297.36,148.62 303.72,149.71 306.97,154.32L491.13,415.49C494.38,420.1 493.29,426.48 488.69,429.74"
        android:strokeWidth="1"
        android:fillColor="#A071E6"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M491.12,415.49L485.7,407.82L276.76,555.74C251.05,573.93 215.47,567.83 197.29,542.1L89.41,389.45C71.22,363.72 77.32,328.12 103.03,309.92L311.97,162L306.54,154.32C303.29,149.71 296.91,148.62 292.3,151.88L90.06,295.06C57.99,317.76 50.38,362.19 73.06,394.28L187.3,555.92C209.98,588.01 254.37,595.63 286.43,572.93L488.68,429.74C493.29,426.48 494.38,420.1 491.12,415.49"
        android:strokeWidth="1"
        android:fillColor="#6C3CB4"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M296.98,158.14C283.42,162.8 123.2,275.27 122.42,275.85C46.92,331.48 73.95,381.27 72.74,374.36C60.1,302.51 138.45,270.33 265.5,186.72C276.91,179.22 290.25,172.55 299.38,162.25C300.89,160.55 299.47,157.29 296.98,158.14"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M479.9,422.16C450.65,433.63 299.33,549.89 280.78,560.15C235.35,585.27 207.62,562.38 208,562.69C258.8,603.82 321.03,533.89 418.46,470.5C471.84,435.76 475.09,433.54 482.33,426.34C483.99,424.69 482.31,421.21 479.9,422.16"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M134.8,476.01C146.54,493.62 157.61,512.94 171.98,528.55C204.54,563.92 25.46,312 134.8,476.01"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M160.04,285.46C139.6,303.62 123.1,343.28 116.87,367.43C110.67,391.47 113.74,399.37 113.9,397.81C119.16,346.77 158.83,294.56 162.76,287.56C163.74,285.81 161.49,284.17 160.04,285.46"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M193.01,278.52C149.57,322.95 136.41,412.43 140.8,401C147.48,383.62 147.36,370.9 163.03,337.37C172.36,317.41 184.76,299.62 195.76,280.65C196.76,278.93 194.42,277.07 193.01,278.52"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M388.04,354.28C380.59,368.09 378.47,383.26 364.64,417.29C354.5,442.25 329.73,487.22 334.64,481.5C358.5,453.76 391.93,383.29 393,355.62C393.1,352.99 389.29,351.98 388.04,354.28"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M406.95,390.33C403.98,395.24 384.53,440.6 383.03,445.67C382.51,447.43 388.09,442.63 399.79,420.63C418.43,385.58 408.57,387.64 406.95,390.33"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M254.13,340.3C254.13,340.3 261.79,299.32 300.51,291.14C332.71,284.35 361.07,308.67 368.25,342.45C376.57,381.56 356.16,434.21 286.18,491C199.1,467.37 159.01,427.49 150.69,388.38C143.51,354.6 159.53,320.91 191.73,314.11C230.46,305.93 254.13,340.3 254.13,340.3"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M368.25,342.73C376.56,381.75 356.16,434.31 286.2,491C311,459.85 367.18,405.15 351.48,337.14C346.76,316.68 334.88,299.58 318.72,290.92C342.94,294.51 362.47,315.55 368.25,342.73"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M245.8,337.87C229.33,316.9 193.67,315.79 174.77,334.14C153.75,354.53 144.71,401.41 197.45,434.88C201.78,437.63 159.69,397.27 174.98,358.55C188.6,324.04 223.51,339.46 242.35,343.16C245.52,343.78 247.94,340.59 245.8,337.87"
        android:strokeWidth="1"
        android:fillColor="#018693"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M317.41,297.56C303.21,291.29 258.66,311.68 269.46,341.6C270.97,345.8 269.87,330.56 287.63,318.42C298.4,311.05 314.76,308.35 318.91,304.58C321.41,302.32 320.22,298.8 317.41,297.56"
        android:strokeWidth="1"
        android:fillColor="#018693"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M321.07,433.8C313.97,439.89 285.69,474.3 281.09,476.61C277.28,478.53 272.75,473.74 253.27,465.14C250.91,464.09 220.1,450.43 223.91,453.18C236.02,461.91 256.89,474.16 270.04,481.4C282.69,488.37 282.4,486.57 294.33,475C304.83,464.81 319.71,449.83 325.57,436.71C326.76,434.05 323.02,432.13 321.07,433.8"
        android:strokeWidth="1"
        android:fillColor="#018693"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
    <path
        android:pathData="M181.23,412.17l26.1,-5.51l17.37,32.1l4.25,-73.14l33.55,84.08l5.3,-77.7l11.27,18.59l20.13,-4.25l5.33,-36.68l26.75,44.62l2.75,-16.21l22.15,-4.68"
        android:strokeLineJoin="round"
        android:strokeWidth="3.424"
        android:fillColor="#00000000"
        android:strokeColor="#018693"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M177.55,408.49l26.1,-5.51l17.37,32.1l4.25,-73.14l33.55,84.08l5.3,-77.7l11.27,18.59l20.13,-4.25l5.33,-36.68l26.75,44.62l2.75,-16.21l22.15,-4.68"
        android:strokeLineJoin="round"
        android:strokeWidth="3.424"
        android:fillColor="#00000000"
        android:strokeColor="#018693"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M250.66,336.3C250.66,336.3 258.28,295.32 296.83,287.14C328.88,280.35 357.11,304.67 364.26,338.45C372.54,377.55 352.22,430.21 282.56,487C195.87,463.37 155.96,423.49 147.69,384.38C140.54,350.6 156.49,316.91 188.54,310.11C227.09,301.93 250.66,336.3 250.66,336.3"
        android:strokeWidth="1"
        android:fillColor="#FB6544"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M365.25,338.05C373.58,377.25 353.13,430.05 283,487C307.86,455.71 364.18,400.75 348.44,332.44C343.7,311.88 331.8,294.7 315.6,286C339.87,289.61 359.45,310.74 365.25,338.05"
        android:strokeWidth="1"
        android:fillColor="#ED5539"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M171.67,330.95C150.51,351.38 141.41,398.34 194.51,431.87C198.86,434.62 156.49,394.19 171.88,355.41C185.6,320.84 220.75,336.29 239.71,339.99C242.91,340.62 245.34,337.42 243.18,334.69C226.6,313.69 190.7,312.58 171.67,330.95Z"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M314.16,294.14C300.1,287.86 255.96,308.29 266.65,338.29C268.15,342.49 267.06,327.22 284.66,315.05C295.33,307.66 311.54,304.95 315.65,301.18C318.13,298.91 316.94,295.38 314.16,294.14"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M317.28,429.63C310.19,435.8 281.97,470.66 277.38,473.01C273.58,474.95 269.06,470.1 249.63,461.38C247.26,460.32 216.52,446.47 220.33,449.26C232.41,458.1 253.23,470.52 266.35,477.85C278.97,484.91 278.69,483.09 290.59,471.37C301.07,461.05 315.93,445.87 321.77,432.58C322.96,429.89 319.22,427.94 317.28,429.63"
        android:strokeWidth="1"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M178,408.48l26.1,-5.51l17.38,32.09l4.26,-73.11l33.56,84.05l5.3,-77.67l11.27,18.58l20.14,-4.25l5.33,-36.66l26.75,44.6l2.75,-16.2l22.16,-4.68"
        android:strokeLineJoin="round"
        android:strokeWidth="3.424"
        android:fillColor="#00000000"
        android:strokeColor="#EE4829"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
  <group>
    <clip-path
        android:pathData="M60,150l433,0l0,436l-433,0z"/>
    <path
        android:pathData="M174,404.48l26.1,-5.51l17.38,32.09l4.26,-73.11l33.56,84.04l5.3,-77.67l11.27,18.58l20.14,-4.25l5.33,-36.67l26.75,44.6l2.75,-16.2l22.16,-4.68"
        android:strokeLineJoin="round"
        android:strokeWidth="3.424"
        android:fillColor="#00000000"
        android:strokeColor="#FFFFFF"
        android:fillType="evenOdd"
        android:strokeLineCap="round"/>
  </group>
</vector>
