<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="467dp"
    android:height="398dp"
    android:viewportWidth="467"
    android:viewportHeight="398">
  <group>
    <clip-path
        android:pathData="M278.43,315.44l41,0l0,57l-41,0z"/>
    <path
        android:pathData="M309.64,323.2L319.08,359.3C320.81,365.93 315.91,372.44 309.18,372.44C305.89,372.44 302.81,370.83 300.88,368.12L280.38,339.3C276.93,334.45 278.18,327.64 283.13,324.39L294.18,317.11C300.02,313.27 307.85,316.35 309.64,323.2"
        android:strokeWidth="1"
        android:fillColor="#4D6FB4"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M155.46,314.58l41,0l0,57l-41,0z"/>
    <path
        android:pathData="M165.25,322.35L155.81,358.44C154.08,365.08 158.98,371.58 165.72,371.58C169,371.58 172.09,369.98 174.01,367.27L194.51,338.45C197.96,333.59 196.71,326.78 191.77,323.53L180.71,316.26C174.87,312.41 167.05,315.49 165.25,322.35"
        android:strokeWidth="1"
        android:fillColor="#4D6FB4"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M192.5,195.77L192.5,195.77C189.57,197.75 185.16,196.53 182.66,193.04L125.62,113.57C124.12,111.48 123.64,109.06 124.13,107.03C124.45,105.66 125.22,104.46 126.39,103.67C129.33,101.69 133.73,102.91 136.23,106.4L193.28,185.87C195.78,189.36 195.43,193.79 192.5,195.77"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M161.76,152C136.63,171.28 123.76,181.52 116.64,187.46C99.4,165.02 99.13,135.54 129.29,112.69C163.14,87.03 188.8,96.87 206.32,119.22C201.89,122.5 186.88,132.71 161.76,152Z"
      android:strokeWidth="1"
      android:fillColor="#5ABCFB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M286.02,195.06L286.02,195.06C288.95,197.04 293.36,195.82 295.86,192.33L352.9,112.86C354.39,110.77 354.87,108.35 354.39,106.32C354.06,104.95 353.3,103.75 352.12,102.96C349.19,100.98 344.78,102.2 342.28,105.68L285.24,185.16C282.74,188.65 283.08,193.08 286.02,195.06"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M314.02,150.5C339.29,167.64 351.23,176.78 361.12,183.89C377.59,161.09 376.58,130.15 345.42,110.54C311.26,89.04 284.46,96.87 267.69,119.6C272.28,122.31 288.76,133.36 314.02,150.5Z"
      android:strokeWidth="1"
      android:fillColor="#5ABCFB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M170.05,38.68m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M63.99,171.76m-7,0a7,7 0,1 1,14 0a7,7 0,1 1,-14 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M421.51,216.83m-7,0a7,7 0,1 1,14 0a7,7 0,1 1,-14 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M40.94,215.28m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M414.73,172.57m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M310,32.18m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M367.53,51.22m-3,0a3,3 0,1 1,6 0a3,3 0,1 1,-6 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M332.87,85.93m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M214.2,67.08m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#41A9EC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M342.69,28m-6,0a6,6 0,1 1,12 0a6,6 0,1 1,-12 0"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#41A9EC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M106.99,59.57m-4.5,0a4.5,4.5 0,1 1,9 0a4.5,4.5 0,1 1,-9 0"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M341.58,169.54C348.19,174.51 354.73,179.38 361.22,184.15C377.17,161.67 370.15,141.87 369.38,139.15C366.27,155.62 357,165.75 341.58,169.54Z"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M135.9,172C129.35,177.14 122.93,182.29 116.64,187.46C99.69,165.6 105.89,146.12 106.85,142.52C110.57,158.84 120.26,168.67 135.9,172Z"
      android:strokeWidth="1"
      android:fillColor="#41A9EC"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M342.71,246.15C342.71,303.31 296.37,349.65 239.21,349.65C182.04,349.65 135.71,303.31 135.71,246.15C135.71,188.99 182.04,142.65 239.21,142.65C296.37,142.65 342.71,188.99 342.71,246.15"
      android:strokeWidth="1"
      android:fillColor="#5ABCFB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M328.59,246.5C328.59,296.21 288.3,336.5 238.59,336.5C188.89,336.5 148.59,296.21 148.59,246.5C148.59,196.8 188.89,156.5 238.59,156.5C288.3,156.5 328.59,196.8 328.59,246.5"
      android:strokeWidth="1"
      android:fillColor="#4481FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M238.5,329.42C191.83,329.42 154,291.59 154,244.92C154,198.26 191.83,160.42 238.5,160.42C285.17,160.42 323,198.26 323,244.92C323,291.59 285.17,329.42 238.5,329.42Z"
      android:strokeWidth="1"
      android:fillColor="#EBFBFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M271.19,307.72C269.94,305.57 270.67,302.82 272.82,301.57C274.96,300.32 277.72,301.04 278.97,303.19C280.22,305.34 279.49,308.09 277.34,309.34C275.2,310.59 272.44,309.87 271.19,307.72"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M198.36,181.57C197.11,179.42 197.84,176.67 199.98,175.42C202.13,174.17 204.89,174.89 206.14,177.04C207.39,179.19 206.66,181.94 204.51,183.19C202.36,184.44 199.61,183.72 198.36,181.57"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M306.12,213.53C303.98,214.78 301.22,214.05 299.97,211.9C298.72,209.75 299.45,207 301.59,205.75C303.74,204.5 306.5,205.23 307.75,207.37C309,209.52 308.27,212.28 306.12,213.53"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M173.11,283.51C171.86,281.36 172.59,278.61 174.74,277.36C176.89,276.11 179.64,276.83 180.89,278.98C182.14,281.13 181.41,283.88 179.27,285.13C177.12,286.38 174.37,285.66 173.11,283.51Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M303.33,284.4C301.18,283.16 300.44,280.41 301.68,278.25C302.92,276.1 305.67,275.36 307.82,276.6C309.98,277.84 310.72,280.59 309.48,282.74C308.24,284.89 305.49,285.64 303.33,284.4"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M174.78,211.65C172.63,210.41 171.89,207.66 173.13,205.5C174.37,203.35 177.12,202.61 179.27,203.85C181.42,205.09 182.17,207.84 180.93,209.99C179.69,212.14 176.94,212.89 174.78,211.65"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M279.39,182.54C278.15,184.69 275.4,185.43 273.24,184.2C271.09,182.96 270.35,180.21 271.59,178.05C272.83,175.9 275.58,175.16 277.73,176.39C279.88,177.63 280.63,180.38 279.39,182.54"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M200.3,309.59C198.15,308.35 197.41,305.6 198.65,303.45C199.89,301.29 202.64,300.55 204.79,301.79C206.94,303.03 207.69,305.78 206.45,307.93C205.21,310.09 202.46,310.83 200.3,309.59Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M309.43,245.47C309.43,242.98 311.44,240.97 313.93,240.97C316.41,240.97 318.43,242.98 318.43,245.47C318.43,247.95 316.41,249.97 313.93,249.97C311.44,249.97 309.43,247.95 309.43,245.47Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M163.52,251.07C161.03,251.07 159.02,249.06 159.02,246.57C159.02,244.09 161.03,242.07 163.52,242.07C166,242.07 168.02,244.09 168.02,246.57C168.02,249.06 166,251.07 163.52,251.07"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M243.28,170.46C243.28,172.95 241.27,174.96 238.78,174.96C236.3,174.96 234.28,172.95 234.28,170.46C234.28,167.97 236.3,165.96 238.78,165.96C241.27,165.96 243.28,167.97 243.28,170.46"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M243.49,316.49C243.49,318.98 241.48,320.99 238.99,320.99C236.51,320.99 234.49,318.98 234.49,316.49C234.49,314.01 236.51,311.99 238.99,311.99C241.48,311.99 243.49,314.01 243.49,316.49"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M241.25,263.44C232,263.44 224.5,255.95 224.5,246.7C224.5,237.45 232,229.95 241.25,229.95C250.5,229.95 258,237.45 258,246.7C258,255.95 250.5,263.44 241.25,263.44Z"
      android:strokeWidth="1"
      android:fillColor="#5ABCFB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M241.25,257.86C235.08,257.86 230.09,252.86 230.09,246.7C230.09,240.53 235.08,235.53 241.25,235.53C247.42,235.53 252.42,240.53 252.42,246.7C252.42,252.86 247.42,257.86 241.25,257.86Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M239.77,183.17C241.06,183.19 242.1,184.23 242.12,185.52L242.94,237.12C242.96,238.36 241.97,239.39 240.73,239.4C240.7,239.41 240.68,239.41 240.66,239.4C239.37,239.39 238.32,238.34 238.3,237.05L237.49,185.45C237.47,184.21 238.46,183.19 239.7,183.17C239.72,183.17 239.75,183.17 239.77,183.17Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M280.18,223.68C280.86,224.77 280.53,226.21 279.44,226.89L250.25,245.13C249.16,245.81 247.72,245.48 247.04,244.39C246.36,243.3 246.69,241.86 247.78,241.18L276.98,222.94C278.07,222.26 279.5,222.59 280.18,223.68Z"
      android:strokeWidth="1"
      android:fillColor="#4D6FB4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M241.25,252.28C238.17,252.28 235.67,249.78 235.67,246.7C235.67,243.61 238.17,241.11 241.25,241.11C244.33,241.11 246.83,243.61 246.83,246.7C246.83,249.78 244.33,252.28 241.25,252.28Z"
      android:strokeWidth="1"
      android:fillColor="#D4F7FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M348.37,198.69C352.87,206.17 355.88,214.47 358.09,222.97C360.28,231.48 361.62,240.3 361.83,249.2C361.89,251.43 361.86,253.66 361.81,255.89L361.62,259.23C361.57,260.34 361.48,261.45 361.34,262.56C360.84,267 360.21,271.43 359.06,275.74C356.96,284.38 353.41,292.7 348.29,299.69L347.86,299.46C351.34,291.69 353.85,283.45 355.3,275C356.04,270.78 356.56,266.51 356.81,262.22C357.09,257.94 357.17,253.64 357.02,249.35C356.83,240.76 355.87,232.19 354.33,223.75C353.56,219.52 352.64,215.33 351.57,211.18C350.52,207.03 349.34,202.89 347.92,198.88L348.37,198.69Z"
      android:strokeWidth="1"
      android:fillColor="#4482FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M371.49,207.85C374.74,212.93 376.68,218.83 378.03,224.82L378.53,227.07C378.7,227.83 378.79,228.59 378.92,229.36C379.17,230.88 379.41,232.41 379.55,233.95C379.7,235.49 379.84,237.04 379.94,238.59L380.07,243.24C380.1,244.79 380,246.34 379.96,247.89C379.88,249.43 379.83,250.98 379.63,252.52C379.45,254.05 379.29,255.59 379.08,257.12C378.85,258.64 378.52,260.15 378.21,261.65C377.7,264.68 376.74,267.6 375.87,270.51C375.63,271.23 375.38,271.94 375.09,272.65C374.81,273.35 374.55,274.06 374.23,274.75C373.64,276.14 373.03,277.52 372.33,278.85L371.9,278.71L372.75,274.34C373.01,272.88 373.36,271.44 373.54,269.96C373.96,267.01 374.5,264.09 374.75,261.12C374.89,259.64 375.07,258.16 375.2,256.67L375.47,252.21C375.58,250.72 375.6,249.23 375.62,247.74C375.63,246.25 375.72,244.76 375.67,243.27C375.68,237.3 375.28,231.34 374.55,225.43C374.21,222.48 373.7,219.55 373.15,216.64C372.57,213.74 371.93,210.83 371.08,208.04L371.49,207.85Z"
      android:strokeWidth="1"
      android:fillColor="#4482FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M149.67,324.23C143.02,318.7 137.6,311.93 132.9,304.72C128.22,297.48 124.26,289.71 121.4,281.49C120.68,279.43 120.04,277.35 119.42,275.26L118.61,272.1C118.32,271.04 118.08,269.98 117.89,268.9C117.05,264.62 116.34,260.29 116.18,255.92C115.65,247.21 116.64,238.35 119.55,230.23L120.04,230.3C118.95,238.64 118.97,247.1 120.07,255.42C120.61,259.58 121.38,263.72 122.42,267.79C123.42,271.86 124.63,275.88 126.06,279.83C128.82,287.76 132.31,295.43 136.34,302.8C138.36,306.49 140.51,310.1 142.79,313.62C145.06,317.15 147.45,320.62 150.04,323.91L149.67,324.23Z"
      android:strokeWidth="1"
      android:fillColor="#4482FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M120.16,324.24C114.91,320.65 110.87,315.82 107.49,310.71L106.21,308.78C105.79,308.13 105.44,307.45 105.06,306.78C104.3,305.45 103.55,304.1 102.92,302.71C102.26,301.33 101.63,299.93 101.03,298.53L99.43,294.23C98.92,292.79 98.54,291.31 98.11,289.85C97.72,288.38 97.29,286.91 97.04,285.41C96.76,283.92 96.46,282.42 96.22,280.92C96,279.42 95.9,277.9 95.78,276.39C95.43,273.38 95.6,270.31 95.68,267.3C95.72,266.54 95.78,265.79 95.88,265.03C95.98,264.27 96.06,263.52 96.2,262.76C96.43,261.26 96.69,259.76 97.07,258.27L97.59,258.24L98,262.62C98.16,264.08 98.22,265.55 98.48,266.99C98.92,269.89 99.22,272.8 99.87,275.67C100.16,277.1 100.43,278.54 100.75,279.98L101.83,284.24C102.17,285.67 102.61,287.07 103.05,288.47C103.5,289.86 103.88,291.28 104.4,292.65C106.24,298.23 108.55,303.65 111.21,308.91C112.53,311.55 114.01,314.1 115.55,316.62C117.1,319.13 118.73,321.62 120.57,323.92L120.16,324.24Z"
      android:strokeWidth="1"
      android:fillColor="#4482FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
