<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="125dp"
    android:height="115dp"
    android:viewportWidth="125"
    android:viewportHeight="115">
  <group>
    <clip-path
        android:pathData="M16.11,15C12.74,15 10,17.75 10,21.15L10,21.15L10,103.85C10,107.25 12.74,110 16.11,110L16.11,110L82.89,110C86.26,110 89,107.25 89,103.85L89,103.85L89,21.15C89,17.75 86.26,15 82.89,15L82.89,15L16.11,15Z"/>
    <path
        android:pathData="M16.11,15C12.74,15 10,17.75 10,21.15L10,21.15L10,103.85C10,107.25 12.74,110 16.11,110L16.11,110L82.89,110C86.26,110 89,107.25 89,103.85L89,103.85L89,21.15C89,17.75 86.26,15 82.89,15L82.89,15L16.11,15Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="49.5"
            android:startY="15"
            android:endX="49.5"
            android:endY="110"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9A1A5"/>
          <item android:offset="1" android:color="#FFFBE4E2"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M81.97,104L17.03,104C15.91,104 15,103.1 15,101.98L15,24.02C15,22.9 15.91,22 17.03,22L81.97,22C83.09,22 84,22.9 84,24.02L84,101.98C84,103.1 83.09,104 81.97,104"
      android:strokeWidth="1"
      android:fillColor="#FFF0F0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M48.98,5C45.57,5 42.8,7.74 42.8,11.12L42.8,11.12L42.8,12.73L34.18,12.73C30.77,12.73 28,15.47 28,18.85L28,18.85L28,18.88C28,22.26 30.77,25 34.18,25L34.18,25L63.82,25C67.23,25 70,22.26 70,18.88L70,18.88L70,18.85C70,15.47 67.23,12.73 63.82,12.73L63.82,12.73L55.2,12.73L55.2,11.12C55.2,7.74 52.43,5 49.02,5L49.02,5L48.98,5Z"/>
    <path
        android:pathData="M48.98,5C45.57,5 42.8,7.74 42.8,11.12L42.8,12.73L34.18,12.73C30.77,12.73 28,15.47 28,18.85L28,18.88C28,22.26 30.77,25 34.18,25L63.82,25C67.23,25 70,22.26 70,18.88L70,18.85C70,15.47 67.23,12.73 63.82,12.73L55.2,12.73L55.2,11.12C55.2,7.74 52.43,5 49.02,5L48.98,5Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="49"
            android:startY="16.26"
            android:endX="49"
            android:endY="12.73"
            android:type="linear">
          <item android:offset="0" android:color="#FFF89095"/>
          <item android:offset="1" android:color="#FFFBE4E2"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M23.03,31C21.91,31 21,31.9 21,33.01L21,33.01L21,37.99C21,39.1 21.91,40 23.03,40L23.03,40L74.97,40C76.09,40 77,39.1 77,37.99L77,37.99L77,33.01C77,31.9 76.09,31 74.97,31L74.97,31L23.03,31Z"/>
    <path
        android:pathData="M23.03,31C21.91,31 21,31.9 21,33.01L21,37.99C21,39.1 21.91,40 23.03,40L74.97,40C76.09,40 77,39.1 77,37.99L77,33.01C77,31.9 76.09,31 74.97,31L23.03,31Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="21"
            android:startY="35.5"
            android:endX="77"
            android:endY="35.5"
            android:type="linear">
          <item android:offset="0" android:color="#D4F8BDC0"/>
          <item android:offset="1" android:color="#FFFDCECA"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M31.01,50C29.9,50 29,50.9 29,52L29,52C29,53.1 29.9,54 31.01,54L31.01,54L74.99,54C76.1,54 77,53.1 77,52L77,52C77,50.9 76.1,50 74.99,50L74.99,50L31.01,50Z"/>
    <path
        android:pathData="M31.01,50C29.9,50 29,50.9 29,52C29,53.1 29.9,54 31.01,54L74.99,54C76.1,54 77,53.1 77,52C77,50.9 76.1,50 74.99,50L31.01,50Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29"
            android:startY="52"
            android:endX="76.71"
            android:endY="52"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9C6C8"/>
          <item android:offset="1" android:color="#FFFFD2CE"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,51.5C25,52.33 24.33,53 23.5,53C22.67,53 22,52.33 22,51.5C22,50.67 22.67,50 23.5,50C24.33,50 25,50.67 25,51.5"
      android:strokeWidth="1"
      android:fillColor="#FBC9C9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M31.01,63C29.9,63 29,63.9 29,65L29,65C29,66.1 29.9,67 31.01,67L31.01,67L74.99,67C76.1,67 77,66.1 77,65L77,65C77,63.9 76.1,63 74.99,63L74.99,63L31.01,63Z"/>
    <path
        android:pathData="M31.01,63C29.9,63 29,63.9 29,65L29,65C29,66.1 29.9,67 31.01,67L31.01,67L74.99,67C76.1,67 77,66.1 77,65L77,65C77,63.9 76.1,63 74.99,63L74.99,63L31.01,63Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29"
            android:startY="65"
            android:endX="77"
            android:endY="65"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9C6C8"/>
          <item android:offset="1" android:color="#FFFFD2CE"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,65.5C25,66.33 24.33,67 23.5,67C22.67,67 22,66.33 22,65.5C22,64.67 22.67,64 23.5,64C24.33,64 25,64.67 25,65.5"
      android:strokeWidth="1"
      android:fillColor="#FBC9C9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M31.01,77C29.9,77 29,77.9 29,79L29,79C29,80.1 29.9,81 31.01,81L31.01,81L74.99,81C76.1,81 77,80.1 77,79L77,79C77,77.9 76.1,77 74.99,77L74.99,77L31.01,77Z"/>
    <path
        android:pathData="M31.01,77C29.9,77 29,77.9 29,79L29,79C29,80.1 29.9,81 31.01,81L31.01,81L74.99,81C76.1,81 77,80.1 77,79L77,79C77,77.9 76.1,77 74.99,77L74.99,77L31.01,77Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29"
            android:startY="79"
            android:endX="77"
            android:endY="79"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9C7C8"/>
          <item android:offset="1" android:color="#FFFFD2CE"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,78.5C25,79.33 24.33,80 23.5,80C22.67,80 22,79.33 22,78.5C22,77.67 22.67,77 23.5,77C24.33,77 25,77.67 25,78.5"
      android:strokeWidth="1"
      android:fillColor="#FAC9C8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M31.01,90C29.9,90 29,90.9 29,92L29,92C29,93.11 29.9,94 31.01,94L31.01,94L74.99,94C76.1,94 77,93.11 77,92L77,92C77,90.9 76.1,90 74.99,90L74.99,90L31.01,90Z"/>
    <path
        android:pathData="M31.01,90C29.9,90 29,90.9 29,92L29,92C29,93.11 29.9,94 31.01,94L31.01,94L74.99,94C76.1,94 77,93.11 77,92L77,92C77,90.9 76.1,90 74.99,90L74.99,90L31.01,90Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="29"
            android:startY="92"
            android:endX="77"
            android:endY="92"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9C6C8"/>
          <item android:offset="1" android:color="#FFFFD2CE"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,92.5C25,93.33 24.33,94 23.5,94C22.67,94 22,93.33 22,92.5C22,91.67 22.67,91 23.5,91C24.33,91 25,91.67 25,92.5"
      android:strokeWidth="1"
      android:fillColor="#FAC9C8"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M101.31,40.93L57.43,84.28C57.43,83.97 56.81,84.59 56.81,84.9L56.81,84.9C56.5,85.21 56.19,85.83 56.19,85.83L56.19,85.83L53.08,98.52C53.08,98.52 52.77,99.76 53.39,100.38L53.39,100.38C54.01,101 55.26,101 55.26,101L55.26,101L68.01,97.9C68.01,97.9 68.64,97.59 69.26,97.59L69.26,97.59C69.57,97.28 70.19,96.98 70.19,96.98L70.19,96.98L114.07,53.31C114.07,53.31 115,52.39 115,51.77L115,51.77C115,50.84 114.38,49.91 114.38,49.91L114.38,49.91L105.04,40.62C105.04,40.62 104.42,40 103.18,40L103.18,40C101.93,40 101.31,40.93 101.31,40.93"/>
    <path
        android:pathData="M101.31,40.93L57.43,84.28C57.43,83.97 56.81,84.59 56.81,84.9L56.81,84.9C56.5,85.21 56.19,85.83 56.19,85.83L56.19,85.83L53.08,98.52C53.08,98.52 52.77,99.76 53.39,100.38L53.39,100.38C54.01,101 55.26,101 55.26,101L55.26,101L68.01,97.9C68.01,97.9 68.64,97.59 69.26,97.59L69.26,97.59C69.57,97.28 70.19,96.98 70.19,96.98L70.19,96.98L114.07,53.31C114.07,53.31 115,52.39 115,51.77L115,51.77C115,50.84 114.38,49.91 114.38,49.91L114.38,49.91L105.04,40.62C105.04,40.62 104.42,40 103.18,40L103.18,40C101.93,40 101.31,40.93 101.31,40.93"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="52.77"
            android:startY="70.5"
            android:endX="115"
            android:endY="70.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9A1A5"/>
          <item android:offset="1" android:color="#FFFBE4E2"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
