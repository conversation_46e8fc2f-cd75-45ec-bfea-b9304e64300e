<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="379dp"
    android:height="191dp"
    android:viewportWidth="379"
    android:viewportHeight="191">
  <path
      android:pathData="M0,0L359,0C370.05,-0 379,8.95 379,20L379,171C379,182.05 370.05,191 359,191L0,191L0,191L0,0Z"
      android:strokeWidth="1"
      android:fillColor="#FFF5F5"
      android:fillAlpha="0.5"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M0,0L359,0C370.05,-0 379,8.95 379,20L379,171C379,182.05 370.05,191 359,191L0,191L0,191L0,0Z"/>
    <group>
      <clip-path
          android:pathData="M20,-9L689,-9A20,20 0,0 1,709 11L709,152A20,20 0,0 1,689 172L20,172A20,20 0,0 1,0 152L0,11A20,20 0,0 1,20 -9z"/>
      <path
          android:pathData="M84.6,141L84.49,141C83.37,140.95 82.47,140.12 82.35,139.02L72.14,44.36L61.24,113.31C61.07,114.36 60.17,115.15 59.07,115.18C58.02,115.28 57.04,114.49 56.79,113.44L44.16,57.37L40.91,94.5C40.82,95.55 39.98,96.4 38.91,96.51C37.84,96.61 36.84,95.98 36.51,94.99L24.09,56.55L17.42,72.32C17.08,73.14 16.25,73.68 15.33,73.68L-7.24,73.68C-8.49,73.68 -9.51,72.69 -9.51,71.47C-9.51,70.24 -8.49,69.25 -7.24,69.25L13.84,69.25L22.3,49.23C22.66,48.38 23.51,47.8 24.48,47.86C25.43,47.9 26.25,48.52 26.53,49.41L37.4,83.03L40.95,42.51C41.05,41.42 41.93,40.56 43.05,40.48C44.13,40.44 45.17,41.14 45.4,42.22L58.61,100.95L70.32,26.87C70.48,25.77 71.37,24.95 72.6,25C73.73,25.03 74.67,25.87 74.78,26.98L85.29,124.25L98.93,58.46C99.15,57.54 99.97,56.82 100.95,56.74C101.97,56.66 102.87,57.19 103.25,58.09L115.27,86.58L124.63,59.7C124.96,58.73 125.89,58.15 126.94,58.2C127.98,58.28 128.81,59.04 128.98,60.05L132.85,82.82L137.43,70.71C137.76,69.84 138.6,69.26 139.55,69.26L153.85,69.26C155.1,69.26 156.12,70.26 156.12,71.48C156.12,72.7 155.1,73.7 153.85,73.7L141.13,73.7L134.15,92.16C133.79,93.09 132.84,93.72 131.82,93.59C130.8,93.49 129.98,92.74 129.8,91.76L126.01,69.46L117.6,93.56C117.31,94.42 116.47,95.02 115.53,95.05C114.63,95.18 113.74,94.54 113.38,93.68L101.91,66.48L86.82,139.22C86.6,140.26 85.67,141 84.6,141Z"
          android:strokeWidth="1"
          android:fillType="nonZero"
          android:strokeColor="#00000000">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="156.12"
              android:startY="82.98"
              android:endX="-9.51"
              android:endY="82.98"
              android:type="linear">
            <item android:offset="0" android:color="#FFFFADDA"/>
            <item android:offset="1" android:color="#FFFFE0F1"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M245.78,141L245.66,141C244.55,140.95 243.64,140.12 243.53,139.02L233.32,44.36L222.42,113.31C222.25,114.36 221.35,115.15 220.25,115.18C219.2,115.28 218.21,114.49 217.97,113.44L205.34,57.37L202.09,94.5C201.99,95.55 201.15,96.4 200.09,96.51C199.02,96.61 198.02,95.98 197.69,94.99L185.26,56.55L178.6,72.32C178.25,73.14 177.43,73.68 176.51,73.68L153.94,73.68C152.69,73.68 151.67,72.69 151.67,71.47C151.67,70.24 152.69,69.25 153.94,69.25L175.02,69.25L183.47,49.23C183.83,48.38 184.69,47.8 185.66,47.86C186.61,47.9 187.43,48.52 187.71,49.41L198.58,83.03L202.12,42.51C202.22,41.42 203.11,40.56 204.23,40.48C205.31,40.44 206.34,41.14 206.57,42.22L219.79,100.95L231.5,26.87C231.66,25.77 232.55,24.95 233.78,25C234.91,25.03 235.85,25.87 235.96,26.98L246.47,124.25L260.11,58.46C260.32,57.54 261.15,56.82 262.13,56.74C263.15,56.66 264.05,57.19 264.43,58.09L276.45,86.58L285.81,59.7C286.13,58.73 287.07,58.15 288.12,58.2C289.15,58.28 289.99,59.04 290.16,60.05L294.03,82.82L298.61,70.71C298.94,69.84 299.78,69.26 300.73,69.26L315.03,69.26C316.28,69.26 317.29,70.26 317.29,71.48C317.29,72.7 316.28,73.7 315.03,73.7L302.31,73.7L295.33,92.16C294.97,93.09 294.01,93.72 293,93.59C291.98,93.49 291.16,92.74 290.98,91.76L287.18,69.46L278.78,93.56C278.48,94.42 277.65,95.02 276.71,95.05C275.81,95.18 274.92,94.54 274.56,93.68L263.08,66.48L248,139.22C247.78,140.26 246.85,141 245.78,141Z"
          android:strokeWidth="1"
          android:fillType="nonZero"
          android:strokeColor="#00000000">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="317.29"
              android:startY="82.98"
              android:endX="151.67"
              android:endY="82.98"
              android:type="linear">
            <item android:offset="0" android:color="#FFFD9FD2"/>
            <item android:offset="1" android:color="#FFFFB4DD"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M408.07,141L407.95,141C406.84,140.95 405.93,140.12 405.82,139.02L395.61,44.36L384.7,113.31C384.54,114.36 383.64,115.15 382.54,115.18C381.49,115.28 380.5,114.49 380.26,113.44L367.63,57.37L364.38,94.5C364.28,95.55 363.44,96.4 362.38,96.51C361.31,96.61 360.31,95.98 359.98,94.99L347.55,56.55L340.89,72.32C340.54,73.14 339.72,73.68 338.8,73.68L316.23,73.68C314.98,73.68 313.96,72.69 313.96,71.47C313.96,70.24 314.98,69.25 316.23,69.25L337.31,69.25L345.76,49.23C346.12,48.38 346.98,47.8 347.95,47.86C348.9,47.9 349.72,48.52 350,49.41L360.87,83.03L364.41,42.51C364.51,41.42 365.4,40.56 366.51,40.48C367.6,40.44 368.63,41.14 368.86,42.22L382.08,100.95L393.78,26.87C393.95,25.77 394.83,24.95 396.07,25C397.2,25.03 398.13,25.87 398.25,26.98L408.76,124.25L422.4,58.46C422.61,57.54 423.43,56.82 424.42,56.74C425.44,56.66 426.34,57.19 426.72,58.09L438.74,86.58L448.09,59.7C448.42,58.73 449.36,58.15 450.41,58.2C451.44,58.28 452.28,59.04 452.44,60.05L456.32,82.82L460.9,70.71C461.23,69.84 462.07,69.26 463.02,69.26L477.32,69.26C478.57,69.26 479.58,70.26 479.58,71.48C479.58,72.7 478.57,73.7 477.32,73.7L464.59,73.7L457.62,92.16C457.26,93.09 456.3,93.72 455.29,93.59C454.27,93.49 453.45,92.74 453.27,91.76L449.47,69.46L441.07,93.56C440.77,94.42 439.93,95.02 439,95.05C438.1,95.18 437.21,94.54 436.85,93.68L425.37,66.48L410.28,139.22C410.07,140.26 409.13,141 408.07,141Z"
          android:strokeWidth="1"
          android:fillType="nonZero"
          android:strokeColor="#00000000">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="479.58"
              android:startY="82.98"
              android:endX="313.96"
              android:endY="82.98"
              android:type="linear">
            <item android:offset="0" android:color="#FFFE7BC2"/>
            <item android:offset="1" android:color="#FFFDA0D3"/>
          </gradient>
        </aapt:attr>
      </path>
    </group>
  </group>
</vector>
