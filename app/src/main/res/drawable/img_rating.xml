<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="242dp"
    android:height="217dp"
    android:viewportWidth="242"
    android:viewportHeight="217">
  <path
      android:pathData="M199.54,74.64C199.05,74.8 198.63,75.23 198.56,75.85L197.71,82.54C197.65,83.03 197.35,83.46 196.92,83.68L191.09,86.61C190,87.15 190.02,88.77 191.12,89.33L197.03,92.43C197.46,92.66 197.77,93.08 197.84,93.59L198.87,100.31C199.02,101.3 199.99,101.83 200.79,101.57C201,101.5 201.21,101.38 201.39,101.19L205.89,96.4C206.05,96.22 206.27,96.09 206.49,96.02C206.71,95.94 206.95,95.92 207.2,95.97L213.65,97.2C213.9,97.25 214.14,97.23 214.36,97.16C215.18,96.89 215.65,95.9 215.18,95.01L212.06,88.97C211.83,88.51 211.82,87.99 212.03,87.55L215,81.57C215.56,80.47 214.62,79.15 213.42,79.35L206.98,80.42C206.51,80.5 206.01,80.32 205.67,79.96L201.06,75.04C200.63,74.58 200.04,74.48 199.54,74.64Z"
      android:strokeWidth="1"
      android:fillColor="#FFDA6C"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M28.71,91.29C28.48,91.37 28.28,91.57 28.25,91.86L27.86,94.94C27.83,95.17 27.7,95.37 27.49,95.47L24.81,96.82C24.3,97.07 24.31,97.82 24.82,98.08L27.55,99.5C27.74,99.61 27.89,99.81 27.92,100.04L28.39,103.15C28.47,103.6 28.91,103.84 29.28,103.72C29.38,103.69 29.47,103.64 29.56,103.55L31.64,101.34C31.71,101.25 31.81,101.19 31.91,101.16C32.02,101.13 32.13,101.12 32.24,101.14L35.22,101.71C35.33,101.73 35.44,101.72 35.55,101.69C35.92,101.57 36.14,101.11 35.93,100.7L34.48,97.91C34.38,97.7 34.37,97.46 34.47,97.25L35.84,94.5C36.1,93.99 35.67,93.38 35.11,93.47L32.14,93.96C31.92,94 31.69,93.92 31.54,93.75L29.4,91.48C29.21,91.27 28.93,91.22 28.71,91.29Z"
      android:strokeWidth="1"
      android:fillColor="#FFDA6C"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M129.21,190C126.58,190 121.41,189.6 115.93,189.18C111.99,188.88 107.91,188.57 104.45,188.38L104.28,188.35C100.68,187.37 100.68,184.42 100.68,182.83L100.68,149.18C100.68,145.78 101.66,143.77 104.08,142.26C109.77,138.7 119.16,130.87 118.13,118.42C117.97,116.48 118.44,114.85 119.5,113.71C120.52,112.61 122.04,112 123.77,112C126.55,112 129.89,113.72 130.59,116.91C130.66,117.22 130.74,117.58 130.84,117.98C131.78,121.94 133.74,130.17 129.75,139.24C129.41,139.97 129.43,140.63 129.81,141.22C130.54,142.36 132.49,143.1 134.77,143.1C136.3,143.1 138.58,142.78 140.99,142.43C144.03,142 147.46,141.51 150.41,141.51C154.07,141.51 156.38,142.29 157.49,143.9C159.7,147.12 158.07,151.72 156.34,156.58C155.54,158.85 154.7,161.21 154.27,163.42C154.01,164.77 153.77,166.36 153.52,168.05C152.45,175.1 151.11,183.89 146.84,186.65C141.69,190 134.77,190 129.21,190ZM93.53,189.85L86,189.85C83.55,189.85 81.56,187.87 81.56,185.44L81.56,146.97C81.56,144.53 83.55,142.56 86,142.56L93.53,142.56C95.98,142.56 97.97,144.53 97.97,146.97L97.97,185.44C97.97,187.87 95.98,189.85 93.53,189.85L93.53,189.85Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="159.7"
          android:startY="151"
          android:endX="81.56"
          android:endY="151"
          android:type="linear">
        <item android:offset="0" android:color="#FF8E61D2"/>
        <item android:offset="1" android:color="#FF6937B6"/>
      </gradient>
    </aapt:attr>
  </path>
  <group>
    <clip-path
        android:pathData="M53.56,73l64,0l0,30l-64,0z"/>
    <path
        android:pathData="M86.25,89.37C68.06,88.1 61.6,88.98 56.46,85.3C52.49,82.45 50.29,71.83 65.7,73.11C81.11,74.38 102.94,84.28 117.07,101.31C120.15,108.04 108.69,92.75 86.25,89.37"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M92.56,27l36,0l0,70l-36,0z"/>
    <path
        android:pathData="M113.91,64.54C105.69,47.26 94.37,50.69 92.66,38.4C91.3,28.64 104.42,20.82 114.6,33.9C124.77,46.98 132.08,71.99 126.79,95.3C123.72,102.71 127.53,84.73 113.91,64.54"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M135.56,47l52,0l0,56l-52,0z"/>
    <path
        android:pathData="M159.34,75.23C173.35,63.84 181.39,68.09 186.54,59.13C189.81,53.42 185.36,41.47 171.22,49.94C157.08,58.41 140.93,78.2 135.56,101.21C135.83,109.13 139.48,89.04 159.34,75.23"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <group>
    <clip-path
        android:pathData="M74.56,109l18,0l0,18l-18,0z"/>
    <path
        android:pathData="M92.56,118C92.56,122.97 88.53,127 83.56,127C78.59,127 74.56,122.97 74.56,118C74.56,113.03 78.59,109 83.56,109C88.53,109 92.56,113.03 92.56,118"
        android:strokeWidth="1"
        android:fillColor="#8E61D2"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M164.56,98.5C164.56,99.88 163.44,101 162.06,101C160.68,101 159.56,99.88 159.56,98.5C159.56,97.12 160.68,96 162.06,96C163.44,96 164.56,97.12 164.56,98.5"
      android:strokeWidth="1"
      android:fillColor="#8E61D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M164.56,114C164.56,115.66 163.22,117 161.56,117C159.9,117 158.56,115.66 158.56,114C158.56,112.34 159.9,111 161.56,111C163.22,111 164.56,112.34 164.56,114"
      android:strokeWidth="1"
      android:fillColor="#8E61D2"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
