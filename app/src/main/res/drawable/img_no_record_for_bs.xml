<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="125dp"
    android:height="115dp"
    android:viewportWidth="125"
    android:viewportHeight="115">
  <group>
    <clip-path
        android:pathData="M16.11,16C12.74,16 10,18.75 10,22.15L10,22.15L10,104.85C10,108.25 12.74,111 16.11,111L16.11,111L82.89,111C86.26,111 89,108.25 89,104.85L89,104.85L89,22.15C89,18.75 86.26,16 82.89,16L82.89,16L16.11,16Z"/>
    <path
        android:pathData="M16.11,16C12.74,16 10,18.75 10,22.15L10,22.15L10,104.85C10,108.25 12.74,111 16.11,111L16.11,111L82.89,111C86.26,111 89,108.25 89,104.85L89,104.85L89,22.15C89,18.75 86.26,16 82.89,16L82.89,16L16.11,16Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="49.5"
            android:startY="16"
            android:endX="49.5"
            android:endY="111"
            android:type="linear">
          <item android:offset="0" android:color="#FFB7A1F9"/>
          <item android:offset="1" android:color="#FFEAE2FB"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M82.97,105L18.03,105C16.91,105 16,104.1 16,102.98L16,25.02C16,23.9 16.91,23 18.03,23L82.97,23C84.09,23 85,23.9 85,25.02L85,102.98C85,104.1 84.09,105 82.97,105"
      android:strokeWidth="1"
      android:fillColor="#F4F0FF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M49.98,5C46.57,5 43.8,7.74 43.8,11.12L43.8,11.12L43.8,12.73L35.18,12.73C31.77,12.73 29,15.47 29,18.85L29,18.85L29,18.88C29,22.26 31.77,25 35.18,25L35.18,25L64.82,25C68.23,25 71,22.26 71,18.88L71,18.88L71,18.85C71,15.47 68.23,12.73 64.82,12.73L64.82,12.73L56.2,12.73L56.2,11.12C56.2,7.74 53.43,5 50.02,5L50.02,5L49.98,5Z"/>
    <path
        android:pathData="M49.98,5C46.57,5 43.8,7.74 43.8,11.12L43.8,12.73L35.18,12.73C31.77,12.73 29,15.47 29,18.85L29,18.88C29,22.26 31.77,25 35.18,25L64.82,25C68.23,25 71,22.26 71,18.88L71,18.85C71,15.47 68.23,12.73 64.82,12.73L56.2,12.73L56.2,11.12C56.2,7.74 53.43,5 50.02,5L49.98,5Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="50"
            android:startY="16.26"
            android:endX="50"
            android:endY="12.73"
            android:type="linear">
          <item android:offset="0" android:color="#FFAA90F8"/>
          <item android:offset="1" android:color="#FFEAE2FB"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M24.03,32C22.91,32 22,32.9 22,34.01L22,34.01L22,38.99C22,40.1 22.91,41 24.03,41L24.03,41L75.97,41C77.09,41 78,40.1 78,38.99L78,38.99L78,34.01C78,32.9 77.09,32 75.97,32L75.97,32L24.03,32Z"/>
    <path
        android:pathData="M24.03,32C22.91,32 22,32.9 22,34.01L22,38.99C22,40.1 22.91,41 24.03,41L75.97,41C77.09,41 78,40.1 78,38.99L78,34.01C78,32.9 77.09,32 75.97,32L24.03,32Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22"
            android:startY="36.5"
            android:endX="78"
            android:endY="36.5"
            android:type="linear">
          <item android:offset="0" android:color="#99C6BDF8"/>
          <item android:offset="1" android:color="#99D6CAFD"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M32.01,51C30.9,51 30,51.9 30,53L30,53C30,54.1 30.9,55 32.01,55L32.01,55L75.99,55C77.1,55 78,54.1 78,53L78,53C78,51.9 77.1,51 75.99,51L75.99,51L32.01,51Z"/>
    <path
        android:pathData="M32.01,51C30.9,51 30,51.9 30,53C30,54.1 30.9,55 32.01,55L75.99,55C77.1,55 78,54.1 78,53C78,51.9 77.1,51 75.99,51L32.01,51Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="30"
            android:startY="53"
            android:endX="77.71"
            android:endY="53"
            android:type="linear">
          <item android:offset="0" android:color="#99C6BDF8"/>
          <item android:offset="1" android:color="#99D6CAFD"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,52.5C25,53.33 24.33,54 23.5,54C22.67,54 22,53.33 22,52.5C22,51.67 22.67,51 23.5,51C24.33,51 25,51.67 25,52.5"
      android:strokeWidth="1"
      android:fillColor="#D3C7FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M32.01,64C30.9,64 30,64.9 30,66L30,66C30,67.1 30.9,68 32.01,68L32.01,68L75.99,68C77.1,68 78,67.1 78,66L78,66C78,64.9 77.1,64 75.99,64L75.99,64L32.01,64Z"/>
    <path
        android:pathData="M32.01,64C30.9,64 30,64.9 30,66C30,67.1 30.9,68 32.01,68L75.99,68C77.1,68 78,67.1 78,66C78,64.9 77.1,64 75.99,64L32.01,64Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="30"
            android:startY="66"
            android:endX="78"
            android:endY="66"
            android:type="linear">
          <item android:offset="0" android:color="#99C6BDF8"/>
          <item android:offset="1" android:color="#99D6CAFD"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,66.5C25,67.33 24.33,68 23.5,68C22.67,68 22,67.33 22,66.5C22,65.67 22.67,65 23.5,65C24.33,65 25,65.67 25,66.5"
      android:strokeWidth="1"
      android:fillColor="#D3C7FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M32.01,78C30.9,78 30,78.9 30,80L30,80C30,81.1 30.9,82 32.01,82L32.01,82L75.99,82C77.1,82 78,81.1 78,80L78,80C78,78.9 77.1,78 75.99,78L75.99,78L32.01,78Z"/>
    <path
        android:pathData="M32.01,78C30.9,78 30,78.9 30,80L30,80C30,81.1 30.9,82 32.01,82L32.01,82L75.99,82C77.1,82 78,81.1 78,80L78,80C78,78.9 77.1,78 75.99,78L75.99,78L32.01,78Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="30"
            android:startY="80"
            android:endX="78"
            android:endY="80"
            android:type="linear">
          <item android:offset="0" android:color="#99C6BDF8"/>
          <item android:offset="1" android:color="#99D6CAFD"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,79.5C25,80.33 24.33,81 23.5,81C22.67,81 22,80.33 22,79.5C22,78.67 22.67,78 23.5,78C24.33,78 25,78.67 25,79.5"
      android:strokeWidth="1"
      android:fillColor="#D3C7FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M32.01,91C30.9,91 30,91.9 30,93L30,93C30,94.11 30.9,95 32.01,95L32.01,95L75.99,95C77.1,95 78,94.11 78,93L78,93C78,91.9 77.1,91 75.99,91L75.99,91L32.01,91Z"/>
    <path
        android:pathData="M32.01,91C30.9,91 30,91.9 30,93L30,93C30,94.11 30.9,95 32.01,95L32.01,95L75.99,95C77.1,95 78,94.11 78,93L78,93C78,91.9 77.1,91 75.99,91L75.99,91L32.01,91Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="30"
            android:startY="93"
            android:endX="78"
            android:endY="93"
            android:type="linear">
          <item android:offset="0" android:color="#99C6BDF8"/>
          <item android:offset="1" android:color="#99D6CAFD"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M25,92.5C25,93.33 24.33,94 23.5,94C22.67,94 22,93.33 22,92.5C22,91.67 22.67,91 23.5,91C24.33,91 25,91.67 25,92.5"
      android:strokeWidth="1"
      android:fillColor="#D3C7FB"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M102.31,41.93L58.43,85.28C58.43,84.97 57.81,85.59 57.81,85.9L57.81,85.9C57.5,86.21 57.19,86.83 57.19,86.83L57.19,86.83L54.08,99.52C54.08,99.52 53.77,100.76 54.39,101.38L54.39,101.38C55.01,102 56.26,102 56.26,102L56.26,102L69.01,98.9C69.01,98.9 69.64,98.59 70.26,98.59L70.26,98.59C70.57,98.28 71.19,97.98 71.19,97.98L71.19,97.98L115.07,54.31C115.07,54.31 116,53.39 116,52.77L116,52.77C116,51.84 115.38,50.91 115.38,50.91L115.38,50.91L106.04,41.62C106.04,41.62 105.42,41 104.18,41L104.18,41C102.93,41 102.31,41.93 102.31,41.93"/>
    <path
        android:pathData="M102.31,41.93L58.43,85.28C58.43,84.97 57.81,85.59 57.81,85.9L57.81,85.9C57.5,86.21 57.19,86.83 57.19,86.83L57.19,86.83L54.08,99.52C54.08,99.52 53.77,100.76 54.39,101.38L54.39,101.38C55.01,102 56.26,102 56.26,102L56.26,102L69.01,98.9C69.01,98.9 69.64,98.59 70.26,98.59L70.26,98.59C70.57,98.28 71.19,97.98 71.19,97.98L71.19,97.98L115.07,54.31C115.07,54.31 116,53.39 116,52.77L116,52.77C116,51.84 115.38,50.91 115.38,50.91L115.38,50.91L106.04,41.62C106.04,41.62 105.42,41 104.18,41L104.18,41C102.93,41 102.31,41.93 102.31,41.93"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="53.77"
            android:startY="71.5"
            android:endX="116"
            android:endY="71.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFB0A1F9"/>
          <item android:offset="1" android:color="#FFEAE2FB"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
