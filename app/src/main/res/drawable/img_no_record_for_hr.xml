<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="125dp"
    android:height="115dp"
    android:viewportWidth="125"
    android:viewportHeight="115">
  <group>
    <clip-path
        android:pathData="M16.13,16.78C12.75,16.78 10,19.51 10,22.88L10,22.88L10,104.9C10,108.27 12.75,111 16.13,111L16.13,111L83.12,111C86.51,111 89.25,108.27 89.25,104.9L89.25,104.9L89.25,22.88C89.25,19.51 86.51,16.78 83.12,16.78L83.12,16.78L16.13,16.78Z"/>
    <path
        android:pathData="M16.13,16.78C12.75,16.78 10,19.51 10,22.88L10,22.88L10,104.9C10,108.27 12.75,111 16.13,111L16.13,111L83.12,111C86.51,111 89.25,108.27 89.25,104.9L89.25,104.9L89.25,22.88C89.25,19.51 86.51,16.78 83.12,16.78L83.12,16.78L16.13,16.78Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="49.62"
            android:startY="16.78"
            android:endX="49.62"
            android:endY="111"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9A1C7"/>
          <item android:offset="1" android:color="#FFFBE2ED"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M83.25,104.13L17.98,104.13C16.86,104.13 15.94,103.22 15.94,102.1L15.94,23.71C15.94,22.59 16.86,21.69 17.98,21.69L83.25,21.69C84.38,21.69 85.29,22.59 85.29,23.71L85.29,102.1C85.29,103.22 84.38,104.13 83.25,104.13"
      android:strokeWidth="1"
      android:fillColor="#FFF0F9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M48.62,5C45.24,5 42.5,7.55 42.5,10.7L42.5,10.7L42.5,12.21L33.95,12.21C30.57,12.21 27.83,14.76 27.83,17.91L27.83,17.91L27.83,17.94C27.83,21.09 30.57,23.65 33.95,23.65L33.95,23.65L63.32,23.65C66.7,23.65 69.44,21.09 69.44,17.94L69.44,17.94L69.44,17.91C69.44,14.76 66.7,12.21 63.32,12.21L63.32,12.21L54.77,12.21L54.77,10.7C54.77,7.55 52.03,5 48.65,5L48.65,5L48.62,5Z"/>
    <path
        android:pathData="M48.62,5C45.24,5 42.5,7.55 42.5,10.7L42.5,12.21L33.95,12.21C30.57,12.21 27.83,14.76 27.83,17.91L27.83,17.94C27.83,21.09 30.57,23.65 33.95,23.65L63.32,23.65C66.7,23.65 69.44,21.09 69.44,17.94L69.44,17.91C69.44,14.76 66.7,12.21 63.32,12.21L54.77,12.21L54.77,10.7C54.77,7.55 52.03,5 48.65,5L48.62,5Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="48.64"
            android:startY="15.37"
            android:endX="48.63"
            android:endY="12.45"
            android:type="linear">
          <item android:offset="0" android:color="#FFF890C0"/>
          <item android:offset="1" android:color="#FFFBE2EC"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M22.95,32.48C21.82,32.48 20.9,33.36 20.9,34.45L20.9,34.45L20.9,39.34C20.9,40.43 21.82,41.31 22.95,41.31L22.95,41.31L75.32,41.31C76.45,41.31 77.37,40.43 77.37,39.34L77.37,39.34L77.37,34.45C77.37,33.36 76.45,32.48 75.32,32.48L75.32,32.48L22.95,32.48Z"/>
    <path
        android:pathData="M22.94,32.48C21.81,32.48 20.9,33.36 20.9,34.45L20.9,39.35C20.9,40.43 21.81,41.31 22.94,41.31L75.32,41.31C76.45,41.31 77.36,40.43 77.36,39.35L77.36,34.45C77.36,33.36 76.45,32.48 75.32,32.48L22.94,32.48Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20.9"
            android:startY="36.9"
            android:endX="77.36"
            android:endY="36.9"
            android:type="linear">
          <item android:offset="0" android:color="#99F8BDD7"/>
          <item android:offset="1" android:color="#99FDCAE0"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M29.91,52.11C28.76,52.11 27.83,52.77 27.83,53.58L27.83,53.58C27.83,54.4 28.76,55.05 29.91,55.05L29.91,55.05L75.29,55.05C76.43,55.05 77.36,54.4 77.36,53.58L77.36,53.58C77.36,52.77 76.43,52.11 75.29,52.11L75.29,52.11L29.91,52.11Z"/>
    <path
        android:pathData="M29.91,52.11C28.76,52.11 27.83,52.77 27.83,53.58C27.83,54.4 28.76,55.06 29.91,55.06L75.29,55.06C76.44,55.06 77.36,54.4 77.36,53.58C77.36,52.77 76.44,52.11 75.29,52.11L29.91,52.11Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.83"
            android:startY="53.58"
            android:endX="77.06"
            android:endY="53.58"
            android:type="linear">
          <item android:offset="0" android:color="#99F8BDDB"/>
          <item android:offset="1" android:color="#99FDCAE3"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M23.87,53.58C23.87,54.4 23.2,55.06 22.38,55.06C21.56,55.06 20.9,54.4 20.9,53.58C20.9,52.77 21.56,52.11 22.38,52.11C23.2,52.11 23.87,52.77 23.87,53.58"
      android:strokeWidth="1"
      android:fillColor="#FBD2E6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M29.91,64.87C28.76,64.87 27.83,65.53 27.83,66.34L27.83,66.34C27.83,67.16 28.76,67.81 29.91,67.81L29.91,67.81L75.29,67.81C76.43,67.81 77.36,67.16 77.36,66.34L77.36,66.34C77.36,65.53 76.43,64.87 75.29,64.87L75.29,64.87L29.91,64.87Z"/>
    <path
        android:pathData="M29.91,64.87C28.76,64.87 27.83,65.53 27.83,66.34C27.83,67.16 28.76,67.81 29.91,67.81L75.29,67.81C76.44,67.81 77.36,67.16 77.36,66.34C77.36,65.53 76.44,64.87 75.29,64.87L29.91,64.87Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.83"
            android:startY="66.34"
            android:endX="77.36"
            android:endY="66.34"
            android:type="linear">
          <item android:offset="0" android:color="#99F8BDDB"/>
          <item android:offset="1" android:color="#99FDCAE3"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M23.87,66.34C23.87,67.16 23.2,67.81 22.38,67.81C21.56,67.81 20.9,67.16 20.9,66.34C20.9,65.53 21.56,64.87 22.38,64.87C23.2,64.87 23.87,65.53 23.87,66.34"
      android:strokeWidth="1"
      android:fillColor="#FBD2E6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M29.91,77.63C28.76,77.63 27.83,78.29 27.83,79.1L27.83,79.1C27.83,79.92 28.76,80.57 29.91,80.57L29.91,80.57L75.29,80.57C76.43,80.57 77.36,79.92 77.36,79.1L77.36,79.1C77.36,78.29 76.43,77.63 75.29,77.63L75.29,77.63L29.91,77.63Z"/>
    <path
        android:pathData="M29.91,77.63C28.76,77.63 27.83,78.29 27.83,79.1C27.83,79.91 28.76,80.57 29.91,80.57L75.29,80.57C76.44,80.57 77.36,79.91 77.36,79.1C77.36,78.29 76.44,77.63 75.29,77.63L29.91,77.63Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.83"
            android:startY="79.1"
            android:endX="77.36"
            android:endY="79.1"
            android:type="linear">
          <item android:offset="0" android:color="#99F8BDDB"/>
          <item android:offset="1" android:color="#99FDCAE3"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M23.87,79.1C23.87,79.92 23.2,80.57 22.38,80.57C21.56,80.57 20.9,79.92 20.9,79.1C20.9,78.29 21.56,77.63 22.38,77.63C23.2,77.63 23.87,78.29 23.87,79.1"
      android:strokeWidth="1"
      android:fillColor="#FBD2E6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M29.91,92.35C28.76,92.35 27.83,93.01 27.83,93.82L27.83,93.82C27.83,94.64 28.76,95.29 29.91,95.29L29.91,95.29L75.29,95.29C76.43,95.29 77.36,94.64 77.36,93.82L77.36,93.82C77.36,93.01 76.43,92.35 75.29,92.35L75.29,92.35L29.91,92.35Z"/>
    <path
        android:pathData="M29.91,92.35C28.76,92.35 27.83,93.01 27.83,93.82C27.83,94.64 28.76,95.3 29.91,95.3L75.29,95.3C76.44,95.3 77.36,94.64 77.36,93.82C77.36,93.01 76.44,92.35 75.29,92.35L29.91,92.35Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.83"
            android:startY="93.83"
            android:endX="77.36"
            android:endY="93.83"
            android:type="linear">
          <item android:offset="0" android:color="#99F8BDDB"/>
          <item android:offset="1" android:color="#99FDCAE3"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M23.87,93.82C23.87,94.64 23.2,95.3 22.38,95.3C21.56,95.3 20.9,94.64 20.9,93.82C20.9,93.01 21.56,92.35 22.38,92.35C23.2,92.35 23.87,93.01 23.87,93.82"
      android:strokeWidth="1"
      android:fillColor="#FBD2E6"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M102.22,41.26L58.05,84.5C58.05,84.19 57.43,84.81 57.43,85.12L57.43,85.12C57.11,85.43 56.8,86.05 56.8,86.05L56.8,86.05L53.67,98.71C53.67,98.71 53.35,99.95 53.98,100.56L53.98,100.56C54.61,101.18 55.86,101.18 55.86,101.18L55.86,101.18L68.7,98.09C68.7,98.09 69.33,97.78 69.96,97.78L69.96,97.78C70.27,97.47 70.9,97.17 70.9,97.17L70.9,97.17L115.06,53.61C115.06,53.61 116,52.69 116,52.07L116,52.07C116,51.14 115.38,50.21 115.38,50.21L115.38,50.21L105.98,40.95C105.98,40.95 105.35,40.33 104.1,40.33L104.1,40.33C102.85,40.33 102.22,41.26 102.22,41.26"/>
    <path
        android:pathData="M58.05,84.51C58.05,84.2 57.43,84.81 57.43,85.12C57.11,85.43 56.8,86.05 56.8,86.05L53.67,98.71C53.67,98.71 53.35,99.95 53.98,100.57C54.61,101.19 55.86,101.19 55.86,101.19L68.7,98.1C68.7,98.1 69.33,97.79 69.96,97.79C70.27,97.48 70.9,97.17 70.9,97.17L115.06,53.61C115.06,53.61 116,52.69 116,52.07C116,51.14 115.37,50.22 115.37,50.22L105.98,40.95C105.98,40.95 105.35,40.33 104.1,40.33C102.85,40.33 102.22,41.26 102.22,41.26L58.05,84.51Z"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="53.35"
            android:startY="70.76"
            android:endX="116"
            android:endY="70.76"
            android:type="linear">
          <item android:offset="0" android:color="#FFF9A1C7"/>
          <item android:offset="1" android:color="#FFFBE2EC"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
