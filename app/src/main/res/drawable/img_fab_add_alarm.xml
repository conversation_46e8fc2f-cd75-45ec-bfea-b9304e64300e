<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="97dp"
    android:height="97dp"
    android:viewportWidth="97"
    android:viewportHeight="97">
  <path
      android:pathData="M49,49m-46,0a46,46 0,1 1,92 0a46,46 0,1 1,-92 0"
      android:strokeWidth="1"
      android:fillColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M3,3l92,0l0,92l-92,0z"/>
    <path
        android:pathData="M74.27,56.064L55.786,56.059L55.786,74.543C55.635,78.368 52.412,81.346 48.587,81.195C44.974,81.053 42.078,78.157 41.935,74.543L41.93,56.059L23.456,56.059C19.632,55.908 16.654,52.685 16.804,48.86C16.948,45.248 19.843,42.351 23.456,42.209L41.93,42.204L41.93,23.73C41.93,19.903 45.035,16.799 48.861,16.799C52.687,16.799 55.791,19.903 55.791,23.73L55.786,42.204L74.27,42.204C78.098,42.204 81.201,45.306 81.201,49.134C81.201,52.961 78.098,56.064 74.27,56.064M49,3C23.595,3 3,23.595 3,49C3,74.405 23.595,95 49,95C74.405,95 95,74.405 95,49C95,23.595 74.405,3 49,3"
        android:strokeWidth="1"
        android:fillType="evenOdd"
        android:strokeColor="#00000000">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="49"
            android:startY="17.953"
            android:endX="49"
            android:endY="95"
            android:type="linear">
          <item android:offset="0" android:color="#FFB0DEFD"/>
          <item android:offset="1" android:color="#FF568FFC"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
