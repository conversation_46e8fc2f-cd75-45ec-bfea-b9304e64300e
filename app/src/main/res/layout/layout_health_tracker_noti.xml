<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 数据卡片区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="3">

        <!-- 心率卡片 -->
        <LinearLayout
            android:id="@+id/card_heart_rate"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/rounded_pink_bg"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_heart_rate_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="--"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_heart_rate_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" bpm"
                    android:textColor="#FFFFFF"
                    android:textSize="13sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 血压卡片 -->
        <LinearLayout
            android:id="@+id/card_blood_pressure"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/rounded_red_bg"
            android:gravity="center"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_blood_pressure_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="--/--"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_blood_pressure_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" mmHg"
                    android:textColor="#FFFFFF"
                    android:textSize="11sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 血糖卡片 -->
        <LinearLayout
            android:id="@+id/card_blood_sugar"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_margin="2dp"
            android:layout_weight="1"
            android:background="@drawable/rounded_purple_bg"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_blood_sugar_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="--"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_blood_sugar_unit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" mmol/L"
                    android:textColor="#FFFFFF"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>