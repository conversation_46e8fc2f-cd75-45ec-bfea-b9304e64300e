<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="50dp"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_noti_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="16dp"
            android:textColor="#FF081C31"
            android:textSize="15sp"
            android:textStyle="bold"
            tools:text="🎊Congratulations to......" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_noti_image"
                android:layout_width="72dp"
                android:layout_height="72dp"
                tools:src="@drawable/img_noti_fixed" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_noti_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:paddingHorizontal="4dp"
                    android:textColor="#FF081C31"
                    android:textSize="14sp"
                    tools:text="You are so lucky and...... \nlalala \nlalala" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/bg_noti_go_btn"
                    android:gravity="center"
                    android:paddingVertical="4dp"
                    android:text="@string/text_noti_cta_go"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

    <ImageView
        android:id="@+id/iv_close_noti"
        android:layout_width="13dp"
        android:layout_height="13dp"
        android:layout_gravity="top|end"
        android:layout_marginTop="8dp"
        android:src="@drawable/ic_noti_close"
        android:visibility="gone"
        tools:visibility="visible" />

</FrameLayout>