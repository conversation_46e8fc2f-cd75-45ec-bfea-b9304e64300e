<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:minHeight="50dp"
    tools:background="@color/white">

    <ImageView
        android:id="@+id/iv_noti_image"
        android:layout_width="50dp"
        android:layout_height="50dp" />

    <TextView
        android:id="@+id/tv_noti_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:layout_weight="1"
        android:textColor="#000"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="🎊Congratulations to......" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_noti_go_btn"
        android:paddingHorizontal="20dp"
        android:paddingVertical="4dp"
        android:text="@string/text_noti_cta_go"
        android:textColor="#fff"
        android:textSize="15sp"
        android:textStyle="bold" />

</LinearLayout>