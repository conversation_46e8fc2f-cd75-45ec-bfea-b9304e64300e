<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFE9FFD3">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="6dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="6dp">

        <LinearLayout
            android:id="@+id/ll_ad_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="8dp"
            android:elevation="1px"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="0dp"
                app:cardUseCompatPadding="false">

                <TextView
                    android:id="@+id/tvAd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#FF78B400"
                    android:fontFamily="@font/figtree_medium"
                    android:padding="1dp"
                    android:text=" AD "
                    android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                    android:textColor="@android:color/white"
                    android:textSize="9sp" />
            </androidx.cardview.widget.CardView>


            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:fontFamily="@font/figtree_medium"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="10sp"
                tools:text="Advertiser" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <FrameLayout
                android:id="@+id/ad_media"
                android:layout_width="178dp"
                android:layout_height="110dp"
                tools:background="@color/cardview_dark_background" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_ad_content_action"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="4dp">

                <androidx.cardview.widget.CardView
                    android:id="@+id/mcv_icon_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    app:cardUseCompatPadding="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/ad_app_icon"
                        android:layout_width="34dp"
                        android:layout_height="34dp"

                        tools:src="@drawable/launcher_icon" />

                </androidx.cardview.widget.CardView>


                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/figtree_medium"
                    android:gravity="center_vertical"
                    android:maxLines="2"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/mcv_icon_image_view"
                    app:layout_constraintTop_toTopOf="@+id/mcv_icon_image_view"
                    tools:text="Title" />

                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="1dp"
                    android:layout_marginBottom="2dp"
                    android:ellipsize="end"
                    android:fontFamily="@font/figtree_medium"
                    android:lineSpacingExtra="-2sp"
                    android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                    android:textSize="11sp"
                    app:layout_constraintBottom_toTopOf="@+id/card_ad_call_to_action_container"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/ad_headline"
                    app:layout_constraintTop_toBottomOf="@id/ad_headline"
                    tools:text="BodyBodyBodyBodyBodyBodyBodyBody" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/card_ad_call_to_action_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="3dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent">

                    <Button
                        android:id="@+id/ad_call_to_action"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:background="#FFA8E36E"
                        android:elevation="0dp"
                        android:fontFamily="@font/figtree_bold"
                        android:minWidth="80dp"
                        android:textColor="#FFF"
                        android:textSize="13dp"
                        tools:text="Click" />
                </androidx.cardview.widget.CardView>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </FrameLayout>

    <FrameLayout
        android:id="@+id/ad_options"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_gravity="top|end"
        android:orientation="horizontal" />
</FrameLayout>