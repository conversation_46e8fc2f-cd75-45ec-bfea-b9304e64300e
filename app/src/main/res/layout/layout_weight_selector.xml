<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.zjun.widget.RuleView
            android:id="@+id/rvWeight"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:gv_longGradationLen="54dp"
            app:gv_longLineWidth="3dp"
            app:gv_shortGradationLen="40dp"
            app:gv_shortLineWidth="2dp"
            app:zjun_bgColor="#00000000"
            app:zjun_gradationColor="#FF43435E"
            app:zjun_indicatorLineColor="#00000000"
            app:zjun_textColor="#00000000" />

        <View
            android:layout_width="96dp"
            android:layout_height="55dp"
            android:layout_gravity="start"
            android:background="@drawable/bg_fade_white_to_alpha" />

        <View
            android:layout_width="32dp"
            android:layout_height="55dp"
            android:layout_gravity="start"
            android:background="@drawable/bg_fade_white_to_alpha" />

        <View
            android:layout_width="96dp"
            android:layout_height="55dp"
            android:layout_gravity="end"
            android:background="@drawable/bg_fade_white_to_alpha"
            android:rotation="180" />

        <View
            android:layout_width="96dp"
            android:layout_height="55dp"
            android:layout_gravity="end"
            android:background="@drawable/bg_fade_white_to_alpha"
            android:rotation="180" />
    </FrameLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center|top"
        android:orientation="vertical">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="82dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_weight_cursor" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="6dp">

            <TextView
                android:id="@+id/tvLb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:textColor="#FF43435E"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="488.8" />

            <TextView
                android:id="@+id/tvUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/figtree_bold"
                android:textSize="15sp"
                android:textStyle="bold"
                tools:text="lbs" />

        </LinearLayout>


    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_gravity="top"
        android:background="@color/white" />

</FrameLayout>