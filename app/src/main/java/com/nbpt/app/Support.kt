package com.nbpt.app

import java.util.Locale


@Suppress("MemberVisibilityCanBePrivate", "HasPlatformType")
object Support {
    val En = Locale.ENGLISH
    val Ar = Locale("ar")
    val De = Locale.GERMAN
    val Fr = Locale.FRENCH
    val Ko = Locale.KOREAN
    val Ms = Locale("ms")
    val Pt = Locale("pt")
    val Ja = Locale.JAPANESE
    val Zh_TW = Locale.TAIWAN
    val Th = Locale("th")
    val Tr = Locale("tr")
    val Es = Locale("es")
    val Zh_HK = Locale("zh", "HK")
    val It = Locale.ITALIAN
    val In = Locale("in")
    val Vi = Locale("vi")

    val localeList = listOf<Locale>(
        En,
        Ar,
        De,
        Fr,
        Ko,
        Ms,
        Pt,
        Ja,
        Th,
        Tr,
        Es,
        It,
        In,
        Vi,
        Zh_HK,
        Zh_TW,
    )

    val languageMatchList: List<String>
        get() {
            val matches = mutableListOf<String>()

            localeList.forEach {
                if (it.language.isNotEmpty() && it.country.isEmpty()) {
                    matches.add(it.language)
                }
            }

            return matches
        }

    fun compatibleLanguage(locale: Locale): Locale? {
        return if (locale.language in languageMatchList) {
            Locale(locale.language)
        } else if (locale in localeList) {
            locale
        } else {
            null
        }
    }
}
