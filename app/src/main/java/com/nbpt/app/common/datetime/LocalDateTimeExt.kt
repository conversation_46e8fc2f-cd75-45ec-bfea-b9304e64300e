package com.nbpt.app.common.datetime

import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.LocalTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toJavaLocalTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.time.format.TextStyle
import java.util.Locale

fun LocalDateTime.toInstant(tz: TimeZone = TimeZone.currentSystemDefault()) = this.toInstant(tz)

fun LocalDate.format(
    style: TextStyle = TextStyle.SHORT,
    locale: Locale = Locale.getDefault()
): String {
    val dayOfMonth = this.dayOfMonth
    val month = this.month.getDisplayName(style, locale)

    return "$dayOfMonth $month"
}

fun LocalDate.isoFormat(style: FormatStyle = FormatStyle.MEDIUM): String {
    return this.toJavaLocalDate().format(DateTimeFormatter.ofLocalizedDate(style))
}

fun LocalTime.isoFormat(): String {
    return this.toJavaLocalTime().format(DateTimeFormatter.ofLocalizedTime(FormatStyle.SHORT))
}
