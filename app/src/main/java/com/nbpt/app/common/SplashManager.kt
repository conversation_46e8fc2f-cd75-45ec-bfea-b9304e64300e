package com.nbpt.app.common

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import org.koin.core.context.GlobalContext

class SplashManager {
    private val _skipSplash = MutableStateFlow(false)
    val skipSplashFlow: StateFlow<Boolean> get() = _skipSplash

    fun doSkipSplash(doSkip: Boolean) {
        _skipSplash.update { doSkip }
    }
}

fun skipSplash() {
    GlobalContext.get().get<SplashManager>().doSkipSplash(true)
}
