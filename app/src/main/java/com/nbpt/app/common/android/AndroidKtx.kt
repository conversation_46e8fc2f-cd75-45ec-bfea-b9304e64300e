package com.nbpt.app.common.android

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.LocaleList
import android.os.PowerManager
import android.provider.Settings
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.ShareCompat
import androidx.core.content.FileProvider
import androidx.core.os.LocaleListCompat
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.logger.debugLog
import java.io.File
import java.util.Locale

data class PendingSharedFile(
    val file: File,
    val fileMimeType: String,
    val shareSubject: String,
    val shareTitle: String,
)

fun Context.shareFile(sharedFile: PendingSharedFile) {
    val fileUri = FileProvider.getUriForFile(this, "${this.packageName}.provider", sharedFile.file)

//    val shareIntent = Intent().apply {
//        action = Intent.ACTION_SEND
//        putExtra(Intent.EXTRA_TEXT, "Share!!!")
//        putExtra(Intent.EXTRA_STREAM, fileUri)
//        type = "text/csv"
//        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//    }
//
//    startActivity(Intent.createChooser(shareIntent, "Share file"))

    val shareIntent = ShareCompat.IntentBuilder(this)
        .setStream(fileUri)
        .setType(sharedFile.fileMimeType)
        .setChooserTitle(sharedFile.shareTitle)
        .setSubject(sharedFile.shareSubject)
        .createChooserIntent()
        .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

    startActivity(shareIntent)
}

fun Context.findActivity(): Activity {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) return context
        context = context.baseContext
    }
    throw IllegalStateException("no activity")
}

fun Activity.changeLocale(locale: Locale, onLocaleChanged: (changeSuccessful: Boolean) -> Unit) {
    if (locale != Locale.getDefault()) {
        runOnUiThread {
            AppCompatDelegate.setApplicationLocales(LocaleListCompat.forLanguageTags(locale.toLanguageTag()))
            onLocaleChanged(true)
        }
    } else {
        onLocaleChanged(false)
    }
}

fun Activity.resetLocal() {
    runOnUiThread {
        AppCompatDelegate.setApplicationLocales(
            LocaleListCompat.getEmptyLocaleList()
        )
    }
}

const val REQUEST_IGNORE_BATTERY_OPTIMIZATIONS_SETTINGS = 0x9000
@RequiresApi(Build.VERSION_CODES.M)
fun Activity.openBatteryOptimizationSettings(): Boolean {
    val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager?
    val hasIgnoringBatteryOptimizations = powerManager?.isIgnoringBatteryOptimizations(packageName)

    val needToOpenBatteryOptimizationSettings = hasIgnoringBatteryOptimizations == false

    runOnUiThread {
        if (needToOpenBatteryOptimizationSettings) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:$packageName")
            startActivityForResult(intent, REQUEST_IGNORE_BATTERY_OPTIMIZATIONS_SETTINGS)
        }
    }

    return needToOpenBatteryOptimizationSettings
}
