package com.nbpt.app.common.datatransform

import android.content.Context
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.data.db.model.BpRecordEntity
import com.github.doyaaaaaken.kotlincsv.dsl.csvWriter
import com.nbpt.app.common.calculate.mmolLToMgDL
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.adt.HrStatus
import com.nbpt.app.data.db.model.BsRecordEntity
import com.nbpt.app.data.db.model.HrRecordEntity
import com.nbpt.app.ui.screen.heartrateeditor.Gender
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.math.RoundingMode

class RecordsToCvsTransformer {

    private val csvWriter = csvWriter {
//        delimiter = '\t'
    }

    suspend fun transformBp(
        records: List<BpRecordEntity>,
        context: Context,
        fileName: String
    ) = withContext(Dispatchers.IO) {
        val file = File(context.cacheDir, "$fileName.csv")

        val titles =
            listOf(
                "Date & Time",
                "Blood Pressure Type",
                "Systolic/mmHg",
                "Diastolic/mmHg",
                "Pulse/BPM",
                "Notes"
            )

        csvWriter.open(file) {
            writeRow(titles)

            records.forEach { record ->
                val dateTime = record.instant.toLocalDatetime()
                val date = dateTime.date.isoFormat()
                val time = dateTime.time.isoFormat()

                val dateAndTime = "$date $time"
                val bpType = context.getString(
                    BpStatus.from(
                        record.systolic,
                        record.diastolic
                    ).titleStringId
                )
                val systolic = record.systolic.toString()
                val diastolic = record.diastolic.toString()
                val pulse = record.pulse.toString()
                val notes = record.notes.toJsonString()?.trim('"') ?: ""

                val values = listOf(dateAndTime, bpType, systolic, diastolic, pulse, notes)
                writeRow(values)
            }
        }

        file
    }

    suspend fun transformHr(
        records: List<HrRecordEntity>,
        context: Context,
        fileName: String
    ) = withContext(Dispatchers.IO) {
        val file = File(context.cacheDir, "$fileName.csv")

        val titles =
            listOf(
                "Date & Time",
                "Heart Rate Type",
                "Heart Rate / BPM",
                "Gender",
                "Age",
                "Notes"
            )

        csvWriter.open(file) {
            writeRow(titles)

            records.forEach { record ->
                val dateTime = record.instant.toLocalDatetime()
                val date = dateTime.date.isoFormat()
                val time = dateTime.time.isoFormat()

                val dateAndTime = "$date $time"
                val hrBpm = record.heartRateBpm
                val hrType = context.getString(HrStatus.from(hrBpm).titleStringId)
                val gender = context.getString(Gender.numberOf(record.gender).textStringId)
                val age = record.age.toString()
                val notes = record.notes.toJsonString()?.trim('"') ?: ""

                val values = listOf(dateAndTime, hrType, hrBpm, gender, age, notes)
                writeRow(values)
            }
        }

        file
    }

    suspend fun transformBs(
        bsUnit: BsUnit,
        records: List<BsRecordEntity>,
        context: Context,
        fileName: String
    ) = withContext(Dispatchers.IO) {
        val file = File(context.cacheDir, "$fileName.csv")

        val titles =
            listOf(
                "Date & Time",
                "Blood Sugar Type",
                "Blood Sugar (${bsUnit.text})",
                "State",
                "Notes"
            )

        csvWriter.open(file) {
            writeRow(titles)

            records.forEach { record ->
                val dateTime = record.instant.toLocalDatetime()
                val date = dateTime.date.isoFormat()
                val time = dateTime.time.isoFormat()

                val dateAndTime = "$date $time"

                val (status, bsValue) = when (bsUnit) {
                    BsUnit.SI -> {
                        BsStatus.fromMmolL(record.mmolL) to record.mmolL
                    }

                    BsUnit.NonSI -> {
                        val mgDl = mmolLToMgDL(record.mmolL)

                        BsStatus.fromMgDl(mgDl) to mgDl
                    }
                }

                val bsType = context.getString(status.titleStringId)
                val state = context.getString(record.state.textId)
                val notes = record.notes.toJsonString()?.trim('"') ?: ""

                val values = listOf(
                    dateAndTime,
                    bsType,
                    bsValue.scale(1, RoundingMode.DOWN),
                    state,
                    notes
                )

                writeRow(values)
            }
        }

        file
    }
}
