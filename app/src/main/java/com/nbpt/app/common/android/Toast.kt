package com.nbpt.app.common.android

import android.content.Context
import android.os.Build
import android.widget.Toast
import androidx.core.text.HtmlCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

fun Context.showToast(text: String?) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
        showToastApi30(text)
    } else {
        Toast.makeText(this, text, Toast.LENGTH_SHORT).show()
    }
}

private fun Context.showToastApi30(text: String?) {
    GlobalScope.launch(Dispatchers.Main) {
        Toast.makeText(
            this@showToastApi30,
            HtmlCompat.fromHtml(
                "<font color='#FF303030'>$text</font>",
                HtmlCompat.FROM_HTML_MODE_LEGACY
            ),
            Toast.LENGTH_SHORT
        ).show()
    }
}
