package com.nbpt.app.common.moshi

import com.squareup.moshi.JsonDataException
import com.squareup.moshi.Moshi
import com.squareup.moshi.adapter
import kotlin.reflect.KType
import kotlin.reflect.typeOf


val moshi: Moshi = Moshi.Builder()
//    .add(ArrayListMoshiAdapter())
    .build()

@OptIn(ExperimentalStdlibApi::class)
fun <T> fromJson(json: String, kType: KType): T? {
    return try {
        val adapter = moshi.adapter<T>(kType)
        adapter.fromJson(json)
    } catch (e: Exception) {
        val wrappedException = JsonDataException("Failed to parse JSON: $json", e)
        wrappedException.printStackTrace()
        null
    }
}

inline fun <reified T> String.toObj(): T? {
    return fromJson(this, typeOf<T>())
}

inline fun <reified T> T?.toJsonString(): String? {
    val typeAdapter = moshi.adapter(T::class.java)

    return typeAdapter.toJson(this)
}
