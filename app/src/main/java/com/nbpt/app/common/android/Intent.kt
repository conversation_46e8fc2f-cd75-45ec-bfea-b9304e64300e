package com.nbpt.app.common.android

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.nbpt.app.BuildConfig
import com.nbpt.app.biz.analytics.logEventRecord
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.logger.debugLog
//import com.google.firebase.crashlytics.ktx.crashlytics
//import com.google.firebase.ktx.Firebase

const val REQUEST_POST_NOTIFICATION_SETTINGS = 0x9001
@RequiresApi(Build.VERSION_CODES.O)
fun Activity.openNotificationSettings() {
    try {
        startActivityForResult(
            Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(
                    Settings.EXTRA_APP_PACKAGE,
                    <EMAIL>
                )
            },
            REQUEST_POST_NOTIFICATION_SETTINGS
        )
        logEventRecord("notification_permission_request")
        debugLog { "notification_permission_request" }
    } catch (e: Exception) {
        Firebase.crashlytics.recordException(e)
        e.printStackTrace()
    }
}

fun Context.openBrowser(url: String) {
    runCatching {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        startActivity(
            Intent.createChooser(browserIntent, "")
        )
    }
}

fun Context.openToMarket() {
    openBrowser("")
}

fun Context.openFeedbackMailto(
    email: String,
    subject: String? = null,
    metadata: String? = null
) {
    runCatching {
        startActivity(createFeedbackMailtoIntent(email, subject, metadata))
    }
}

fun Context.openFeedbackDef() {
    openFeedbackMailto(
        email = BuildConfig.feedbackEmail,
        subject = "Feedback",
        metadata = "Please let us know what we can do better.\n"
    )
}

private fun createFeedbackMailtoIntent(
    email: String,
    subject: String?,
    metadata: String?
): Intent {
    return Intent(Intent.ACTION_SEND)
        .putExtra(
            Intent.EXTRA_EMAIL,
            arrayOf(email)
        )
        .putExtra(
            Intent.EXTRA_SUBJECT,
            subject
        )
        .putExtra(
            Intent.EXTRA_TEXT,
            metadata
        )
        .apply {
            selector = Intent(Intent.ACTION_SENDTO).setData(Uri.parse("mailto:"))
        }
}
