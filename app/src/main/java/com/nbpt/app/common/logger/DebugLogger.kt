@file:Suppress("ObjectPropertyName")

package com.nbpt.app.common.logger

import co.touchlab.kermit.Logger
import co.touchlab.kermit.loggerConfigInit
import co.touchlab.kermit.platformLogWriter
import com.nbpt.app.BuildConfig

private val _debugLog = if (BuildConfig.DEBUG) Logger(
    loggerConfigInit(platformLogWriter())
) else null

fun debugLog(throwable: Throwable? = null, tag: String = "debug_log", message: () -> String) {
    _debugLog?.withTag(tag)?.d(throwable = throwable, message = message)
}
