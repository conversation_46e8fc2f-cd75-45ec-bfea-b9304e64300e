package com.nbpt.app.common.datetime

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime

fun nowInstant() = Clock.System.now()

fun Instant.toLocalDatetime(tz: TimeZone = TimeZone.currentSystemDefault()) = this.toLocalDateTime(tz)

fun Instant.todayStartInstant(timeZone: TimeZone = TimeZone.currentSystemDefault()): Instant {
    return this.todayStartDateTime(timeZone).toInstant(timeZone)
}

fun Instant.todayStartDateTime(timeZone: TimeZone): LocalDateTime {
    return this.toLocalDateTime(timeZone).let {
        LocalDateTime(it.year, it.month, it.dayOfMonth, 0, 0, 0)
    }
}
