package com.nbpt.app

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.material.navigation.ModalBottomSheetLayout
import androidx.compose.material.navigation.rememberBottomSheetNavigator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewModelScope
import androidx.navigation.compose.rememberNavController
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import com.nbpt.app.bi.BiReporter
import com.nbpt.app.bi.reportPageOnStartEvent
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.screen.appDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
import com.nbpt.app.ui.screen.navDestination
import com.nbpt.app.ui.theme.AppTheme
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.nbpt.app.androidcomponent.PermissionRequestManager
import com.nbpt.app.ui.screen.destinations.GuideDestination
import com.ramcosta.composedestinations.utils.currentDestinationFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.compose.koinInject


@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter", "RestrictedApi")
@Composable
internal fun Host() {
    val context = LocalContext.current

    val bottomSheetNavigator = rememberBottomSheetNavigator()
    val navController = rememberNavController(bottomSheetNavigator)

    Scaffold {
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            ModalBottomSheetLayout(
                bottomSheetNavigator = bottomSheetNavigator,
                sheetBackgroundColor = Color.Transparent,
                sheetElevation = 0.dp,
                sheetShape = RectangleShape,
            ) {
                AppNavigation(
                    navController = navController,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }

    val splashManager: SplashManager = koinInject()
    val scope = rememberCoroutineScope()
    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> {
                scope.launch {
                    val currentDestination = navController.currentDestinationFlow.first()

                    Log.d("HostContainer", "currentDestination: ${currentDestination.baseRoute}")

                    val skipSplash = splashManager.skipSplashFlow.first()

                    if (currentDestination !is SplashDestination && !skipSplash) {
                        navController.navigate(
                            SplashDestination(
                                isColdLaunch = false,
                                previousDestinationWasHome = currentDestination is HomeDestination
                            ).route
                        )
                    } else if (skipSplash) {
                        splashManager.doSkipSplash(false)
                    }
                }
            }

            else -> {}
        }
    }

    LaunchedEffect(Unit) {
        GlobalNavigator.configure(navController = navController, lifecycleScope = this)
    }

    val biReporter: BiReporter = koinInject()
    LaunchedEffect(Unit) {
        navController.currentDestinationFlow.onEach {
            biReporter.reportPageOnStartEvent(it.baseRoute)
        }.launchIn(this)
    }

//    val permissionRequestManager: PermissionRequestManager = koinInject()
//    LaunchedEffect(Unit) {
//        permissionRequestManager.registerRequesterIfNeeded(
//            activity = context.findActivity(),
//            navController = navController,
//            lifecycleScope = this
//        )
//    }

}
