package com.nbpt.app.biz.heartrate

import android.content.Context
import android.graphics.Bitmap
import android.util.Size
import androidx.camera.view.PreviewView
import androidx.compose.runtime.Composable
import androidx.core.graphics.get
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.withResumed
import com.nbpt.app.common.logger.debugLog
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.rememberPermissionState
import com.kylecorry.andromeda.camera.Camera
import com.kylecorry.andromeda.core.bitmap.BitmapUtils.toBitmap
import com.kylecorry.sol.math.filters.MovingAverageFilter
import com.kylecorry.sol.math.optimization.DispersionExtremaFinder
import com.kylecorry.sol.units.Reading
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import java.time.Duration
import java.time.Instant
import kotlin.math.roundToInt

private const val TAG = "HeartRateMonitor"

class HeartRateMonitor {
    companion object {
        const val HEART_RATE_MEASUREMENT_DURATION_SECONDS: Int = 20
        const val HEART_RATE_PROGRESS_PER_STEP: Float = 1f / HEART_RATE_MEASUREMENT_DURATION_SECONDS
    }

    private var _camera: Camera? = null

    @Volatile
    private var isCameraStarted = false

    @OptIn(ExperimentalCoroutinesApi::class, DelicateCoroutinesApi::class)
    private val cameraDispatcher = newSingleThreadContext("CameraDispatcher")


    private val readings = mutableListOf<Reading<Float>>()
    private val filter = MovingAverageFilter(4)

    private var heartRateMeasureJob: Job? = null

    val measureStateFlow: MutableStateFlow<HeartRateMeasureState> =
        MutableStateFlow(HeartRateMeasureState.Stop)

    @OptIn(ExperimentalPermissionsApi::class)
    @Composable
    fun checkCameraPermissions(): PermissionState =
        rememberPermissionState(android.Manifest.permission.CAMERA)

    fun flashSwitch(use: Boolean) {
        _camera?.setTorch(use)

        if (use) {
            _camera?.setExposure(0)
        } else {
            _camera?.setExposure(2)
        }
    }

    fun prepare(
        context: Context,
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView,
    ) {
        measureStateFlow.update { HeartRateMeasureState.Stop }
        _camera = null
        _camera = Camera(
            context = context,
            lifecycleOwner = lifecycleOwner,
            previewView = previewView,
            targetResolution = Size(160, 200)
        )
        isCameraStarted = false
    }

    fun interrupt() {
        isCameraStarted = false
        measureStateFlow.update { HeartRateMeasureState.Stop }

        _camera?.apply {
            stop(null)
            setTorch(false)
        }

        heartRateMeasureJob?.cancel()
        heartRateMeasureJob = null
    }

    @androidx.annotation.OptIn(androidx.camera.core.ExperimentalGetImage::class)
    fun launch(lifecycleOwner: LifecycleOwner) {
        readings.clear()
        heartRateMeasureJob?.cancel()
        heartRateMeasureJob = null

        if (!isCameraStarted) {
            isCameraStarted = true
            measureStateFlow.update { HeartRateMeasureState.TryingToMeasure }
        }
        _camera?.start {
            lifecycleOwner.lifecycleScope.launch(cameraDispatcher) {
                withContext(Dispatchers.Default) {
                    process(_camera?.image?.image?.toBitmap())
                }
                _camera?.image?.close()
            }

            true
        }
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.withResumed {
                _camera?.setExposure(2)
            }
        }
    }

    private suspend fun finish(bpm: Int?) = withContext(Dispatchers.Main.immediate) {
        isCameraStarted = false
        measureStateFlow.update { HeartRateMeasureState.Finish(bpm) }

        _camera?.apply {
            stop(null)
            setTorch(false)
        }

        heartRateMeasureJob?.cancel()
        heartRateMeasureJob = null
    }

    private fun process(image: Bitmap?) {
        image ?: return
        var r = 0f
        var g = 0f

        val total = image.width * image.height.toFloat()
        for (x in 0 until image.width) {
            for (y in 0 until image.height) {
                r += image[x, y].red / total
                g += image[x, y].green / total
            }
        }

        debugLog(tag = TAG) { "process() r:$r g:$g" }
        debugLog(tag = TAG) { "process() readings.size:${readings.size}" }

        if (r in 100f..250f && g < 60) {
            val now = Instant.now()
//            val maxTime = now.minus(heartRateMeasurementDuration)
            val filtered = filter.filter(r)
//            readings.removeIf { it.time < maxTime }
            readings.add(Reading(filtered, now))
            calculateHeartRateMeasureState()
        } else {
            readings.clear()
            heartRateMeasureJob?.cancel()
            heartRateMeasureJob = null
            measureStateFlow.update { HeartRateMeasureState.TryingToMeasure }
        }

        image.recycle()
    }

    private fun calculateHeartRateBpm(beats: List<Reading<Float>> = findPeaks(readings)): Int? {
        val durations = mutableListOf<Duration>()
        for (i in 1 until beats.size) {
            durations.add(Duration.between(beats[i - 1].time, beats[i].time))
        }

        debugLog(tag = TAG) { "findPeaks durations.size:${durations.size}" }

        val averageDuration = 1 / (durations.map { it.toMillis() }.average() / 1000 / 60)

        val bpm = if (!averageDuration.isNaN()) {
            averageDuration.roundToInt()
        } else {
            null
        }

        return bpm
    }

    private fun calculateHeartRateMeasureState() {
        if (measureStateFlow.value is HeartRateMeasureState.Finish) {
            heartRateMeasureJob?.cancel()
            heartRateMeasureJob = null

            return
        }

        if (measureStateFlow.value is HeartRateMeasureState.Measuring) return

        val dt = Duration.between(readings.first().time, readings.last().time)

        if (dt < Duration.ofSeconds(1) && readings.size > 3) {
            return
        }

        val beats = findPeaks(readings)
//        val bpm = calculateHeartRateBpm(beats)

        if (measureStateFlow.value is HeartRateMeasureState.Stop && beats.size < 4) {
            measureStateFlow.update { HeartRateMeasureState.TryingToMeasure }
        } else if (measureStateFlow.value is HeartRateMeasureState.TryingToMeasure && beats.size >= 2) {
            readings.clear()

            measureStateFlow.update { HeartRateMeasureState.Measuring(null, 0f) }

            heartRateMeasureJob = GlobalScope.launch(Dispatchers.Default) {
                repeat(HEART_RATE_MEASUREMENT_DURATION_SECONDS) { countDown ->
                    val _bpm = calculateHeartRateBpm()
                    val _progress =
                        countDown.toFloat() / HEART_RATE_MEASUREMENT_DURATION_SECONDS
                    measureStateFlow.value =
                        HeartRateMeasureState.Measuring(_bpm, _progress).apply {
                            debugLog(tag = TAG) { this.toString() }
                        }
                    delay(1000)
                }

                val finallyBpm = calculateHeartRateBpm(beats = findPeaks(readings))
                finish(bpm = finallyBpm)

//                launch {
//                    delay(heartRateMeasurementDurationSeconds * 1000L)
//                    finish(calculateHeartRateBpm())
//                }
            }
        } /*else if (measureStateFlow.value is HeartRateMeasureState.Measuring) {
            debugLog(tag = TAG) { HeartRateMeasureState.Measuring(bpm).toString() }
            measureStateFlow.value = HeartRateMeasureState.Measuring(bpm)
        }*/

    }

    @Suppress("LocalVariableName")
    private fun findPeaks(values: List<Reading<Float>>): List<Reading<Float>> {
//        val lag = 5
//        val influence = 0.2f
//        val threshold = 1.5f
        val lag = 2
        val influence = 1.2f
        val threshold = 0.5f

        val _values = ArrayList(values)
        debugLog(tag = TAG) { "findPeaks(values) _values.size: ${_values.size}" }

        return DispersionExtremaFinder(lag, threshold, influence).find(_values.map { it.value })
            .filter { it.isHigh }
            .map { _values[it.point.x.toInt()] }
    }
}
