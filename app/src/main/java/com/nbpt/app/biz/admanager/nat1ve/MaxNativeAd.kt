package com.nbpt.app.biz.admanager.nat1ve

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.children
import androidx.lifecycle.Lifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import org.koin.compose.koinInject

@Composable
fun MaxNativeAd(
    place: NativeAdPlaceholder,
    modifier: Modifier = Modifier,
) {
    val localContext = LocalContext.current

    val nativeAdManager: MaxNativeAdManager = koinInject()

    val nativeAd by nativeAdManager.adFlow(place).collectAsState()

    debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

    var adContainer by remember { mutableStateOf<FrameLayout?>(null) }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_DESTROY -> {
                debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

                nativeAdManager.destroy(place)
            }

            else -> {}
        }
    }

    LaunchedEffect(nativeAd?.adView) {
        nativeAd?.adView?.let {
            runCatching {
                (it.parent as? ViewGroup)?.removeAllViews()
                adContainer?.removeAllViews()

                if (nativeAdManager.needToBlockSomeClickAreas(nativeAd?.ad ?: return@let)) {
                    it.titleTextView.isClickable = false
                    it.bodyTextView.isClickable = false
                    it.advertiserTextView.isClickable = false
                    it.iconImageView.isClickable = false
                    it.mediaContentViewGroup.isEnabled = false
                    it.postDelayed({
                        it.mediaContentViewGroup.children.forEach {
                            it.isEnabled = false
                        }
                    }, 500)
                }

                adContainer?.addView(it)
            }
        }
    }

    LaunchedEffect(Unit) {
        logEventRecord("ad_native_show")

        if (nativeAd == null) {
            debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
            nativeAdManager.buildAd(place, localContext, useNovelty = false)
        }
    }

    Crossfade(
        targetState = nativeAd != null,
        modifier = modifier.apply {
            if (nativeAd == null) animateContentSize()
        },
        label = "",
    ) { hasNativeAd ->
        if (hasNativeAd) {
            Spacer(modifier = Modifier.height(126.dp))

            Surface(shape = RoundedCornerShape12Dp, modifier = Modifier.defShadow()) {
                AndroidView(
                    factory = { context ->
                        FrameLayout(context).apply {
                            layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.WRAP_CONTENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )
                        }.apply {
                            adContainer = this
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        } else {
            NativeAdPlaceholder(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(126.dp)
            )
        }
    }
}


@Composable
private fun NativeAdPlaceholder(
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(
        spec = LottieCompositionSpec.Asset("ad_native_g.json")
    )

    LottieAnimation(
        composition = composition,
        iterations = Int.MAX_VALUE,
        modifier = modifier,
        contentScale = ContentScale.FillBounds
    )
}

@Preview
@Composable
private fun NativeAdPreview() {
    NativeAd(place = NativeAdPlaceholder.Test)
}
