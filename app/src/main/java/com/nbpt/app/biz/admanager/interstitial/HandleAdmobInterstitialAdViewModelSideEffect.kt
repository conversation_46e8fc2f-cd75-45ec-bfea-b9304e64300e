package com.nbpt.app.biz.admanager.interstitial

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import com.nbpt.app.GlobalNavigateBlock
import com.nbpt.app.GlobalNavigator
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.orbitmvi.orbit.compose.collectSideEffect

typealias NavigateAction = DestinationsNavigator.() -> Unit

@Composable
fun AdmobInterstitialAdViewModel.RegisterHandleSideEffect(
    navigator: DestinationsNavigator,
    onNavTo: (NavigateAction) -> Unit = { it(navigator) },
    onNavUp: () -> Unit = { navigator.popBackStack() }
) {
    collectSideEffect {
        when (it) {
            is AdmobInterstitialAdSideEffect.NavTo -> {
                onNavTo(it.navAction)
            }

            AdmobInterstitialAdSideEffect.NavUp -> {
                onNavUp()
            }
        }
    }
}