package com.nbpt.app.biz.admanager.banner

import android.content.Context
import android.view.View
import android.view.View.OnAttachStateChangeListener
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import com.applovin.mediation.*
import com.applovin.mediation.ads.MaxAdView
import com.nbpt.app.BuildConfig
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.collections.getOrPut
import kotlin.collections.mutableMapOf


private const val TAG = "MaxBannerAdManager"

class MaxBannerAdListener(
    private val placeholder: BannerAdPlaceholder,
    private val hasCacheFlow: MutableStateFlow<Boolean>,
    private val hasPreloadButNoDisplayFlow: MutableStateFlow<Boolean>,
    private val lastImpressInstantFlow: MutableStateFlow<Instant?>,
    private val adPlaceNameFlow: StateFlow<String?>,
    private val splashManager: SplashManager,
) : MaxAdViewAdListener,
    MaxAdRevenueListener {

    private var adViewAttachedToWindow: Boolean = false


    override fun onAdLoaded(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdLoaded" }

        hasCacheFlow.update { true }

        bannerAdMap[placeholder]?.let { bannerAd ->
            if (!ViewCompat.isAttachedToWindow(bannerAd.adView)) {
                adViewAttachedToWindow = false

                debugLog(tag = TAG) { "$placeholder not attached to window" }
                bannerAd.stopAutoRefresh()
            } else {
                adViewAttachedToWindow = true

                debugLog(tag = TAG) { "$placeholder is attached to window" }
            }
        }
    }

    override fun onAdDisplayed(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdDisplayed" }

        GlobalScope.launch(Dispatchers.Main) {
            adPlaceNameFlow.first()?.let { adPlaceName ->
                debugLog(tag = TAG) { "$placeholder ad_${adPlaceName}_banner_impress" }
                logEventRecord("ad_${adPlaceName}_banner_impress")
                logEventRecord("ad_banner_impress")

                lastImpressInstantFlow.update { nowInstant() }
            }

            debugLog(tag = TAG) { "adViewAttachedToWindow -> $adViewAttachedToWindow" }
            if (!adViewAttachedToWindow) {
                hasPreloadButNoDisplayFlow.update { true }
            }
        }
    }

    override fun onAdHidden(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdHidden" }
    }

    override fun onAdClicked(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdClicked" }

        splashManager.doSkipSplash(true)

        GlobalScope.launch(Dispatchers.Main) {
            val adPlaceName = adPlaceNameFlow.first() ?: return@launch
            debugLog(tag = TAG) { "$placeholder $adPlaceName onAdClicked" }

            logEventRecord("ad_${adPlaceName}_banner_click")
            logEventRecord("ad_banner_click")
        }
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
        debugLog(tag = TAG) { "$placeholder onAdLoadFailed" }
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
        debugLog(tag = TAG) { "$placeholder onAdDisplayFailed" }
    }

    override fun onAdExpanded(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdExpanded" }
    }

    override fun onAdCollapsed(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdCollapsed" }
    }

    override fun onAdRevenuePaid(ad: MaxAd) {
        debugLog(tag = TAG) { "$placeholder onAdRevenuePaid" }

        if (!BuildConfig.DEBUG) {
            GlobalScope.launch {
                val adPlaceName = adPlaceNameFlow.first()

                AnalyticsLogEvent.recordAdImpressionRevenue(
                    ad,
                    adPlaceName ?: placeholder.placeName
                )
                AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(ad)
                AnalyticsLogEvent.recordAdImpression(ad)

                AnalyticsLogEvent.roasReport(ad)
            }
        }
    }
}

private val bannerAdMap = mutableMapOf<BannerAdPlaceholder, MaxBannerAd>()

open class MaxBannerAd(
    context: Context,
    private val placeholder: BannerAdPlaceholder,
) : KoinComponent {
    private val splashManager: SplashManager by inject()
    private val remoteConfig: RealRemoteConfig by inject()

    private val adPlaceNameFlow = MutableStateFlow<String?>(null)

    val hasCacheFlow = MutableStateFlow(false)

    private val hasPreloadButNoDisplayFlow = MutableStateFlow(false)

    private val lastImpressInstantFlow = MutableStateFlow<Instant?>(null)

    private val adListener =
        MaxBannerAdListener(
            placeholder,
            hasCacheFlow,
            hasPreloadButNoDisplayFlow,
            lastImpressInstantFlow,
            adPlaceNameFlow,
            splashManager
        )

    var adView: MaxAdView =
        MaxAdView(remoteConfig.adConfig1.bannerAdKey, MaxAdFormat.BANNER, context).apply {
            debugLog(tag = TAG) { "create banner for $placeholder" }
            setListener(adListener)
            setRevenueListener(adListener)

            addOnAttachStateChangeListener(object : OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                    GlobalScope.launch(Dispatchers.Main) {
                        val placeName = adPlaceNameFlow.first()

                        debugLog(tag = TAG) { "$placeholder $placeName onViewAttachedToWindow" }
                        <EMAIL>()

                        val hasPreloadButNoDisplay = hasPreloadButNoDisplayFlow.first()

                        if (hasPreloadButNoDisplay && placeName != null) {
                            logEventRecord("ad_${placeName}_banner_impress")
                            logEventRecord("ad_banner_impress")
                            debugLog(tag = TAG) { "$placeholder ad_${placeName}_banner_impress" }
                            lastImpressInstantFlow.update { nowInstant() }
                            hasPreloadButNoDisplayFlow.update { false }
                        }
                    }
                }

                override fun onViewDetachedFromWindow(view: View) {
                    debugLog(tag = TAG) { "$placeholder onViewDetachedFromWindow" }
                    <EMAIL>()
                }
            })
            loadAd()
        }

    suspend fun getLastImpressInstant() = lastImpressInstantFlow.first()

    fun configure(placeName: String) {
        adPlaceNameFlow.update { placeName }
    }

    fun stopAutoRefresh() {
        debugLog(tag = TAG) { "$placeholder stopAutoRefresh" }

        adView.apply {
            setExtraParameter("allow_pause_auto_refresh_immediately", "true")
            stopAutoRefresh()
        }

        adPlaceNameFlow.update { null }
    }

    fun startAutoRefresh() {
        debugLog(tag = TAG) { "$placeholder startAutoRefresh" }
        adView.startAutoRefresh()
    }

    fun loadAd() {
        debugLog(tag = TAG) { "$placeholder loadAd" }
        adView.loadAd()
    }

}

class MaxBannerAdHelper {

    fun getBannerAd(
        context: Context,
        placeholder: BannerAdPlaceholder,
        placeName: String,
    ): MaxBannerAd {
        return bannerAdMap.getOrPut(placeholder) { MaxBannerAd(context, placeholder) }
            .apply { configure(placeName) }
    }

    fun destroy(placeholder: BannerAdPlaceholder) {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            bannerAdMap[placeholder]?.apply {
                runCatching {
                    (adView.parent as? ViewGroup)?.removeAllViews()
                    adView.destroy()
                }
            }
            bannerAdMap.remove(placeholder)
        }
    }

    fun destroyAll() {
        GlobalScope.launch {
            bannerAdMap.keys.forEach(::destroy)
        }
    }
}
