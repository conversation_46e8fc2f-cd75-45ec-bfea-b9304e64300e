package com.nbpt.app.biz.remoteconfig

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.ConfigUpdate
import com.google.firebase.remoteconfig.ConfigUpdateListener
import com.google.firebase.remoteconfig.FirebaseRemoteConfigException
//import com.nbpt.app.androidcomponent.repeatnoti.HealthArticleRepeatNoti
//import com.nbpt.app.androidcomponent.repeatnoti.HealthMonitoringReminderRepeatNoti
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.moshi.toObj
//import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfigValue
import com.google.firebase.remoteconfig.get
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.nbpt.app.androidcomponent.repeatnoti.ArticleRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemindRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemoteConfigMessageRepeatNotification
import com.nbpt.app.configureMaxAdIfNeeded
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant

class FirebaseRemoteConfig(context: Context) {

    companion object {
        const val TAG = "FirebaseRemoteConfig"
    }

    init {
        Firebase.remoteConfig.addOnConfigUpdateListener(object : ConfigUpdateListener {
            override fun onUpdate(configUpdate: ConfigUpdate) {
                GlobalScope.launch {
                    configureMaxAdIfNeeded(context)
                }
            }

            override fun onError(error: FirebaseRemoteConfigException) {
            }
        })
    }

    private class RemoteConfigDataStore(private val context: Context) {
        private val Context._dataStore: DataStore<Preferences> by preferencesDataStore(name = "FirebaseRemoteConfigDataStore")
        private val latestResetInstantSecondsKey = longPreferencesKey("latestResetInstantSeconds")

        suspend fun getLatestResetInstantSeconds(): Long {
            return context._dataStore.data.map { it[latestResetInstantSecondsKey] }.first() ?: 0L
        }

        suspend fun setLatestResetInstantSeconds(instant: Instant) {
            context._dataStore.edit { it[latestResetInstantSecondsKey] = instant.epochSeconds }
        }
    }

    private val remoteConfigDataStore = RemoteConfigDataStore(context)


    private val _remoteConfig get() = Firebase.remoteConfig

    fun tryToUpdateConfig(now: Instant) {
        GlobalScope.launch {
            val latestResetInstantSeconds = remoteConfigDataStore.getLatestResetInstantSeconds()

            if (now.epochSeconds - latestResetInstantSeconds >= 60 * 60L) { // 1 hour
                fetchAndActivate { isSuccessful ->
                    if (isSuccessful == true) {
                        GlobalScope.launch {
                            remoteConfigDataStore.setLatestResetInstantSeconds(now)
                        }
                    }
                }
            }
        }
    }

    fun fetchAndActivate(onCompleteBlock: (isSuccessful: Boolean?) -> Unit) {
        _remoteConfig.fetchAndActivate().addOnCompleteListener {
            onCompleteBlock(it.isSuccessful)
        }
    }

    fun reset() {
        _remoteConfig.reset()
    }

    fun useLegacyAd(): Boolean {
        val key = RemoteConfigKey.USE_LEGACY_AD
        return _remoteConfig.getBoolean(key)
    }

    fun adConfig1(): AdConfig {
        val key = RemoteConfigKey.AD_CONFIG_1
        return _remoteConfig[key].toObjOrNull() ?: AdConfig.Default
    }

    fun adConfig2(): AdConfig {
        val key = RemoteConfigKey.AD_CONFIG_2
        return _remoteConfig[key].toObjOrNull() ?: AdConfig.Default2
    }

    fun repeatNotiPushStrategy(
        repeatNotification: RemoteConfigMessageRepeatNotification
    ): RepeatNotiPushStrategy {
        val (key, defaultStrategy) = when (repeatNotification) {
            is RemindRepeatNoti -> {
                RemoteConfigKey.REMIND_REPEAT_NOTI_PUSH_STRATEGY to RepeatNotiPushStrategy(60, 20)
            }

            is ArticleRepeatNoti -> {
                RemoteConfigKey.ARTICLE_REPEAT_NOTI_PUSH_STRATEGY to RepeatNotiPushStrategy(40, 30)
            }
        }

        debugLog(tag = TAG) { "repeatNoti: $repeatNotification, key: $key" }
        return _remoteConfig[key].toObjOrNull() ?: defaultStrategy
    }

    fun remindNotiGroup(): RepeatNotiGroup? {
        val key = RemoteConfigKey.REMIND_NOTI_GROUP
        return _remoteConfig[key].toObjOrNull()
    }

    fun articleNotiGroup(): RepeatNotiGroup? {
        val key = RemoteConfigKey.ARTICLE_NOTI_GROUP
        return _remoteConfig[key].toObjOrNull()
    }

    fun addRemindAlarmGuideConfig(): AddRemindAlarmGuideConfig {
        val key = RemoteConfigKey.ADD_REMIND_ALARM_GUIDE_CONFIG
        return _remoteConfig[key].toObjOrNull() ?: AddRemindAlarmGuideConfig.Default
    }

    fun dailyNotiTimes(): Int {
        val key = RemoteConfigKey.DAILY_NOTI_TIMES
        return _remoteConfig[key].asString().toIntOrNull() ?: 10
    }

    fun adPlaceControl(): AdPlaceControl {
        val key = RemoteConfigKey.AD_PLACE_CONTROL
        return _remoteConfig[key].toObjOrNull() ?: AdPlaceControl()
    }

    fun adaptiveRectAdConfig(): AdaptiveRectAdConfig {
        val key = RemoteConfigKey.ADAPTIVE_RECTANGLE_AD_CONFIG
        return _remoteConfig[key].toObjOrNull() ?: AdaptiveRectAdConfig.Default
    }

    fun smartRectAdConfig(): SmartRectAdConfig {
        val key = RemoteConfigKey.SMART_RECTANGLE_AD_CONFIG
        return _remoteConfig[key].toObjOrNull() ?: SmartRectAdConfig.Default
    }

    fun useNoveltyRectAdLayout(): Boolean {
        val key = RemoteConfigKey.USE_NOVELTY_RECT_AD_LAYOUT

        return _remoteConfig.getBoolean(key)
    }

    private inline fun <reified T> FirebaseRemoteConfigValue.toObjOrNull(): T? {
        return try {
            asString().toObj<T>()
        } catch (_: Exception) {
            return null
        }
    }
}
