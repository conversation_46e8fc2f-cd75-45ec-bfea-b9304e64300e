package com.nbpt.app.biz.admanager.interstitial

import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import kotlinx.coroutines.runBlocking
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object InterAdKeyController : KoinComponent {

    private val ubds: UserBehaviorDataStore by inject()

    private const val TAG = "InterAdKeyController"

    fun useNextAdKey(nextActiveIntervalMinutes: Int): Bo<PERSON>an {
        debugLog(tag = TAG) { "nextActiveIntervalMinutes: $nextActiveIntervalMinutes" }
        return when {
            nextActiveIntervalMinutes < 0 -> false
            nextActiveIntervalMinutes == 0 -> true
            else -> {
                val firstTimeLaunchAppInstant = runBlocking { ubds.get1stTimeLaunchAppInstant() }
                val nowInstant = nowInstant()

                firstTimeLaunchAppInstant + nextActiveIntervalMinutes.toDuration(DurationUnit.MINUTES) < nowInstant
            }
        }
    }

}