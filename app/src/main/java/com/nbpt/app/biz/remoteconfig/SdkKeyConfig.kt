//package com.nbpt.app.biz.remoteconfig
//
//import androidx.annotation.Keep
//import com.squareup.moshi.JsonClass
//
////language=json
//private val json = """
//    {
//      "appOpenAdKey": "",
//      "bannerAdKey": "",
//      "nativeAdKey": "",
//      "interstitialAdKey": "",
//      "rewardedInterstitialAdKey": ""
//    }
//""".trimIndent()
//
//@Keep
//@JsonClass(generateAdapter = true)
//data class SdkKeyConfig(
//    val appOpenAdKey: String = "",
//    val bannerAdKey: String = "",
//    val nativeAdKey: String = "",
//    val interstitialAdKey: String = "",
//    val rewardedInterstitialAdKey: String = "",
//) {
//    companion object {
//        val Empty = SdkKeyConfig()
//    }
//}