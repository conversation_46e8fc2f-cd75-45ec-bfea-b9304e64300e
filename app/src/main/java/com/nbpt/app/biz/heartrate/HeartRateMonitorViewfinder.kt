package com.nbpt.app.biz.heartrate

import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView

@Composable
fun HeartRateMonitorViewfinder(
    onViewfinderCreate: (PreviewView) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(shape = RoundedCornerShape(percent = 50), modifier = modifier) {
        AndroidView(
            factory = {
                PreviewView(it).apply {
                    onViewfinderCreate(this)
                }
            },
            modifier = modifier,
        )
    }
}
