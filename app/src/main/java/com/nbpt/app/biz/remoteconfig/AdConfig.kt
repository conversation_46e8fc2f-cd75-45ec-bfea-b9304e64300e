package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.nbpt.app.biz.BizConfig
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

//language=json
private val json = """
    {
      "ad_show_interval_seconds": 60,
      "ad_loading_timeout_seconds": 5,
      "next_ad_unit_interval_minutes": -1
    }
""".trimIndent()

@JsonClass(generateAdapter = true)
@Keep
data class AdConfig(
    @Json(name = "sdk_key")
    val adSdkKey: String = "",

    @<PERSON><PERSON>(name = "app_open_ad")
    val appOpenAd: String = "",

    @<PERSON><PERSON>(name = "banner_ad_key")
    val bannerAdKey: String = "",

    @<PERSON><PERSON>(name = "native_ad_key")
    val nativeAdKey: String = "",

    @<PERSON><PERSON>(name = "inter_ad_key")
    val interAdKey: String = "",

    @<PERSON><PERSON>(name = "inter_ad_key2")
    val interAdKey2: String = "",

    @<PERSON><PERSON>(name = "ad_show_interval_seconds")
    val adShowIntervalSeconds: Int = 120,

    @<PERSON><PERSON>(name = "ad_loading_timeout_seconds")
    val adLoadingTimeoutSeconds: Int = 5,

    @Json(name = "next_inter_ad_key_active_interval_minutes")
    val nextInterAdKeyActiveIntervalMinutes: Int = -1
) {
    companion object {
        val Default = AdConfig(
            appOpenAd = BizConfig.ADMOB_AD_UNIT_ID_FOR_APP_OPEN,
            bannerAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_BANNER,
            nativeAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_NATIVE,
            interAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL,
            interAdKey2 = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL_2,
        )
        val Default2 = AdConfig(
            appOpenAd = BizConfig.ADMOB_AD_UNIT_ID_FOR_APP_OPEN,
            bannerAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_BANNER,
            nativeAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_NATIVE,
            interAdKey = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL,
            interAdKey2 = BizConfig.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL_2,
        )
    }
}
