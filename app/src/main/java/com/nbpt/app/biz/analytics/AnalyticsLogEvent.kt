package com.nbpt.app.biz.analytics

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Bundle
import com.applovin.mediation.MaxAd
import com.google.android.gms.ads.AdValue
import com.nbpt.app.BuildConfig
import com.nbpt.app.bi.BiReporter
import com.nbpt.app.bi.reportAdOnPaid
import com.nbpt.app.biz.analytics.tenjin.TenjinHelper
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.AnalyticsDataStore
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.lang.IllegalArgumentException

@SuppressLint("StaticFieldLeak")
object AnalyticsLogEvent : KoinComponent {
    private const val TAG = "AnalyticsLogEvent"

    private val tenjinHelper: TenjinHelper by inject()

    private val analyticsDataStore: AnalyticsDataStore by inject()

    private val biReporter: BiReporter by inject()

    private var activity: Activity? = null

    fun configureActivity(activity: Activity) {
        this.activity = activity
    }

    fun record(eventName: String, args: Bundle? = Bundle()) {
        checkEventNameIllegal(eventName)

        if (BuildConfig.DEBUG) return

        GlobalScope.launch {
            if (args == null) {
                debugLog { "$TAG eventName:{$eventName}" }
            }

            Firebase.analytics.logEvent(eventName, args)

            debugLog { "$TAG eventName:{$eventName}, _args.size: ${args?.size()}}" }

//            activity?.showToast("$eventName: tenjin_network:{${tenjinAttribution.adNetwork}}, tenjin_campaign:{${tenjinAttribution.campaignName}}")
        }
    }

    fun revenueRecord(eventName: String, args: Bundle? = Bundle()) {
        checkEventNameIllegal(eventName)

        if (BuildConfig.DEBUG) return

        GlobalScope.launch {
            if (args == null) {
                debugLog { "$TAG eventName:{$eventName}" }
            }

            Firebase.analytics.logEvent(eventName, args)

            debugLog { "$TAG eventName:{$eventName}, _args.size: ${args?.size()}}" }

//            activity?.showToast("$eventName: tenjin_network:{${tenjinAttribution.adNetwork}}, tenjin_campaign:{${tenjinAttribution.campaignName}}")
        }
    }

    fun tryToRecordTotalAdsRevenue001(
        ad: MaxAd,
    ) {
        GlobalScope.launch {
            val adFormat = ad.format.label
            val revenue = ad.revenue
            val adNetwork = ad.networkName
            val adUnitId = ad.adUnitId

            val total = analyticsDataStore.getTotalAdRevenue()
            val newTotal = total + revenue

            if (newTotal >= 0.01) {
                logEventAdRevenueRecord("Total_Ads_Revenue_001") {
                    putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
                    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
                    putString("adNetwork", adNetwork ?: "")
                    putString("adFormat", adFormat ?: "")
                }

                analyticsDataStore.storeTotalAdRevenue(0.0)
            } else {
                analyticsDataStore.storeTotalAdRevenue(newTotal)
            }
        }
    }

    fun recordAdImpressionRevenue(
        ad: MaxAd,
        adPlacement: String,
    ) {
        logEventAdRevenueRecord("Ad_Impression_Revenue") {
            putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            putString("adNetwork", ad.networkName)
            putString("adFormat", ad.format.label)
        }

        biLogEventAdOnPaid(
            value = ad.revenue.toFloat(),
            currency = "USD",
            precisionType = ad.revenuePrecision,
            adNetwork = ad.networkName ?: "",
            adType = ad.format.label ?: "",
            adPlacement = adPlacement
        )
    }

    fun recordAdImpression(
        ad: MaxAd
    ) {
        logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
            putString(FirebaseAnalytics.Param.AD_PLATFORM, "appLovin")
            putString(FirebaseAnalytics.Param.AD_SOURCE, ad.networkName)
            putString(FirebaseAnalytics.Param.AD_FORMAT, ad.format.displayName)
            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, ad.adUnitId)
            putDouble(FirebaseAnalytics.Param.VALUE, ad.revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
        }
    }

    fun tryToRecordTotalAdsRevenue001(
        adValue: AdValue?,
        adSourceName: String?,
    ) {
        GlobalScope.launch {
            val valueMicros = adValue?.valueMicros ?: return@launch

            val revenue = valueMicros / (1_000_000).toDouble()

            val total = analyticsDataStore.getTotalAdRevenue()
            val newTotal = total + revenue

            if (newTotal >= 0.01) {
                logEventAdRevenueRecord("Total_Ads_Revenue_001") {
                    putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
                    putString(FirebaseAnalytics.Param.CURRENCY, "USD")
                    putString("adNetwork", adSourceName ?: "")
                    putString("precisionType", adValue.precisionType.toString())
                }

                analyticsDataStore.storeTotalAdRevenue(0.0)
            } else {
                analyticsDataStore.storeTotalAdRevenue(newTotal)
            }
        }
    }

    fun recordAdImpressionRevenue(
        adValue: AdValue?,
        adSourceName: String?,
        adFormat: String,
        adPlacement: String,
    ) {
        val valueMicros = adValue?.valueMicros ?: return

        val revenue = valueMicros / (1_000_000).toDouble()

        logEventAdRevenueRecord("Ad_Impression_Revenue") {
            putDouble(FirebaseAnalytics.Param.VALUE, revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
            putString("adNetwork", adSourceName ?: "")
            putString("precisionType", adValue.precisionType.toString())
        }
    }

    fun recordAdImpression(
        adValue: AdValue?,
        adSourceName: String?,
        adFormat: String,
        adUnit: String,
    ) {
        val valueMicros = adValue?.valueMicros ?: return

        val revenue = valueMicros / (1_000_000).toDouble()

        logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
            putString(FirebaseAnalytics.Param.AD_SOURCE, adSourceName)
            putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
            putString(FirebaseAnalytics.Param.AD_UNIT_NAME, adUnit)
            putDouble(FirebaseAnalytics.Param.VALUE, revenue)
            putString(FirebaseAnalytics.Param.CURRENCY, "USD")
        }

        biLogEventAdOnPaid(
            value = revenue.toFloat(),
            currency = "USD",
            precisionType = adValue.precisionType.toString(),
            adNetwork = adSourceName ?: "",
            adType = adFormat,
        )
    }

    fun roasReport(adValue: AdValue, ad: Any?) {
        tenjinHelper.roasAdMob(adValue, ad)
    }

    fun roasReport(maxAd: MaxAd) {
        tenjinHelper.roasApplovin(maxAd)
    }

    fun biLogEventAdOnPaid(
        value: Float,
        currency: String,
        precisionType: String,
        adNetwork: String,
        adType: String,
        adPlacement: String = "",
    ) {
        GlobalScope.launch {
            biReporter.reportAdOnPaid(
                value,
                currency,
                precisionType,
                adNetwork,
                adType,
                adPlacement
            )
        }
    }

    private fun checkEventNameIllegal(eventName: String) {
        if (eventName.length > 40) {
            if (BuildConfig.DEBUG) {
                throw IllegalArgumentException("Event name:\"$eventName\" can be up to 40 characters long.")
            } else {
                Firebase.crashlytics.recordException(IllegalArgumentException("Event name:\"$eventName\" can be up to 40 characters long."))
            }
        }
    }
}

fun logEventRecord(eventName: String, argsBlock: (Bundle.() -> Unit)? = null) {
    AnalyticsLogEvent.record(
        eventName = eventName,
        args = Bundle().apply {
            argsBlock?.invoke(this)
        }
    )
}

fun logEventAdRevenueRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
    AnalyticsLogEvent.revenueRecord(eventName, Bundle().apply(argsBlock))
}

