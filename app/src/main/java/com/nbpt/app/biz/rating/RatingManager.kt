package com.nbpt.app.biz.rating

import android.app.Activity
import android.content.Context
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.datetime.todayStartInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.screen.destinations.RatingDestination
import com.google.android.play.core.review.ReviewManagerFactory
import com.nbpt.app.data.datastore.AnalyticsDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import kotlin.time.Duration.Companion.hours
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class RatingManager(
    private val context: Context,
    private val userBehaviorDataStore: UserBehaviorDataStore,
    private val analyticsDataStore: AnalyticsDataStore
) {
    private val gpReviewManager by lazy { ReviewManagerFactory.create(context) }

    suspend fun tryToOpenRatingSheet(activity: Activity): Boolean {
        val ratingTimes = userBehaviorDataStore.getRatingTimes()
        val latestRatingInstant = userBehaviorDataStore.latestRatingInstant()

        val now = nowInstant()

        val is1stDay = latestRatingInstant == null
        val isOver48Hours = if (latestRatingInstant == null) {
            false
        } else {
            now - 48.hours >= latestRatingInstant
        }

        val needRating = when {
            ratingTimes == 0 && is1stDay -> true
            ratingTimes == 1 && isOver48Hours -> true
            ratingTimes == 2 && isOver48Hours -> true
            else -> false
        }

        val directlyOpenGooglePlayInAppReviews = false

        return if (needRating) {
            delay(150)
            if (directlyOpenGooglePlayInAppReviews) {
                withContext(Dispatchers.Main.immediate) {
                    openGooglePlayInAppReviews(activity)
                }
            } else {
                GlobalNavigator.navigate(RatingDestination.route)
                logEventRecord("show_rate_dialog")
                userBehaviorDataStore.setRatingTimes(ratingTimes + 1)
                userBehaviorDataStore.setRatingInstant(nowInstant())
            }
            true
        } else {
            false
        }
    }

    fun openGooglePlayInAppReviews(activity: Activity) {
        gpReviewManager.requestReviewFlow().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                runCatching {
                    gpReviewManager.launchReviewFlow(activity, task.result)
                        .addOnCompleteListener {
                            debugLog { "ReviewsHelper launchReviewFlow it.isSuccessful: ${it.isSuccessful}" }
                            debugLog { "ReviewsHelper launchReviewFlow it.result: ${it.result}" }
                            GlobalScope.launch {
                                userBehaviorDataStore.setRatingTimes(3)
                            }

                            logEventRecord("show_rate_dialog_gp")
                        }
                }
            }
        }
    }
}
