@file:Suppress("ObjectPropertyName")

package com.nbpt.app.biz.admanager.appopen

import android.app.Activity
import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAppOpenAd
import com.nbpt.app.BuildConfig
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.biz.BizConfig
import com.nbpt.app.biz.admanager.FullscreenAdManager
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.AppOpenAdConfig
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import com.nbpt.app.common.sendBlock
import com.nbpt.app.mainActivity
import com.nbpt.app.ui.screen.destinations.SplashDestination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import java.util.UUID
import kotlin.time.DurationUnit
import kotlin.time.toDuration

data class CloseSplashEvent(val eventId: String = UUID.randomUUID().toString())

@Suppress("ObjectPropertyName")
internal val _closeSplashEventFlow: EventFlow<CloseSplashEvent> = EventFlow()

val closeSplashEventFlow: SharedFlow<CloseSplashEvent> get() = _closeSplashEventFlow

private const val TAG = "MaxAppOpenAdHelper"


sealed interface TryToShowExecuteResult {
  data object DoNotShow : TryToShowExecuteResult
  data object ReadyToShow : TryToShowExecuteResult
  data object Showing : TryToShowExecuteResult
  data object ShowFinish : TryToShowExecuteResult
  data object Error : TryToShowExecuteResult
}

class MaxAppOpenAdHelper(
  private val context: Context,
  private val splashController: SplashManager,
  private val remoteConfig: RealRemoteConfig,
  private val fullscreenAdManager: FullscreenAdManager
) {
  private val adKey
    get() = remoteConfig.adConfig1.appOpenAd.ifEmpty { BizConfig.APPLOVIN_MAX_APP_OPEN_AD }

  private val appOpenAdConfig: AppOpenAdConfig
    get() = remoteConfig.adConfig1.let {
      AppOpenAdConfig(
        it.adShowIntervalSeconds,
        it.adLoadingTimeoutSeconds
      )
    }

  private val _adCacheDuration = 55.toDuration(DurationUnit.MINUTES)

  private var _appOpenAd: MaxAppOpenAd? = null

  private val _lastCacheAdInstant = MutableStateFlow(Instant.fromEpochSeconds(0))
  private val _isLoadingAdFlow = MutableStateFlow(false)

  val tryToShowExecuteResultEventFlow = EventFlow<TryToShowExecuteResult?>()

  private val maxAdOpenAdListener = object : MaxAdListener, MaxAdRevenueListener {
    override fun onAdLoaded(p0: MaxAd) {
      debugLog { "$TAG maxAdOpenAdListener onAdLoaded" }
      _isLoadingAdFlow.update { false }
      _closeSplashEventFlow.sendBlock {
        CloseSplashEvent().apply {
          debugLog { "loadAd send closeSplashEvent when onAdLoaded(): $this" }
        }
      }
      _lastCacheAdInstant.update { nowInstant() }
    }

    override fun onAdDisplayed(p0: MaxAd) {
      debugLog { "$TAG maxAdOpenAdListener onAdDisplayed" }

      fullscreenAdManager.latestShowAdSuccessInstant = nowInstant()

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Showing)
      splashController.doSkipSplash(true)

      debugLog { "ad_app_open_impress" }
      logEventRecord("ad_app_open_impress")
    }

    override fun onAdHidden(p0: MaxAd) {
      debugLog { "$TAG maxAdOpenAdListener onAdHidden" }

      debugLog { "ad_app_open_close" }
      logEventRecord("ad_app_open_close")

      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ShowFinish)

      loadAppOpenAd()
    }

    override fun onAdClicked(p0: MaxAd) {
      debugLog { "ad_app_open_click" }
      logEventRecord("ad_app_open_click")
      splashController.doSkipSplash(true)
    }

    override fun onAdLoadFailed(p0: String, p1: MaxError) {
      debugLog { "$TAG maxAdOpenAdListener onAdLoadFailed: $p1" }

      debugLog { "ad_app_open_load_failed" }
      logEventRecord("ad_app_open_load_failed")

      _isLoadingAdFlow.update { false }
    }

    override fun onAdDisplayFailed(p0: MaxAd, p1: MaxError) {
      debugLog { "$TAG maxAdOpenAdListener onAdDisplayFailed" }

      debugLog { "ad_app_open_display_failed" }
      logEventRecord("ad_app_open_display_failed")
      tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.Error)

    }

    override fun onAdRevenuePaid(ad: MaxAd) {
      if (!BuildConfig.DEBUG) {
        GlobalScope.launch {
            AnalyticsLogEvent.recordAdImpressionRevenue(ad, "splash")
            AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(ad)
            AnalyticsLogEvent.recordAdImpression(ad)

            AnalyticsLogEvent.roasReport(ad)
          }
      }
    }

  }

  fun initIfNeed(activity: Activity) {
    if (_appOpenAd?.adUnitId != adKey) {
      _appOpenAd?.destroy()
      _appOpenAd = null
      _appOpenAd = MaxAppOpenAd(adKey, activity).apply {
        setListener(maxAdOpenAdListener)
        setRevenueListener(maxAdOpenAdListener)
      }
      loadAppOpenAd(skipInitIfNeeded = true)
    }
  }

  private fun loadAppOpenAd(skipInitIfNeeded: Boolean = false) {
    if (skipInitIfNeeded.not()) {
      if (_appOpenAd?.adUnitId != adKey) {
        _appOpenAd?.destroy()
        _appOpenAd = null
        _appOpenAd = MaxAppOpenAd(adKey, mainActivity ?: context).apply {
          setListener(maxAdOpenAdListener)
          setRevenueListener(maxAdOpenAdListener)
        }
      }
    }

    _appOpenAd?.let {
      debugLog { "$TAG loadAppOpenAd()" }
      it.loadAd()
      _isLoadingAdFlow.update { true }
    }
  }

  private fun showAppOpenAd() {
    _appOpenAd?.showAd()
    debugLog { ("$TAG _appOpenAd?.showAd()") }
  }

  fun tryToLoadAd() {
    debugLog { ("$TAG tryToLoadAd()") }

    GlobalScope.launch(Dispatchers.Main.immediate) {
      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) { // has available ad cache
        debugLog { ("$TAG tryToLoadAd() has available ad cache") }

        _closeSplashEventFlow.sendBlock {
          CloseSplashEvent().apply {
            debugLog { "loadAd send closeSplashEvent when has ad cache: $this" }
          }
        }
      } else {
        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog { ("$TAG tryToLoadAd() isLoadingAd: $isLoadingAd") }

        if (!isLoadingAd) {
          loadAppOpenAd()
        }

        launch {
          instantLoadTimeoutDelay()
          if (_appOpenAd?.isReady != true) {
            _closeSplashEventFlow.sendBlock {
              CloseSplashEvent().apply {
                debugLog { "loadAd send closeSplashEvent when timeout: $this" }
              }
            }
          }
        }
      }
    }
  }

  fun tryToShowAd() {
    debugLog { ("$TAG tryToShowAd()") }
    GlobalScope.launch(Dispatchers.Main.immediate) {
      debugLog { "ad_app_open_show" }
      logEventRecord("ad_app_open_show")

      val now = nowInstant()

      if (
        _appOpenAd?.isReady == true
        && fullscreenAdManager.isAdShowTimeInShowInterval().not()
        && now - _adCacheDuration < _lastCacheAdInstant.first()
      ) {
        debugLog { ("$TAG cache available invoke showAppOpenAd()") }
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.ReadyToShow)
        delay(2_000)

        if (GlobalNavigator.currentDestination() is SplashDestination) {
          showAppOpenAd()
        }
      } else {
        tryToShowExecuteResultEventFlow.send(TryToShowExecuteResult.DoNotShow)

        val isLoadingAd = _isLoadingAdFlow.first()
        debugLog { "$TAG cache unavailable isLoadingAd: $isLoadingAd" }
        if (!isLoadingAd) {
          loadAppOpenAd()
        }
      }
    }
  }

  suspend fun instantLoadTimeoutDelay() {
    delay(appOpenAdConfig.adLoadingTimeoutSeconds.toDuration(DurationUnit.SECONDS))
  }
}
