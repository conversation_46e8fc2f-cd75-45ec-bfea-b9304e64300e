package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.squareup.moshi.JsonClass

@Suppress("PropertyName")
@JsonClass(generateAdapter = true)
@Keep
data class AdPlaceControl(
    val enabled_home_native_ad: Boolean = false,
    val enabled_feats_banner_ad: Boolean = false,
    val enabled_article_banner_ad: Boolean = false,
)

//language=json
private val json = """
{
  "enabled_home_native_ad": false,
  "enabled_feats_banner_ad": false,
  "enabled_article_banner_ad": false
}
""".trimIndent()
