package com.nbpt.app.biz.remoteconfig

object RemoteConfigKey {
    const val USE_LEGACY_AD = "use_legacy_ad"
    const val AD_CONFIG_1 = "ad_config"
    const val AD_CONFIG_2 = "ad_config2"
    const val REMIND_REPEAT_NOTI_PUSH_STRATEGY = "remind_repeat_noti_push_strategy"
    const val ARTICLE_REPEAT_NOTI_PUSH_STRATEGY = "article_repeat_noti_push_strategy"
    const val REMIND_NOTI_GROUP = "remind_noti_group"
    const val ARTICLE_NOTI_GROUP = "article_noti_group"
    const val ADD_REMIND_ALARM_GUIDE_CONFIG = "add_remind_alarm_guide_config"
    const val DAILY_NOTI_TIMES = "daily_noti_times"
    const val AD_PLACE_CONTROL = "ad_place_control"

    const val ADAPTIVE_RECTANGLE_AD_CONFIG = "adaptive_rectangle_ad_config"
    const val SMART_RECTANGLE_AD_CONFIG = "smart_rectangle_ad_config"
    const val USE_NOVELTY_RECT_AD_LAYOUT = "use_novelty_rect_ad_layout"
}