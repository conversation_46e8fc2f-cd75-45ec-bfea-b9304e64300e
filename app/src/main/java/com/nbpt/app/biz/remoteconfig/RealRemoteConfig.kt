package com.nbpt.app.biz.remoteconfig

import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import com.nbpt.app.androidcomponent.repeatnoti.RemoteConfigMessageRepeatNotification
import com.nbpt.app.bi.BiApiRemoteConfig
import org.koin.core.context.GlobalContext

val useLegacyAd: Boolean
    get() {
        return GlobalContext.get().get<RealRemoteConfig>().useLegacyAd
    }

class RealRemoteConfig(
    private val firebaseRemoteConfig: FirebaseRemoteConfig,
    private val apiRemoteConfig: BiApiRemoteConfig
) {

    val useLegacyAd: Boolean
        get() = apiRemoteConfig.useLegacyAd() ?: firebaseRemoteConfig.useLegacyAd()
//        get() = true

    val adConfig1: AdConfig
        get() = apiRemoteConfig.adConfig1() ?: firebaseRemoteConfig.adConfig1()

    val adConfig2: AdConfig
        get() = apiRemoteConfig.adConfig2() ?: firebaseRemoteConfig.adConfig2()

    fun repeatNotiPushStrategy(
        repeatNotification: RemoteConfigMessageRepeatNotification
    ): RepeatNotiPushStrategy {
        return apiRemoteConfig.repeatNotiPushStrategy(repeatNotification)
            ?: firebaseRemoteConfig.repeatNotiPushStrategy(repeatNotification)
    }

    val remindNotiGroup: RepeatNotiGroup?
        get() = apiRemoteConfig.remindNotiGroup() ?: firebaseRemoteConfig.remindNotiGroup()

    val articleNotiGroup: RepeatNotiGroup?
        get() = apiRemoteConfig.articleNotiGroup() ?: firebaseRemoteConfig.articleNotiGroup()

    val addRemindAlarmGuideConfig: AddRemindAlarmGuideConfig
        get() = apiRemoteConfig.addRemindAlarmGuideConfig()
            ?: firebaseRemoteConfig.addRemindAlarmGuideConfig()

    val dailyNotiTimes: Int
        get() = apiRemoteConfig.dailyNotiTimes() ?: firebaseRemoteConfig.dailyNotiTimes()

    val adPlaceControl: AdPlaceControl
        get() = apiRemoteConfig.adPlaceControl() ?: firebaseRemoteConfig.adPlaceControl()

    val adaptiveRectAdConfig: AdaptiveRectAdConfig
        get() = apiRemoteConfig.adaptiveRectAdConfig()
            ?: firebaseRemoteConfig.adaptiveRectAdConfig()

    val smartRectAdConfig: SmartRectAdConfig
        get() = apiRemoteConfig.smartRectAdConfig()
            ?: firebaseRemoteConfig.smartRectAdConfig()

    val useNoveltyRectAdLayout: Boolean
        get() = apiRemoteConfig.useNoveltyRectAdLayout()
            ?: firebaseRemoteConfig.useNoveltyRectAdLayout()

    @Composable
    fun rememberUseLegacyAd(): Boolean {
        // 不需要 remember(remoteConfig)，因为 remoteConfig 是单例
        // 直接使用 derivedStateOf 来观察 useLegacyAdConfig 的变化
        val configValue by remember {
            derivedStateOf { useLegacyAd }
        }

        return configValue
    }


    @Composable
    fun rememberUseNoveltyRectAdLayout(): Boolean {
        val configValue by remember {
            derivedStateOf { useNoveltyRectAdLayout }
        }

        return configValue
    }
}