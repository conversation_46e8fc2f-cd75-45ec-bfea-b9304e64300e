package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

//language=json
private val json = """
    {
      "ad_show_interval_seconds": 600,
      "ad_loading_timeout_seconds": 5
    }
""".trimIndent()

@JsonClass(generateAdapter = true)
@Keep
data class AppOpenAdConfig(
    @Json(name = "ad_show_interval_seconds")
    val adShowIntervalSeconds: Int = 600,

    @Json(name = "ad_loading_timeout_seconds")
    val adLoadingTimeoutSeconds: Int = 5,
) {
    companion object {
        val Default = AppOpenAdConfig()
    }
}
