package com.nbpt.app.biz.admanager.adaptive

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun SmartRectAd(
    pageType: SmartAdPageType,
    bannerAdPlace: BannerAdPlace?,
    nativeAdPlace: NativeAdPlace?,
    modifier: Modifier = Modifier,
    remoteConfig: RealRemoteConfig = koinInject()
) {
    val smartRectAdConfig =
        remember(remoteConfig.smartRectAdConfig) { remoteConfig.smartRectAdConfig }

    val adDisplayType = remember(pageType, smartRectAdConfig) {
        when (pageType) {
            SmartAdPageType.HOME_TOP_BAR -> smartRectAdConfig.homeTopBar
            SmartAdPageType.HOME_BOTTOM_BAR -> smartRectAdConfig.homeBottomBar
            SmartAdPageType.HOME_CONTENT -> smartRectAdConfig.homeContent
            SmartAdPageType.RESULT_TOP_BAR -> smartRectAdConfig.resultTopBar
            SmartAdPageType.RESULT_BOTTOM_BAR -> smartRectAdConfig.resultBottomBar
            SmartAdPageType.RESULT_CONTENT -> smartRectAdConfig.resultContent
            SmartAdPageType.FEAT_TOP_BAR -> smartRectAdConfig.featTopBar
            SmartAdPageType.FEAT_BOTTOM_BAR -> smartRectAdConfig.featBottomBar
            SmartAdPageType.FEAT_CONTENT -> smartRectAdConfig.featContent
        }
    }

    when (adDisplayType) {
        RectAdDisplayType.BANNER -> {
            bannerAdPlace?.let {
                BannerAd(
                    placeholder = bannerAdPlace,
                    modifier = modifier
                )
            }
        }

        RectAdDisplayType.NATIVE -> {
            nativeAdPlace?.let {
                NativeAd(
                    place = nativeAdPlace,
                    modifier = modifier.padding(horizontal = 16.dp)
                )
            }
        }

        RectAdDisplayType.NONE -> {}

        else -> {}
    }
}