package com.nbpt.app.biz.admanager.banner

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun BannerAd(
    placeholder: BannerAdPlaceholder,
    modifier: Modifier = Modifier
) {
    val remoteConfig: RealRemoteConfig = koinInject()

    val enabledFeatsBannerAd =
        remember(remoteConfig.adPlaceControl.enabled_feats_banner_ad) {
            remoteConfig.adPlaceControl.enabled_feats_banner_ad
        }

    val enabledArticleBannerAd =
        remember(remoteConfig.adPlaceControl.enabled_article_banner_ad) {
            remoteConfig.adPlaceControl.enabled_article_banner_ad
        }

    val inBannerAdFeatsPlace =
        remember(placeholder) { placeholder in BannerAdFeatsPlace }

    val inArticlePlace =
        remember(placeholder) { placeholder == BannerAdPlaceholder.ARTICLE }

    if (
        (inBannerAdFeatsPlace && !enabledFeatsBannerAd)
        || (inArticlePlace && !enabledArticleBannerAd)
    ) return

    val useLegacyAdConfig = remoteConfig.rememberUseLegacyAd()
    val useNoveltyRectAdLayout = remoteConfig.rememberUseNoveltyRectAdLayout()

    if (useLegacyAdConfig) {
        if (useNoveltyRectAdLayout) {
            MaxBannerAd2(
                placeholder = placeholder,
                modifier = modifier
            )
        } else {
            MaxBannerAd(
                placeholder = placeholder,
                modifier = modifier
            )
        }
    } else {
        if (useNoveltyRectAdLayout) {
            AdmobBannerAd2(
                adPlace = placeholder,
                modifier = modifier
            )
        } else {
            AdmobBannerAd(
                adPlace = placeholder,
                modifier = modifier
            )
        }
    }
}