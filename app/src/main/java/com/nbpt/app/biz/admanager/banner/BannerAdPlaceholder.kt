package com.nbpt.app.biz.admanager.banner

typealias BannerAdPlace = BannerAdPlaceholder

enum class BannerAdPlaceholder(val placeName: String) {
    TEST("test"),

    HOME("home"),
    ARTICLE("article"),

    BP("bp"),
    BP_EDITOR("bp_editor"),
    BP_HISTORY("bp_history"),

    HR("hr"),
    HR_EDITOR("ht_editor"),
    HR_MEASURE("hr_measure"),
    HR_HISTORY("hr_history"),

    BS("bs"),
    BS_EDITOR("bs_editor"),
    BS_HISTORY("bs_history"),

    ALARM_EDITOR("alarm_editor"),
    ALARM_LIST("alarm_list"),

    BMI("bmi"),
    BMI_RESULT("bmi_result"),

    PERMISSIONS("permissions"),

    GUIDE("guide"),
    Language("language"),
}

val BannerAdFeatsPlace = listOf(
    BannerAdPlaceholder.BP,
    BannerAdPlaceholder.BP_EDITOR,
    BannerAdPlaceholder.HR,
    BannerAdPlaceholder.HR_EDITOR,
    BannerAdPlaceholder.BS,
    BannerAdPlaceholder.BS_EDITOR,
    BannerAdPlaceholder.ALARM_EDITOR,
    BannerAdPlaceholder.BMI,
    BannerAdPlaceholder.BMI_RESULT,
    BannerAdPlaceholder.PERMISSIONS
)