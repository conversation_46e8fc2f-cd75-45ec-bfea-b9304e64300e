package com.nbpt.app.biz.alarmguide

import com.nbpt.app.GlobalNavigator
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmDataStore
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.screen.destinations.AlarmGuideDestination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class AddRemindAlarmGuideManager(
    private val remoteConfig: RealRemoteConfig,
    private val alarmDataStore: RemindToRecordAlarmDataStore,
    private val userBehaviorDataStore: UserBehaviorDataStore
) {
    companion object {
        private const val TAG = "AddRemindAlarmGuideManager"
    }

    suspend fun tryToOpenGuideDialog(): Boolean = withContext(Dispatchers.Main) {
        debugLog(tag = TAG) { "tryToOpenGuideDialog" }

        val now = nowInstant()
        val latestShowAddRemindAlarmGuideDialogInstant =
            userBehaviorDataStore.latestShowAddRemindAlarmGuideDialogInstant()
                ?: Instant.fromEpochSeconds(0)

        val nextTriggerInterval = remoteConfig.addRemindAlarmGuideConfig
            .nextTriggerIntervalMinute
            .toDuration(DurationUnit.MINUTES)

        val isLatestShowGuideInstantOver =
            now > latestShowAddRemindAlarmGuideDialogInstant + nextTriggerInterval

        if (!isLatestShowGuideInstantOver) return@withContext false

        val alarmGroupEmpty = alarmDataStore.fetchAlarmGroups().isEmpty()
        if (!alarmGroupEmpty) return@withContext false


        delay(500)
        userBehaviorDataStore.setShowAddRemindAlarmGuideDialogInstant(now)
        GlobalNavigator.navigate(AlarmGuideDestination.route)
        debugLog(tag = TAG) { "GlobalNavigator.navigate(AlarmGuideDestination.route)" }

        return@withContext true
    }
}
