package com.nbpt.app.biz.admanager.nat1ve

typealias NativeAdPlace = NativeAdPlaceholder

enum class NativeAdPlaceholder(val placeName: String) {
    Test("test"),

    ExitApp("exit_app"),

    Home("home"),
    Info("info"),
    Settings("setting"),
    Permissions("permissions"),

    Bp("bp"),
    BpHistory("bp_list"),
    BpEditor("bp_record"),

    Hr("hr"),
    HrMeasure("hr_measure"),
    HrEditor("hr_editor"),
    HrHistory("hr_list"),

    Bs("bs"),
    BsEditor("bs_editor"),
    BsHistory("bs_list"),

    Bmi("bmi"),
    BmiResult("bmi_result"),

    Article("article"),

    Alarm("alarm"),
    AlarmEditor("alarm_editor"),

    AnrBp("result_bp"),
    AnrHr("result_hr"),

    Language("language"),
    Guide("guide"), ;

    fun isDialogPlace(): Boolean {
        return this in listOf(ExitApp)
    }
}

