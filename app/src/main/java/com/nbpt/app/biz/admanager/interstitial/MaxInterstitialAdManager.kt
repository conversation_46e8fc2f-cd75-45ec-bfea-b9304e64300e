@file:Suppress("ObjectPropertyName")

package com.nbpt.app.biz.admanager.interstitial

import android.app.Activity
import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxInterstitialAd
import com.nbpt.app.BuildConfig
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import com.nbpt.app.mainActivity
import com.nbpt.app.ui.screen.adloadingdialog.interAdLoadingAfterEvent
import com.nbpt.app.ui.screen.destinations.InterAdLoadingDialogDestination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "MaxInterstitialAdManager"

private const val _adReloadIntervalSeconds = 55 * 60


sealed interface MaxInterstitialAdLoadingStateEvent {
    data object Loaded : MaxInterstitialAdLoadingStateEvent
    data object FailedToLoad : MaxInterstitialAdLoadingStateEvent
}

sealed interface MaxInterstitialAdShowEvent {
    data object TryToShowing : MaxInterstitialAdShowEvent
    data object Showing : MaxInterstitialAdShowEvent
    data object Skip : MaxInterstitialAdShowEvent
    data object Invalid : MaxInterstitialAdShowEvent
    data object Hidden : MaxInterstitialAdShowEvent
}

class MaxInterstitialAdManager(
    private val context: Context,
    private val splashHelper: SplashManager,
    private val remoteConfig: RealRemoteConfig,
) {
    private val adConfig get() = remoteConfig.adConfig1

    private fun adKey(): String {
        return if (InterAdKeyController.useNextAdKey(adConfig.nextInterAdKeyActiveIntervalMinutes)) {
            debugLog(tag = TAG) { "adKey use key2" }
            adConfig.interAdKey2
        } else {
            debugLog(tag = TAG) { "adKey use key1" }
            adConfig.interAdKey
        }
    }

    private var _interstitialAd: MaxInterstitialAd? = null

    private val _lastCacheAdInstantSeconds = MutableStateFlow(0L)
    private val _lastShowAdInstantSeconds = MutableStateFlow(0L)
    private val _isLoadingAdFlow = MutableStateFlow(false)
    private val _currentActiveAdFrom = MutableStateFlow("")

    val adLoadingStateEvent = EventFlow<MaxInterstitialAdLoadingStateEvent>()

    private val _interstitialAdEventChannel = MutableSharedFlow<MaxInterstitialAdShowEvent>(0)
    val interstitialAdEventFlow: SharedFlow<MaxInterstitialAdShowEvent> get() = _interstitialAdEventChannel

    private val maxInterstitialAdListener = object : MaxAdListener, MaxAdRevenueListener {
        override fun onAdLoaded(ad: MaxAd) {
            debugLog(tag = TAG) { "onAdLoaded" }

            val now = nowInstant()

            _isLoadingAdFlow.update { false }
            _lastCacheAdInstantSeconds.update { now.epochSeconds }
            adLoadingStateEvent.send(MaxInterstitialAdLoadingStateEvent.Loaded)
        }

        override fun onAdDisplayed(ad: MaxAd) {
            debugLog(tag = TAG) { "onAdDisplayed" }

            val now = nowInstant()

            splashHelper.doSkipSplash(true)

            _lastShowAdInstantSeconds.update { now.epochSeconds }

            GlobalScope.launch(Dispatchers.Main.immediate) {
                val adFrom = _currentActiveAdFrom.first()
                if (adFrom.isNotEmpty()) {
                    debugLog(tag = TAG) { "ad_${adFrom}_inter_impress" }
                    logEventRecord("ad_${adFrom}_inter_impress")
                    logEventRecord("ad_inter_impress")
                }

                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Showing)
            }
        }

        override fun onAdHidden(ad: MaxAd) {
            debugLog(tag = TAG) { "onAdHidden" }


            GlobalScope.launch(Dispatchers.Main.immediate) {
                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Hidden)
                val adFrom = _currentActiveAdFrom.first()

                if (adFrom.isNotEmpty()) {
                    debugLog(tag = TAG) { "ad_${adFrom}_inter_close" }
                    logEventRecord("ad_${adFrom}_inter_close")
                }

                _currentActiveAdFrom.update { "" }
                loadInterstitialAd()
            }
        }

        override fun onAdClicked(ad: MaxAd) {
            debugLog(tag = TAG) { "onAdClicked" }

            GlobalScope.launch(Dispatchers.Main.immediate) {
                val adFrom = _currentActiveAdFrom.first()

                if (adFrom.isNotEmpty()) {
                    debugLog(tag = TAG) { "ad_${adFrom}_inter_click" }
                    logEventRecord("ad_${adFrom}_inter_click")
                    logEventRecord("ad_inter_click")
                }
            }
        }

        override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
            debugLog(tag = TAG) { "onAdLoadFailed" }

            GlobalScope.launch(Dispatchers.Main.immediate) {
                val adFrom = _currentActiveAdFrom.first()

                if (adFrom.isNotEmpty()) {
                    debugLog(tag = TAG) { "ad_${adFrom}_inter_load_failed" }
                    logEventRecord("ad_${adFrom}_inter_load_failed")
                }

                _currentActiveAdFrom.update { "" }
                _isLoadingAdFlow.update { false }
                adLoadingStateEvent.send(MaxInterstitialAdLoadingStateEvent.FailedToLoad)
            }

        }

        override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
            GlobalScope.launch(Dispatchers.Main.immediate) {
                val adFrom = _currentActiveAdFrom.first()
                if (adFrom.isNotEmpty()) {
                    debugLog(tag = TAG) { "ad_${adFrom}_inter_display_failed" }
                    logEventRecord("ad_${adFrom}_inter_display_failed")
                }
            }
        }

        override fun onAdRevenuePaid(ad: MaxAd) {
            debugLog(tag = TAG) { "$TAG onAdRevenuePaid" }

            GlobalScope.launch {
                if (!BuildConfig.DEBUG) {
                    val currentActiveAdFrom = _currentActiveAdFrom.first()

                    AnalyticsLogEvent.recordAdImpressionRevenue(ad, currentActiveAdFrom)
                    AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(ad)
                    AnalyticsLogEvent.recordAdImpression(ad)

                    AnalyticsLogEvent.roasReport(ad)
                }
            }

        }
    }

    private fun getAdShowIntervalSeconds(): Int {
        return adConfig.adShowIntervalSeconds
    }

    fun adLoadingTimeoutDuration() =
        adConfig.adLoadingTimeoutSeconds.toDuration(DurationUnit.SECONDS)

    fun initIfNeed(activity: Activity) {
        val adKey = adKey()

        if (_interstitialAd?.adUnitId != adKey) {
            _interstitialAd?.destroy()
            _interstitialAd = null
            _interstitialAd = MaxInterstitialAd(adKey, activity).apply {
                setListener(maxInterstitialAdListener)
                setRevenueListener(maxInterstitialAdListener)
            }
            loadInterstitialAd(skipInitIfNeeded = true)
        }
        if (_interstitialAd == null) {
            debugLog(tag = TAG) { "_interstitialAd init" }
            _interstitialAd = MaxInterstitialAd(adKey, activity).apply {
                setListener(maxInterstitialAdListener)
                setRevenueListener(maxInterstitialAdListener)
            }
            loadInterstitialAd()
        }
    }

    private fun loadInterstitialAd(skipInitIfNeeded: Boolean = false) {
        if (skipInitIfNeeded.not()) {
            val adKey = adKey()

            if (_interstitialAd?.adUnitId != adKey) {
                _interstitialAd?.destroy()
                _interstitialAd = null
                _interstitialAd = MaxInterstitialAd(adKey, mainActivity ?: context).apply {
                    setListener(maxInterstitialAdListener)
                    setRevenueListener(maxInterstitialAdListener)
                }
            }
        }

        _interstitialAd?.let {
            debugLog(tag = TAG) { "_interstitialAd?.let{}" }
            it.loadAd()
            _isLoadingAdFlow.update { true }
        }
    }

    private fun showInterstitialAd() {
        _interstitialAd?.let {
            it.showAd()
        }
    }

    fun tryToLoadAd() {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            val now = nowInstant()

            if (
                _interstitialAd?.isReady == true
                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
            ) { // has available ad cache
                debugLog(tag = TAG) { "loadAd has available ad cache" }
            } else { // need load new ad
                val isLoadingAd = _isLoadingAdFlow.first()

                if (!isLoadingAd) {
                    loadInterstitialAd()
                }
            }
        }
    }

    private var collectOnAdShowOrHiddenJob: Job? = null
    fun tryToShowAd(
        from: String? = null,
        whenShowingExecuteAction: Boolean = true,
//        onAdShowingOrSkip: (() -> Unit)? = null, // dialog use this
        onAdLoadingAfter: (() -> Unit)? = null, // screen use this
    ) {
        debugLog(tag = TAG) { "tryToShowAd from: $from" }

        GlobalScope.launch(Dispatchers.Main.immediate) {
            collectOnAdShowOrHiddenJob?.cancel()
            collectOnAdShowOrHiddenJob = null

            _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.TryToShowing)

            val now = nowInstant()
            if (
                _interstitialAd?.isReady == true
                && now.epochSeconds - _adReloadIntervalSeconds < _lastCacheAdInstantSeconds.first()
            ) { // has available ad cache

                val adShowIntervalSeconds = getAdShowIntervalSeconds()

                debugLog(tag = TAG) { "adShowIntervalSeconds: $adShowIntervalSeconds" }

                if (_lastShowAdInstantSeconds.first() + adShowIntervalSeconds <= now.epochSeconds) {
                    onAdLoadingAfter?.let {
                        interAdLoadingAfterEvent.send(onAdLoadingAfter)
                    }

                    if (from != null && from != "launch") {
                        GlobalNavigator.navigateImmediate {
                            navigate(InterAdLoadingDialogDestination(whenShowingExecuteAction).route)
                        }
                    }

                    from?.let {
                        delay(1700)
                        debugLog(tag = TAG) { "ad_${from}_inter_show" }
                        logEventRecord("ad_${from}_inter_show")
                        logEventRecord("ad_inter_show")
                        _currentActiveAdFrom.update { from }
                    }
                    showInterstitialAd()
                } else {
                    tryToPopInterAdDialog()
                    onAdLoadingAfter?.invoke()

                    if (from == "launch") {
                        delay(1200)
                    }

                    debugLog(tag = TAG) { "do not need show InterstitialAd. from: $from" }
                    _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Skip)
                }

            } else {
//                tryToPopInterAdDialog()
//                onAdShowingOrSkip?.invoke()
//                onAdHiddenOrSkip?.invoke()

                onAdLoadingAfter?.let {
                    interAdLoadingAfterEvent.send(onAdLoadingAfter)
                }

                if (from != null && from != "launch") {
                    GlobalNavigator.navigateImmediate {
                        navigate(InterAdLoadingDialogDestination(whenShowingExecuteAction).route)
                    }
                }

                val isLoadingAd = _isLoadingAdFlow.first()

                if (!isLoadingAd) {
                    debugLog(tag = TAG) { "loadInterstitialAd()" }

                    loadInterstitialAd()
                }

                debugLog(tag = TAG) { "interstitialAd non ready and isLoadingAd: $isLoadingAd" }

                delay(100)
                _interstitialAdEventChannel.emit(MaxInterstitialAdShowEvent.Invalid)
            }

//            collectOnAdShowOrHiddenJob = GlobalScope.launch(Dispatchers.IO) {
//                _interstitialAdEventChannel.take(2).collect {
//                    when (it) {
//                        MaxInterstitialAdShowEvent.Showing -> {
//                            tryToPopInterAdDialog()
//                            if (onAdShowingOrSkip != null) {
//                                async(Dispatchers.Main.immediate) {
//                                    onAdShowingOrSkip()
//                                }
//                            }
//                        }
//
//                        MaxInterstitialAdShowEvent.Hidden -> {
//                            tryToPopInterAdDialog()
//                            if (onAdHiddenOrSkip != null) {
//                                async(Dispatchers.Main.immediate) {
//                                    onAdHiddenOrSkip()
//                                }
//                            }
//                        }
//
//                        else -> {}
//                    }
//                }
//            }
        }
    }

    private suspend fun tryToPopInterAdDialog() {
        val currentDestination = GlobalNavigator.currentDestination()

        if (currentDestination is InterAdLoadingDialogDestination) {
            GlobalNavigator.navigate { navigateUp() }
        }
    }
}