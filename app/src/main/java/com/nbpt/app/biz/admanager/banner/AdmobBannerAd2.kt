package com.nbpt.app.biz.admanager.banner

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.logger.debugLog
import kotlinx.coroutines.launch
import org.koin.compose.koinInject

@Composable
fun AdmobBannerAd2(
    adPlace: BannerAdPlace,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val bannerAdManager: AdmobBannerAdManager = koinInject()
//    val adWidth = LocalConfiguration.current.screenWidthDp - (12 * 2)
    val adWidth = LocalConfiguration.current.screenWidthDp

    val adView by bannerAdManager.getAdViewFlow(adPlace).collectAsState()

    var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
    var hasAddAdView by remember { mutableStateOf(false) }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> scope.launch {
                bannerAdManager.resume(adPlace)
            }

            Lifecycle.Event.ON_RESUME -> scope.launch {
                bannerAdManager.resume(adPlace)
            }

            Lifecycle.Event.ON_PAUSE -> scope.launch {
                bannerAdManager.pause(adPlace)
            }

            Lifecycle.Event.ON_DESTROY -> bannerAdManager.destroy(adPlace)
            else -> {}
        }
    }

    DisposableEffect(Unit) {
        logEventRecord("ad_banner_show")

        onDispose {
            adContainer?.removeAllViews()
        }
    }

//    LaunchedEffect(adView) {
    LaunchedEffect(Unit) {
        if (adView == null) {
            debugLog(tag = "BANNER_AD") { "bannerAdManager.buildAd(adPlace, adWidth)" }
            bannerAdManager.buildAd(adPlace, adWidth, scope)
        }
    }

    val bannerLayoutModifier = modifier.height(64.dp)

    Box(
        modifier = bannerLayoutModifier.background(color = Color.White),
        contentAlignment = Alignment.Center
    ) {
        Crossfade(
            targetState = adView != null,
        ) { adViewNotNull ->
            if (adViewNotNull) {
                AndroidView(
                    factory = { context ->
                        FrameLayout(context).apply {
                            layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.WRAP_CONTENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )
                            runCatching {
                                debugLog(tag = "BANNER_AD") { "addView(adView)" }
                                (adView?.parent as? ViewGroup)?.removeAllViews()
                                addView(adView)
                                hasAddAdView = true
                            }
                        }.apply {
                            adContainer = this
                        }
                    },
                )
            } else {
                BannerAd2PlaceholderByLottie(modifier = Modifier.height(64.dp))
            }
        }
    }
}

@Composable
fun BannerAd2PlaceholderByLottie(
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        val composition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset("ad_banner_g2.json")
        )

        LottieAnimation(
            composition = composition,
            iterations = Int.MAX_VALUE,
            modifier = Modifier
                .fillMaxWidth(),
            contentScale = ContentScale.FillBounds
        )

        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .zIndex(1f)
                .padding(top = 4.dp, end = 6.dp)
                .background(MaterialTheme.colorScheme.primary, RoundedCornerShape(2.dp))
        ) {
            Text(
                " AD ",
                color = Color.White,
                fontSize = 9.5.sp,
                fontWeight = FontWeight.SemiBold,
                lineHeight = 10.sp,
                modifier = Modifier
                    .padding(vertical = 1.dp)
                    .scale(scaleY = 0.96f, scaleX = 1f)
            )
        }
    }

}


@Preview
@Composable
private fun BannerAdPreview() {
    AdmobBannerAd(adPlace = BannerAdPlace.TEST)
}
