package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

//language=json
private val json = """
    {
      "next_trigger_interval_min": 60
    }
""".trimIndent()

@JsonClass(generateAdapter = true)
@Keep
data class AddRemindAlarmGuideConfig(
    @Json(name = "next_trigger_interval_min")
    val nextTriggerIntervalMinute: Int = 60,
) {
    companion object {
        val Default = AddRemindAlarmGuideConfig()
    }
}
