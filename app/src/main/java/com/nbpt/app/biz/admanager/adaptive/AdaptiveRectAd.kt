package com.nbpt.app.biz.admanager.adaptive

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.ui.theme.Layout
import org.koin.compose.koinInject

@Composable
fun AdaptiveRectAd(
    pageType: AdaptiveAdPageType,
    bannerAdPlace: BannerAdPlace,
    nativeAdPlace: NativeAdPlace,
    modifier: Modifier = Modifier,
    remoteConfig: RealRemoteConfig = koinInject()
) {
    val adaptiveConfig =
        remember(remoteConfig.adaptiveRectAdConfig) { remoteConfig.adaptiveRectAdConfig }

    val adType = remember(pageType, adaptiveConfig) {
        when (pageType) {
            AdaptiveAdPageType.LIST -> adaptiveConfig.list
        }
    }

    when (adType) {
        RectAdDisplayType.BANNER -> {
            BannerAd(
                placeholder = bannerAdPlace,
                modifier = modifier
            )
        }

        RectAdDisplayType.NATIVE -> {
            NativeAd(
                place = nativeAdPlace,
                modifier = modifier
                    .padding(horizontal = Layout.bodyMargin)
                    .padding(vertical = Layout.gutter),
            )
        }

        else -> {
            // 默认使用 Banner 广告
            BannerAd(
                placeholder = bannerAdPlace,
                modifier = modifier
            )
        }
    }
}


@Composable
fun ListAdaptiveRectangleAd(
    bannerAdPlace: BannerAdPlace,
    nativeAdPlace: NativeAdPlace,
    modifier: Modifier = Modifier,
    remoteConfig: RealRemoteConfig = koinInject()
) {
    AdaptiveRectAd(
        pageType = AdaptiveAdPageType.LIST,
        bannerAdPlace = bannerAdPlace,
        nativeAdPlace = nativeAdPlace,
        modifier = modifier,
        remoteConfig = remoteConfig
    )
}
