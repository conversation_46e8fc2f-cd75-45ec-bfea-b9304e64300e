package com.nbpt.app.biz.admanager.nat1ve

import android.content.Context
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.logger.debugLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class MaxNativeAd(
    val ad: MaxAd,
    val adView: MaxNativeAdView?,
    val adLoader: MaxNativeAdLoader,
)

class MaxNativeAdManager(
    private val context: Context,
    private val splashManager: SplashManager,
    private val remoteConfig: RealRemoteConfig
) {

    @Suppress("PrivatePropertyName")
    private val TAG = "MaxNativeAdManager"
    private val adUnitNameLowercase = "native"

    private val binder: MaxNativeAdViewBinder = MaxNativeAdViewBinder
        .Builder(R.layout.layout_native_ad_content)
        .setTitleTextViewId(R.id.ad_headline)
        .setBodyTextViewId(R.id.ad_body)
        .setAdvertiserTextViewId(R.id.ad_advertiser)
        .setIconImageViewId(R.id.ad_app_icon)
        .setMediaContentViewGroupId(R.id.ad_media)
        .setOptionsContentViewGroupId(R.id.ad_options)
//        .setStarRatingContentViewGroupId(R.id.star_rating_view)
        .setCallToActionButtonId(R.id.ad_call_to_action)
        .build()

    private val binder2: MaxNativeAdViewBinder = MaxNativeAdViewBinder
        .Builder(R.layout.layout_native_ad_content_2)
        .setTitleTextViewId(R.id.ad_headline)
        .setBodyTextViewId(R.id.ad_body)
        .setAdvertiserTextViewId(R.id.ad_advertiser)
        .setIconImageViewId(R.id.ad_app_icon)
        .setMediaContentViewGroupId(R.id.ad_media)
        .setOptionsContentViewGroupId(R.id.ad_options)
//        .setStarRatingContentViewGroupId(R.id.star_rating_view)
        .setCallToActionButtonId(R.id.ad_call_to_action)
        .build()

    private val adFlowMap: MutableMap<NativeAdPlaceholder, MutableStateFlow<MaxNativeAd?>> =
        mutableMapOf()

    private fun adLoader(
        place: NativeAdPlaceholder
    ) = MaxNativeAdLoader(remoteConfig.adConfig1.nativeAdKey, context).apply {
        setRevenueListener {

            AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(it)
            AnalyticsLogEvent.recordAdImpressionRevenue(it, place.placeName)
            AnalyticsLogEvent.recordAdImpression(it)

            AnalyticsLogEvent.roasReport(it)
        }

        setNativeAdListener(object : MaxNativeAdListener() {
            override fun onNativeAdLoaded(loadedNativeAdView: MaxNativeAdView?, ad: MaxAd) {
                logEventRecord("ad_${adUnitNameLowercase}_load_success")
                debugLog(tag = TAG) { "onNativeAdLoaded" }
                adFlow(place).update { MaxNativeAd(ad, loadedNativeAdView, this@apply) }
            }

            override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                debugLog(tag = TAG) { "onNativeAdLoadFailed" }
            }

            override fun onNativeAdClicked(ad: MaxAd) {
                splashManager.doSkipSplash(true)
                logEventRecord("ad_${adUnitNameLowercase}_click")
                debugLog(tag = TAG) { "onNativeAdClicked" }
            }

            override fun onNativeAdExpired(nativeAd: MaxAd) {
            }
        })
    }

    fun adFlow(place: NativeAdPlaceholder): MutableStateFlow<MaxNativeAd?> {
        return adFlowMap.getOrPut(place) { MutableStateFlow(null) }
    }

    fun buildAd(place: NativeAdPlaceholder, context: Context, useNovelty: Boolean) {
        logEventRecord("ad_${adUnitNameLowercase}_load")
        debugLog(tag = TAG) { "buildAd(${place.placeName})" }

        val maxNativeAd = MaxNativeAdView(if (useNovelty) binder2 else binder, context)

        adLoader(place).apply {
            loadAd(maxNativeAd)
        }
    }

    fun destroy(place: NativeAdPlaceholder) {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            adFlowMap[place]?.first()?.run {
                adLoader.destroy(ad)
                adLoader.destroy()
            }
            adFlowMap[place]?.update { null }
        }
    }

    fun destroyAll() {
        GlobalScope.launch {
            adFlowMap.keys.forEach(::destroy)
        }
    }

    @Suppress("RedundantIf")
    fun needToBlockSomeClickAreas(ad: MaxAd): Boolean {
        return if (
            ad.networkName.contains("admob", ignoreCase = true)
            || ad.networkName.contains("facebook", ignoreCase = true)
        ) {
            true
        } else {
            false
        }
    }
}