package com.nbpt.app.biz.admanager.banner

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import org.koin.compose.koinInject

@Composable
fun MaxBannerAd2(
    placeholder: BannerAdPlaceholder,
    placeName: String = placeholder.placeName,
    modifier: Modifier = Modifier,
) {
    val bannerAdHelper: MaxBannerAdHelper = koinInject()

    val screenWidthDp = LocalConfiguration.current.screenWidthDp

    val context = LocalContext.current

    val bannerAd = bannerAdHelper.getBannerAd(context, placeholder, placeName)

    val hasCache by bannerAd.hasCacheFlow.collectAsState()

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> if (bannerAd.adView.isAttachedToWindow) bannerAd.startAutoRefresh()
            Lifecycle.Event.ON_STOP -> if (bannerAd.adView.isAttachedToWindow) bannerAd.stopAutoRefresh()
            Lifecycle.Event.ON_DESTROY -> if (placeholder != BannerAdPlaceholder.HOME) bannerAdHelper.destroy(placeholder)
            else -> {}
        }
    }

    var adPlaceholderLayout by remember { mutableStateOf<FrameLayout?>(null) }


    DisposableEffect(Unit) {
        logEventRecord("ad_${placeName}_banner_show")
        logEventRecord("ad_banner_show")

        onDispose {
            debugLog { "Banner onDispose adPlaceholderLayout ${adPlaceholderLayout.hashCode()}" }
            adPlaceholderLayout?.removeAllViews()
        }
    }

    val adHeightDp = if (screenWidthDp <= 600) 48.dp else 88.dp

    Surface(modifier = Modifier.height(adHeightDp + 12.dp * 2), color = Color.White) {

        val contentModifier = Modifier.height(adHeightDp)

        if (hasCache) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                AndroidView(
                    factory = {
                        FrameLayout(it).apply {
                            layoutParams = FrameLayout.LayoutParams(
                                ViewGroup.LayoutParams.WRAP_CONTENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )

                            setBackgroundColor(Color.White.toArgb())

                            runCatching {
                                (bannerAd.adView.parent as? ViewGroup)?.removeAllViews()

                                debugLog { "Banner addView adPlaceholderLayout ${this.hashCode()}" }
                                debugLog { "Banner addView(bannerAd.adView)" }
                                addView(bannerAd.adView)
                            }

                            adPlaceholderLayout = this
                        }
                    },
                    modifier = contentModifier.padding(horizontal = 22.dp)
                )
            }
        } else {
            Surface(
                shape = RoundedCornerShape12Dp,
                modifier = Modifier.padding(
                    vertical = Layout.gutter / 2,
                    horizontal = Layout.bodyMargin
                ),
            ) {
                BannerAd2PlaceholderByLottie(contentModifier)
            }
        }
    }
}
