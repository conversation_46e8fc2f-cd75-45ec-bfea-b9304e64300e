package com.nbpt.app.biz.admanager.nat1ve

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import org.koin.compose.koinInject

@Composable
fun NativeAd(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
    remoteConfig: RealRemoteConfig = koinInject()
) {
    val enabledNativeAdInControlPlace =
        remember(remoteConfig.adPlaceControl.enabled_home_native_ad) {
            remoteConfig.adPlaceControl.enabled_home_native_ad
        }

    val inNeedControlPlace = remember {
        place in listOf(
            NativeAdPlace.Home,
            NativeAdPlace.Info
        )
    }

    if (inNeedControlPlace && !enabledNativeAdInControlPlace) return

    val useNoveltyRectAdLayout = remoteConfig.rememberUseNoveltyRectAdLayout()

    if (useNoveltyRectAdLayout) {
        AdmobNativeAd2(
            place = place,
            modifier = modifier
        )
    } else {
        AdmobNativeAd(
            place = place,
            modifier = modifier
        )
    }
}