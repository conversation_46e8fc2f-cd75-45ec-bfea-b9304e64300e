package com.nbpt.app.biz.admanager.nat1ve

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.viewinterop.AndroidViewBinding
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.android.gms.ads.nativead.NativeAd
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.databinding.LayoutNativeAdContentGBinding
import org.koin.compose.koinInject

@Composable
fun AdmobNativeAd2(
    place: NativeAdPlace,
    modifier: Modifier = Modifier,
) {
    val localContext = LocalContext.current

    val nativeAdManager: AdmobNativeAdManager = koinInject()

    val nativeAd by nativeAdManager.getAdFlow(place).collectAsState()

    val isDialogPlace = remember { place.isDialogPlace() }

    debugLog(tag = "NATIVE_AD") { "nativeAd: $nativeAd" }

    var adContainer by remember { mutableStateOf<FrameLayout?>(null) }
    val nativeAdContentBinding by remember { mutableStateOf(localContext.nativeAdContentBinding()) }

    if (!isDialogPlace) {
        OnLifecycleEvent { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    debugLog(tag = "NATIVE_AD") { "Lifecycle.Event.ON_DESTROY call destroy() place: ${place.name}" }

                    nativeAdManager.destroy(place)
                }

                else -> {}
            }
        }
    } else {
        DisposableEffect(Unit) {
            onDispose {
                debugLog(tag = "NATIVE_AD") { "DisposableEffect onDispose call destroy() place: ${place.name}" }
                nativeAdManager.destroy(place)
            }
        }
    }

    val primaryColor = MaterialTheme.colorScheme.primary
    LaunchedEffect(nativeAd) {
        nativeAd?.let {
            debugLog(tag = "NATIVE_AD") { "nativeAdContentBinding.configure2(it, primaryColor)" }
            nativeAdContentBinding.configure2(it, primaryColor)
            adContainer?.removeAllViews()
            adContainer?.addView(nativeAdContentBinding.root)
        }
    }

    LaunchedEffect(Unit) {
        logEventRecord("ad_native_show")

        if (nativeAd == null) {
            debugLog(tag = "NATIVE_AD") { "nativeAdManager.buildAd(place)" }
            nativeAdManager.buildAd(place)
        }
    }

    Crossfade(
        targetState = nativeAd != null,
        modifier = modifier.then(
            if (nativeAd == null) Modifier.animateContentSize() else Modifier
        ),
    ) { hasNativeAd ->
        if (hasNativeAd) {
            Spacer(modifier = Modifier.height(138.dp))

            AndroidView(
                factory = { context ->
                    FrameLayout(context).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                    }.apply {
                        adContainer = this
                    }
                },
//                modifier = modifier
            )
        } else {
            AdmobNativeAdPlaceholder(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(138.dp)
            )
        }
    }
}

private fun Context.nativeAdContentBinding(): LayoutNativeAdContentGBinding {
    val layoutInflater = this.findActivity().layoutInflater

    return LayoutNativeAdContentGBinding.inflate(layoutInflater)
}


@Composable
private fun AdmobNativeAdPlaceholder(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = Modifier
            .zIndex(1f)
            .padding(top = 4.dp, start = 4.dp)
            .background(MaterialTheme.colorScheme.primary, RoundedCornerShape(2.dp))
    ) {
        Text(
            " AD ",
            color = Color.White,
            fontSize = 9.sp,
            fontWeight = FontWeight.SemiBold,
            lineHeight = 10.sp,
            modifier = Modifier.padding(vertical = 1.dp).scale(scaleY = 0.96f, scaleX = 1f)
        )
    }

    val composition by rememberLottieComposition(
        spec = LottieCompositionSpec.Asset("ad_native_admob.json")
    )

    LottieAnimation(
        composition = composition,
        modifier = modifier,
        iterations = Int.MAX_VALUE,
        contentScale = ContentScale.FillBounds
    )

}

private fun LayoutNativeAdContentGBinding.configure2(
    nativeAd: NativeAd,
    primaryColor: Color,
) {
    val nativeAdView = this.nativeAd

    root.setCardBackgroundColor(Color.White.toArgb())
    adCallToAction.setBackgroundColor(primaryColor.toArgb())
    tvAd.setBackgroundColor(primaryColor.toArgb())

    nativeAdView.mediaView = this.adMedia
    nativeAdView.headlineView = this.adHeadline
    nativeAdView.bodyView = this.adBody
    nativeAdView.callToActionView = this.adCallToAction
    nativeAdView.iconView = this.adAppIcon
    nativeAdView.advertiserView = this.adAdvertiser

    this.adHeadline.text = nativeAd.headline
    nativeAd.mediaContent?.let { this.adMedia.mediaContent = it }
    if (nativeAd.body == null) {
        debugLog(tag = "NATIVE_AD") { "this.adBody.visibility = View.INVISIBLE" }

        this.adBody.visibility = View.INVISIBLE
    } else {
        this.adBody.visibility = View.VISIBLE
        this.adBody.text = nativeAd.body
    }

    if (nativeAd.callToAction == null) {
        debugLog(tag = "NATIVE_AD") { "this.adCallToAction.visibility = View.INVISIBLE" }

        this.adCallToAction.visibility = View.INVISIBLE
    } else {
        this.adCallToAction.visibility = View.VISIBLE
        this.adCallToAction.text = nativeAd.callToAction
    }

    if (nativeAd.icon == null) {
        debugLog(tag = "NATIVE_AD") { "this.adAppIcon.visibility = View.INVISIBLE" }

        this.adAppIcon.visibility = View.INVISIBLE
    } else {
        this.adAppIcon.setImageDrawable(nativeAd.icon?.drawable)
        this.adAppIcon.visibility = View.VISIBLE
    }

    if (nativeAd.advertiser == null) {
        debugLog(tag = "NATIVE_AD") { "this.adAdvertiser.visibility = View.INVISIBLE" }

        this.adAdvertiser.visibility = View.INVISIBLE
    } else {
        this.adAdvertiser.text = nativeAd.advertiser
        this.adAdvertiser.visibility = View.VISIBLE
    }

    nativeAdView.setNativeAd(nativeAd)
}

@Preview
@Composable
private fun AdmobNativeAdPreview() {
    AdmobNativeAd(place = NativeAdPlace.Test)
}

@Preview
@Composable
private fun AdmobNativeAdPlaceholderPreview() {
    AdmobNativeAdPlaceholder()
}
