package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.nbpt.app.biz.admanager.adaptive.RectAdDisplayType
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
@Keep
data class SmartRectAdConfig(
    @Json(name = "home_top_bar") val homeTopBar: Int = RectAdDisplayType.NONE,
    @Json(name = "home_bottom_bar") val homeBottomBar: Int = RectAdDisplayType.BANNER,
    @Json(name = "home_content") val homeContent: Int = RectAdDisplayType.NATIVE,

    @Json(name = "result_top_bar") val resultTopBar: Int = RectAdDisplayType.BANNER,
    @<PERSON><PERSON>(name = "result_bottom_bar") val resultBottomBar: Int = RectAdDisplayType.NONE,
    @<PERSON><PERSON>(name = "result_content") val resultContent: Int = RectAdDisplayType.NATIVE,

    @Json(name = "feat_top_bar") val featTopBar: Int = RectAdDisplayType.BANNER,
    @<PERSON>son(name = "feat_bottom_bar") val featBottomBar: Int = RectAdDisplayType.NONE,
    @Json(name = "feat_content") val featContent: Int = RectAdDisplayType.NATIVE,
) {
    companion object {
        val Default = SmartRectAdConfig()

        //language=json
        val json = """
    {
      "home_top_bar": 0,
      "home_bottom_bar": 1,
      "home_content": 2,
      "result_top_bar": 1,
      "result_bottom_bar": 0,
      "result_content": 2,
      "feat_top_bar": 1,
      "feat_bottom_bar": 0,
      "feat_content": 2
    }
    """.trimIndent()
    }
}