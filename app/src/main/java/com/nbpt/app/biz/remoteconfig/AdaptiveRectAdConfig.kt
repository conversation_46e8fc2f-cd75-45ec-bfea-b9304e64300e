package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.nbpt.app.biz.admanager.adaptive.RectAdDisplayType
import com.squareup.moshi.JsonClass
import kotlin.text.trimIndent

/**
 * 自适应矩形广告配置
 * 参考 Applovin MRec AD 的命名方式
 *
 * 配置值说明：
 * - 1: 使用 Banner 广告
 * - 2: 使用 Native 广告
 */
@JsonClass(generateAdapter = true)
@Keep
data class AdaptiveRectAdConfig(
  val list: Int = RectAdDisplayType.NATIVE,
) {
  companion object {
    val Default = AdaptiveRectAdConfig()

    //language=json
    val json = """
    {
      "list": 1
    }
    """.trimIndent()
  }
}
