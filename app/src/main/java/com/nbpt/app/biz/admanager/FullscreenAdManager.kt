package com.nbpt.app.biz.admanager

import android.content.Context
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.mmkv.mmkvWithId
import kotlinx.datetime.Instant

class FullscreenAdManager(
  private val remoteConfig: RealRemoteConfig,
) {
  companion object {
    private const val TAG = "FullscreenAdManager"
    private const val KEY_LATEST_SHOW_AD_SUCCESS_INSTANT = "_latest_show_ad_success_instant"
  }

  private val mmkv by lazy { mmkvWithId(TAG) }


  private val adShowIntervalSeconds: Int
    get() {
      return if (remoteConfig.useLegacyAd) {
        remoteConfig.adConfig1.adShowIntervalSeconds
      } else {
        remoteConfig.adConfig2.adShowIntervalSeconds
      }
    }

  var latestShowAdSuccessInstant: Instant
    get() = mmkv.decodeLong(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, 0L).let(Instant::fromEpochSeconds)
    set(value) {
      mmkv.encode(KEY_LATEST_SHOW_AD_SUCCESS_INSTANT, value.epochSeconds)
    }

  fun isAdShowTimeInShowInterval(): Boolean {
    return nowInstant().epochSeconds - adShowIntervalSeconds < latestShowAdSuccessInstant.epochSeconds
  }
}