package com.nbpt.app.biz.admanager.adaptive

import com.squareup.moshi.Json

/**
 * 自适应矩形广告类型
 * 参考 Applovin MRec AD 的命名方式
 */
object RectAdDisplayType {
    const val NONE = 0

    /**
     * Banner 广告类型
     */
    const val BANNER = 1

    /**
     * Native 广告类型
     */
    const val NATIVE = 2
}


enum class AdaptiveAdPageType {
    LIST,
}

enum class SmartAdPageType {
    HOME_TOP_BAR,
    HOME_BOTTOM_BAR,
    HOME_CONTENT,
    RESULT_TOP_BAR,
    RESULT_BOTTOM_BAR,
    RESULT_CONTENT,
    FEAT_TOP_BAR,
    FEAT_BOTTOM_BAR,
    FEAT_CONTENT,
}