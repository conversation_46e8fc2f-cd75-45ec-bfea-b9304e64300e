//package com.nbpt.app.biz.analytics.tenjin
//
//import androidx.annotation.Keep
//import com.squareup.moshi.JsonClass
//
//@JsonClass(generateAdapter = true)
//@Keep
//data class TenjinAttr(
//    val adNetwork: String = "",
//    val campaignName: String = "",
//    val campaignId: String = "",
//    val siteId: String = "",
//    val creativeName: String = "",
//) {
//    fun isEmpty() = adNetwork.trim().isEmpty()
//
//    fun isOrganic() = adNetwork.lowercase() == "organic" || adNetwork.isEmpty()
//
//    companion object {
//        val Empty get() = TenjinAttr()
//    }
//}
