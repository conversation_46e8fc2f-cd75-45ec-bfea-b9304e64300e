package com.nbpt.app.biz.analytics.tenjin

import android.app.Activity
import com.applovin.mediation.MaxAd
import com.google.android.gms.ads.AdValue
import com.nbpt.app.BuildConfig
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.datetime.todayStartInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.AnalyticsDataStore
import com.nbpt.app.godlikeApplicationContext
import com.tenjin.android.TenjinSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.datetime.TimeZone

class TenjinHelper(
    private val analyticsDataStore: AnalyticsDataStore,
) {

    fun init(activity: Activity) {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            val tz = TimeZone.UTC

            val latestTenjinInitSuccessInstant =
                analyticsDataStore.getLatestTenjinInitSuccessInstant()

            val nowInstant = nowInstant()

            if (latestTenjinInitSuccessInstant
                    .todayStartInstant(tz)
                    .epochSeconds == nowInstant.todayStartInstant(tz).epochSeconds
            ) {
                // 和上次初始化相比还在同一天内, 不需要触发初始化
                debugLog { "$TAG do not need to do anything" }
            } else {
                initInternal(activity)
                analyticsDataStore.storeTenjinInitSuccessInstant(nowInstant())
            }
        }
    }

    private fun initInternal(activity: Activity): TenjinSDK? {
        debugLog { "$TAG init" }

        return TenjinSDK.getInstance(
            activity.applicationContext ?: godlikeApplicationContext,
            TENJIN_KEY
        )?.apply {
            setCacheEventSetting(true)
            setAppStore(TenjinSDK.AppStoreType.googleplay)
            connect()

            debugLog { "test_tenjin connect()" }
            logEventRecord("tenjin_init")
        }
    }

    private var _tenjin: TenjinSDK? = null

    private fun getTenjin(): TenjinSDK? {
        return if (_tenjin == null) {
            val tenjinInstance = TenjinSDK.getInstance(godlikeApplicationContext, TENJIN_KEY).apply {
                setCacheEventSetting(true)
            }
            _tenjin = tenjinInstance
            tenjinInstance
        } else {
            _tenjin
        }
    }

    fun roasApplovin(maxAd: MaxAd) {
        getTenjin()?.eventAdImpressionAppLovin(maxAd)
    }

    fun roasAdMob(adValue: AdValue, ad: Any?) {
        getTenjin()?.eventAdImpressionAdMob(adValue, ad)
    }

    companion object {
        private const val TAG = "TenjinHelper"

        private const val TENJIN_KEY = BuildConfig.TENJIN_SDK_KEY
    }
}
