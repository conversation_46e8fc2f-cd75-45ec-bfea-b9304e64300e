package com.nbpt.app.biz.admanager.interstitial

import android.app.Activity
import android.content.Context
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.AdConfig
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration


private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L

private var latestShowAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

class AdmobInterstitialAdManager(
    private val splashController: SplashManager,
    private val remoteConfig: RealRemoteConfig,
) {
    private data class InterAdConfig(
        val showIntervalSeconds: Int,
        val loadingTimeoutSeconds: Int
    )

    @Suppress("PrivatePropertyName")
    private val TAG = "InterstitialAdManager"

    private val config: AdConfig
        get() = remoteConfig.adConfig2

    private val adKey: String
        get() {
            return if (InterAdKeyController.useNextAdKey(config.nextInterAdKeyActiveIntervalMinutes)) {
                debugLog(tag = TAG) { "adKey use key2" }
                config.interAdKey2
            } else {
                debugLog(tag = TAG) { "adKey use key1" }
                config.interAdKey
            }
        }

    private var interstitialAd: InterstitialAd? = null
    private var isLoadingAd = false
    private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

    private val latestActiveAdPlaceNameFlow = MutableStateFlow<String?>(null)

    private val interstitialAdConfig: InterAdConfig
        get() = config.let {
            InterAdConfig(
                showIntervalSeconds = it.adShowIntervalSeconds,
                loadingTimeoutSeconds = it.adLoadingTimeoutSeconds
            )
        }

    sealed interface AdLoadingStateEvent {
        data object TimeOut : AdLoadingStateEvent
        data object Loaded : AdLoadingStateEvent
        data object FailedToLoad : AdLoadingStateEvent
    }

    sealed interface AdShowStateEvent {
        data object Finish : AdShowStateEvent
        data object Showing : AdShowStateEvent
        data object FailedToShow : AdShowStateEvent
        data object SkipToShow : AdShowStateEvent
    }

    val adLoadingStateEventFlow = EventFlow<AdLoadingStateEvent>()
    val adShowStateEventFlow = EventFlow<AdShowStateEvent>()

    val adShowStateFlow = MutableStateFlow(false)

    private var sendLoadingTimeOutJob: Job? = null

    fun tryToLoadAd(activity: Activity) {
        debugLog(tag = TAG) { "tryToLoadAd" }

        if (isLoadingAd) return

        if (isAdAvailable()) {
            debugLog(tag = TAG) { "hasAdAvailable" }
        } else {
            debugLog(tag = TAG) { "noAdAvailable" }

            loadAd(activity)
        }
    }

    private fun loadAd(context: Context) {
        sendLoadingTimeOutJob?.cancel()
        sendLoadingTimeOutJob = null
        sendLoadingTimeOutJob = GlobalScope.launch {
            delay(interstitialAdConfig.loadingTimeoutSeconds.toDuration(DurationUnit.SECONDS))
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
            adLoadingStateEventFlow.send(AdLoadingStateEvent.TimeOut)
        }

        if (isLoadingAd) {
            debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
            return
        }

        debugLog(tag = TAG) { "loadAd" }

        isLoadingAd = true

        InterstitialAd.load(
            context,
            adKey,
            AdRequest.Builder().build(),
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    debugLog(tag = TAG) { "onAdLoaded" }

                    ad.onPaidEventListener = OnPaidEventListener { adValue ->
                        val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
                        val adFormat = "interstitial"
                        val adUnitId = ad.adUnitId

                        AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
                        AnalyticsLogEvent.recordAdImpressionRevenue(
                            adValue,
                            adSourceName,
                            adFormat,
                            latestActiveAdPlaceNameFlow.value ?: ""
                        )
                        AnalyticsLogEvent.recordAdImpression(
                            adValue,
                            adSourceName,
                            adFormat,
                            adUnitId
                        )

                        AnalyticsLogEvent.roasReport(adValue, ad)
                    }

                    interstitialAd = ad
                    isLoadingAd = false
                    latestLoadAdSuccessInstant = nowInstant()

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AdLoadingStateEvent.Loaded)
                    debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

//                    logEventRecord("ad_${adUnitNameLowercase}_load_success")
                    logEventRecord("ad_interstitial_load_success")
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    debugLog(tag = TAG) { "onAdFailedToLoad" }

                    isLoadingAd = false

                    sendLoadingTimeOutJob?.cancel()
                    sendLoadingTimeOutJob = null
                    adLoadingStateEventFlow.send(AdLoadingStateEvent.FailedToLoad)
                }
            }
        )

        logEventRecord("ad_interstitial_load")
    }

    private fun isAdAvailable(): Boolean {
        return interstitialAd != null && checkAdIsValidAtCachePeriod()
    }

    private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
        val secondsDifference: Long =
            nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
        return secondsDifference < adCachePeriodSeconds
    }

    private fun isAdShowTimeInShowInterval(): Boolean {
        val adShowIntervalSeconds = interstitialAdConfig.showIntervalSeconds

        return nowInstant().epochSeconds - adShowIntervalSeconds < latestShowAdSuccessInstant.epochSeconds
    }

    private fun showAd(activity: Activity) {
        debugLog(tag = TAG) { "showAd" }

        logEventRecord("ad_interstitial_show")

        interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                interstitialAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AdShowStateEvent.Finish)
                GlobalScope.launch {
                    delay(2000)
                    adShowStateFlow.emit(false)
                }
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
                interstitialAd = null
                loadAd(activity)
                adShowStateEventFlow.send(AdShowStateEvent.FailedToShow)
                adShowStateFlow.update { false }
            }

            override fun onAdShowedFullScreenContent() {
                latestShowAdSuccessInstant = nowInstant()
                adShowStateEventFlow.send(AdShowStateEvent.Showing)
                adShowStateFlow.update { true }
                debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
            }

            override fun onAdClicked() {
                splashController.doSkipSplash(true)
                logEventRecord("ad_interstitial_click")
            }

            override fun onAdImpression() {
                logEventRecord("ad_interstitial_impress")
            }
        }

        interstitialAd?.show(activity)
    }

    suspend fun tryToShowAd(
        activity: Activity,
        adPlaceName: String? = null,
        onReadyShowAd: (() -> Unit)? = null
    ) = withContext(Dispatchers.Main.immediate) {
        debugLog(tag = TAG) { "tryToShowAd" }

        adPlaceName?.let {
            latestActiveAdPlaceNameFlow.update { "inter_$adPlaceName" }
        }

        if (isAdShowTimeInShowInterval()) {
            debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
            adShowStateEventFlow.send(AdShowStateEvent.SkipToShow)
        } else { // over the show interval, need to show ad
            onReadyShowAd?.invoke()
            debugLog(tag = TAG) { "over the show interval, need to show ad" }
            if (isAdAvailable()) { // cache available
                debugLog(tag = TAG) { "cache available" }
                delay(1_000)
                showAd(activity)
            } else { // cache not available
                debugLog(tag = TAG) { "cache not available" }
                loadAd(activity)
            }
        }
    }
}