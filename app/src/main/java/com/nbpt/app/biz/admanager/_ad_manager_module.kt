package com.nbpt.app.biz.admanager

import com.nbpt.app.biz.admanager.appopen.AdmobAppOpenAdManager
import com.nbpt.app.biz.admanager.appopen.MaxAppOpenAdHelper
import com.nbpt.app.biz.admanager.banner.AdmobBannerAdManager
import com.nbpt.app.biz.admanager.banner.MaxBannerAdHelper
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.nat1ve.AdmobNativeAdManager
import com.nbpt.app.biz.admanager.nat1ve.MaxNativeAdManager
import org.koin.core.module.dsl.singleOf
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val adManagerModule = module {
    singleOf(::MaxInterstitialAdManager)
    singleOf(::MaxBannerAdHelper)
    singleOf(::MaxNativeAdManager)
    singleOf(::AdmobInterstitialAdManager)
    viewModelOf(::AdmobInterstitialAdViewModel)
    singleOf(::AdmobBannerAdManager)
    singleOf(::AdmobNativeAdManager)
    singleOf(::FullscreenAdManager)
    singleOf(::AdmobAppOpenAdManager)
    singleOf(::MaxAppOpenAdHelper)
}
