package com.nbpt.app.biz.admanager

import com.nbpt.app.biz.admanager.appopen.AdmobAppOpenAdManager
import com.nbpt.app.biz.admanager.banner.AdmobBannerAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.nat1ve.AdmobNativeAdManager
import org.koin.core.module.dsl.singleOf
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val adManagerModule = module {
    singleOf(::AdmobInterstitialAdManager)
    viewModelOf(::AdmobInterstitialAdViewModel)
    singleOf(::AdmobBannerAdManager)
    singleOf(::AdmobNativeAdManager)
    singleOf(::FullscreenAdManager)
    singleOf(::AdmobAppOpenAdManager)
}
