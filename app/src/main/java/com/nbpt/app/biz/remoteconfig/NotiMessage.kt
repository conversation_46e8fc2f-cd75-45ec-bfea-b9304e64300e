@file:Suppress("PropertyName")

package com.nbpt.app.biz.remoteconfig

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

//language=json
private val json_r = """
{
  "messages": [
    {
      "sid": 1,
      "title": "Blood Pressure Log Time ⏰",
      "content": "Log your blood pressure now for a healthier you!",
      "typeId": 1
    },
    {
      "sid": 2,
      "title": "Daily Blood Sugar Check 🍬",
      "content": "Snap a quick record of your blood sugar levels.",
      "typeId": 1
    },
    {
      "sid": 3,
      "title": "🚨 Heart Health Alert",
      "content": "Measure your heart rate for a wellness boost.",
      "typeId": 1
    }
  ]
}
""".trimIndent()

//language=json
private val json_a = """
{
  "messages": [
    {
      "sid": 1,
      "title": "📝 Understanding Hypertension",
      "content": "Learn the basics of hypertension.",
      "typeId": 2
    },
    {
      "sid": 2,
      "title": "Master Your Blood Pressure 🩺",
      "content": "Explore habits for healthy blood pressure.",
      "typeId": 2
    },
    {
      "sid": 3,
      "title": "Blood Sugar Measurement Guide",
      "content": "👆 Tap for precise blood sugar tips!",
      "typeId": 2
    },
    {
      "sid": 4,
      "title": "The latest in blood sugar tech ✨",
      "content": "Discover blood sugar innovations now 🔍",
      "typeId": 2
    },
    {
      "sid": 5,
      "title": "❤️ Decode Heart Rate Mysteries!",
      "content": "Uncover Heart Rate Factors in a Click!",
      "typeId": 2
    },
    {
      "sid": 6,
      "title": "🤔 Wondered about your pulse?",
      "content": "Here are some tips on healthy heart rate.",
      "typeId": 2
    }
  ]
}
""".trimIndent()

@Keep
@JsonClass(generateAdapter = true)
data class RepeatNotiMessage(
    val sid: Int = 0,
    val title: String,
    val content: String,
    val typeId: Int = -1,
)

@Keep
@JsonClass(generateAdapter = true)
data class RepeatNotiGroup(
    val messages: List<RepeatNotiMessage>
) {

    companion object {
        val Test1 = RepeatNotiGroup(
            listOf(
                RepeatNotiMessage(1, "Test1_111", "111111", 1),
                RepeatNotiMessage(2, "Test1_222", "222222", 1),
                RepeatNotiMessage(3, "Test1_333", "333333", 1),
                RepeatNotiMessage(4, "Test1_444", "444444", 1),
                RepeatNotiMessage(5, "Test1_555", "555555", 1),
            )
        )

        val Test2 = RepeatNotiGroup(
            listOf(
                RepeatNotiMessage(1, "Test2_111", "111111", 2),
                RepeatNotiMessage(2, "Test2_222", "222222", 2),
                RepeatNotiMessage(3, "Test2_333", "333333", 2),
                RepeatNotiMessage(4, "Test2_444", "444444", 2),
                RepeatNotiMessage(5, "Test2_555", "555555", 2),
            )
        )
    }

    @Json(ignore = true)
    val sortedMessages get() = messages.sortedBy { it.sid }
}

//language=json
private val _json = """
    {
      "first_push_delay_min": 90,
      "repeat_push_interval_min": 30
    }
""".trimIndent()

@Keep
@JsonClass(generateAdapter = true)
data class RepeatNotiPushStrategy(
    @Json(name = "first_push_delay_min") val firstPushDelayMinutes: Int = 90,
    @Json(name = "repeat_push_interval_min") val repeatIntervalOfMinutes: Int = 30,
)
