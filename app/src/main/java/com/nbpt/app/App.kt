package com.nbpt.app

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import com.nbpt.app.androidcomponent.DetectionScheduler
import com.nbpt.app.androidcomponent.HandleRemoteViewsEventReceiver
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmReceiver
import com.nbpt.app.androidcomponent.fixednoti.FixedNotiService
import com.nbpt.app.androidcomponent.lockreceiver.ScreenSwitchReceiver
import com.nbpt.app.androidcomponent.notireceiver.RepeatNotiRemoveEventReceiver
import com.nbpt.app.androidcomponent.tickreceiver.TimeTickReceiver
import com.nbpt.app.bi.BiReporter
import com.nbpt.app.bi.reportPageOnStartEvent
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

var godlikeApplicationContext: Context? = null

class App : Application() {
    override fun onCreate() {
        super.onCreate()
        godlikeApplicationContext = this
        FixedNotiService.startService(this)
        configureFirebaseMessagingTopicIfNeeded(this)
        TimeTickReceiver.registerTimeTickReceiver(this)
        ScreenSwitchReceiver.register(this)
        RepeatNotiRemoveEventReceiver.register(this)
        RemindToRecordAlarmReceiver.register(this)
        HandleRemoteViewsEventReceiver.register(this)
        registerActivityLifecycleCallbacks(AppActivityLifecycleCallbacks)
        DetectionScheduler(this).scheduleDetectionWork()
    }

    object AppActivityLifecycleCallbacks : ActivityLifecycleCallbacks, KoinComponent {

        private val biReporter: BiReporter by inject()

        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            // No longer need to initialize Max ads
        }

        override fun onActivityStarted(activity: Activity) {
            val activityFullName = activity::class.java.name
            if (activityFullName != MainActivity::class.java.name) {
                GlobalScope.launch(Dispatchers.Main) {
                    biReporter.reportPageOnStartEvent(activityFullName)
                }
            }
        }

        override fun onActivityResumed(activity: Activity) {
        }

        override fun onActivityPaused(activity: Activity) {
        }

        override fun onActivityStopped(activity: Activity) {
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        }

        override fun onActivityDestroyed(activity: Activity) {
        }
    }
}
