package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.data.db.model.BsRecordsNoteEntity
import com.nbpt.app.data.pojo.BsRecordsNote
import kotlinx.coroutines.flow.Flow

@Dao
interface BsRecordsNoteDao {

    @Query("SELECT * FROM bs_records_notes ORDER BY rank DESC")
    fun fetchAllNotesFlow(): Flow<List<BsRecordsNoteEntity>>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun add(notes: List<BsRecordsNoteEntity>): List<Long>

    @Delete
    suspend fun delete(notes: List<BsRecordsNoteEntity>): Int

}

private val pendingPreInsetNotes by lazy {
    listOf(
        "Get up",
        "At bedtime",
        "After taking medicine",
        "Before eating",
        "Walk",
        "Sit",
        "Lying",
        "Period",
        "During period",
        "After meal",
        "Before meal",
        "Before workout",
        "After workout",
    ).map { BsRecordsNote(content = it).toDbEntity() }
}

suspend fun BsRecordsNoteDao.preInsertNotes() {
    add(pendingPreInsetNotes)
}
