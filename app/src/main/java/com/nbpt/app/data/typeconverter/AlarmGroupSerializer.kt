package com.nbpt.app.data.typeconverter

import com.nbpt.app.androidcomponent.alarm.remindtorecord.AlarmGroup
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import com.ramcosta.composedestinations.navargs.DestinationsNavTypeSerializer
import com.ramcosta.composedestinations.navargs.NavTypeSerializer

@NavTypeSerializer
object AlarmGroupSerializer : DestinationsNavTypeSerializer<AlarmGroup> {
    override fun fromRouteString(routeStr: String): AlarmGroup {
        return routeStr.toObj() ?: AlarmGroup.Default
    }

    override fun toRouteString(value: AlarmGroup): String {
        return value.toJsonString() ?: ""
    }
}
