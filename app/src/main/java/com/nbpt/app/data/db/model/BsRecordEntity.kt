package com.nbpt.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.nbpt.app.data.adt.BsRecordState
import kotlinx.datetime.Instant

@Entity(
    tableName = "bs_records",
    indices = [Index(value = ["uuid"], unique = true)]
)
data class BsRecordEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val uuid: String,
    val instant: Instant,
    val mmolL: Float,
    val state: BsRecordState,
    val notes: List<String>,
)

val BsRecordEntity.notesText: String
    get() = buildString {
        notes.forEach { note ->
            append("#$note ")
        }
    }