package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.nbpt.app.data.pojo.BpRecordsNote
import kotlinx.coroutines.flow.Flow

@Dao
interface BpRecordsNoteDao {

    @Query("SELECT * FROM bp_records_notes ORDER BY rank DESC")
    fun fetchAllNotesFlow(): Flow<List<BpRecordsNoteEntity>>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun add(notes: List<BpRecordsNoteEntity>): List<Long>

    @Delete
    suspend fun delete(notes: List<BpRecordsNoteEntity>): Int

}

private val pendingPreInsetNotes by lazy {
    listOf(
        "Right",
        "Left",
        "Lying",
        "Sitting",
        "Period",
        "After meal",
        "Before meal",
        "After medication"
    ).map { BpRecordsNote(content = it).toDbEntity() }
}

suspend fun BpRecordsNoteDao.preInsertNotes() {
    add(pendingPreInsetNotes)
}
