package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.data.db.model.HrRecordsNoteEntity
import com.nbpt.app.data.pojo.HrRecordsNote
import kotlinx.coroutines.flow.Flow

@Dao
interface HrRecordsNoteDao {

    @Query("SELECT * FROM hr_records_notes ORDER BY rank DESC")
    fun fetchAllNotesFlow(): Flow<List<HrRecordsNoteEntity>>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun add(notes: List<HrRecordsNoteEntity>): List<Long>

    @Delete
    suspend fun delete(notes: List<HrRecordsNoteEntity>): Int

}

private val pendingPreInsetNotes by lazy {
    listOf(
        "Left",
        "Right",
        "Get up",
        "At bedtime",
        "After taking medicine",
        "Before eating",
        "Walk",
        "Sit",
        "Lying",
        "During period",
        "Before workout",
        "After workout",
    ).map { HrRecordsNote(content = it).toDbEntity() }
}

suspend fun HrRecordsNoteDao.preInsertNotes() {
    add(pendingPreInsetNotes)
}
