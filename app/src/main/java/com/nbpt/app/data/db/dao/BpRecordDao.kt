package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.data.adt.BpInfoName
import com.nbpt.app.data.db.model.BpRecordEntity
import kotlinx.coroutines.flow.Flow
import java.util.UUID

@Dao
interface BpRecordDao {

    @Query("SELECT * FROM bp_records ORDER BY instant DESC")
    fun fetchAllRecordsFlow(): Flow<List<BpRecordEntity>>

    @Query("SELECT * FROM bp_records ORDER BY instant")
    fun fetchAllRecordsInstantAscendFlow(): Flow<List<BpRecordEntity>>

    @Query("SELECT * FROM bp_records ORDER BY instant DESC LIMIT :count")
    fun fetchRecordsFlow(count: Int): Flow<List<BpRecordEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(record: BpRecordEntity): Long

    @Query("DELETE FROM bp_records WHERE uuid=:uuid")
    suspend fun delete(uuid: String): Int
    
    @Query("SELECT * FROM bp_records ORDER BY instant DESC LIMIT 1")
    suspend fun latestRecord(): BpRecordEntity?
}
