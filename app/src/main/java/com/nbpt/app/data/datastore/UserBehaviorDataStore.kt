package com.nbpt.app.data.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.nbpt.app.common.mmkv.mmkvWithId
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.pojo.BmiCalculatorCache
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Instant

private const val TAG = "UserBehaviorDataStore"

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = TAG)

class UserBehaviorDataStore(
    val context: Context
) {

    private val mmkv = mmkvWithId(TAG)

    // -------------------------------------------------------------------------------------------
    val isGuideFinish = booleanPreferencesKey("isGuideFinish")

    suspend fun setGuideFinish() {
        context.dataStore.edit {
            it[isGuideFinish] = true
        }
    }

    suspend fun getGuideFinish(): Boolean {
        return context.dataStore.data.map {
            it[isGuideFinish]
        }.first() ?: false
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    val isLanguageFinish = booleanPreferencesKey("isLanguageFinish")

    suspend fun setLanguageFinish() {
        context.dataStore.edit {
            it[isLanguageFinish] = true
        }
    }

    suspend fun getLanguageFinish(): Boolean {
        return context.dataStore.data.map {
            it[isLanguageFinish]
        }.first() ?: false
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val firstTimeLaunchAppInstantKey = longPreferencesKey("firstTimeLaunchAppInstant")

    suspend fun get1stTimeLaunchAppInstant(): Instant {
        return context.dataStore.data.map { pref ->
            pref[firstTimeLaunchAppInstantKey] ?: 0L
        }.first().let(Instant::fromEpochSeconds)
    }

    suspend fun firstTimeLaunchApp(instant: Instant) {
        context.dataStore.edit { pref ->
            pref[firstTimeLaunchAppInstantKey] = instant.epochSeconds
        }
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val latestRatingInstantKey = longPreferencesKey("latestRatingInstantKey")

    suspend fun latestRatingInstant(): Instant? {
        return context.dataStore.data.map { pref ->
            pref[latestRatingInstantKey]
        }.first()?.let(Instant::fromEpochSeconds)
    }

    suspend fun setRatingInstant(instant: Instant) {
        context.dataStore.edit { pref ->
            pref[latestRatingInstantKey] = instant.epochSeconds
        }
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val showRatingTimesKey = intPreferencesKey("showRatingTimes")

    suspend fun getRatingTimes() =
        context.dataStore.data.map { pref -> pref[showRatingTimesKey] }.first() ?: 0

    suspend fun setRatingTimes(times: Int) {
        context.dataStore.edit { pref ->
            pref[showRatingTimesKey] = times
        }
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val latestShowAddRemindAlarmGuideDialogInstant =
        longPreferencesKey("latestShowAddRemindAlarmGuideDialogInstant")

    suspend fun latestShowAddRemindAlarmGuideDialogInstant(): Instant? =
        context.dataStore.data.map { pref ->
            pref[latestShowAddRemindAlarmGuideDialogInstant]?.let {
                Instant.fromEpochSeconds(it)
            }
        }.first()

    suspend fun setShowAddRemindAlarmGuideDialogInstant(instant: Instant) {
        context.dataStore.edit { pref ->
            pref[latestShowAddRemindAlarmGuideDialogInstant] = instant.epochSeconds
        }
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val hasShowAddRecordGuideLay =
        booleanPreferencesKey("hasShowAddRecordGuideLay")

    suspend fun hasShowAddRecordGuideLay(): Boolean =
        context.dataStore.data.map { pref ->
            pref[hasShowAddRecordGuideLay]
        }.first() ?: false

    suspend fun setHasShowAddRecordGuideLay(b: Boolean) {
        context.dataStore.edit { pref ->
            pref[hasShowAddRecordGuideLay] = b
        }
    }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val hasConfigureDefaultHeartRateRecordNotes =
        booleanPreferencesKey("hasConfigureDefaultRecordNotes")

    suspend fun hasConfigureDefaultRecordNotes(): Boolean =
        context.dataStore.data.map { pref ->
            pref[hasConfigureDefaultHeartRateRecordNotes]
        }.first() ?: false

    suspend fun setHasConfigureDefaultRecordNotes(b: Boolean) {
        context.dataStore.edit { pref ->
            pref[hasConfigureDefaultHeartRateRecordNotes] = b
        }
    }

    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val isUnlockBpRecordStatKey = "isUnlockBpRecordStatKey"

    var isUnlockBpRecordStat
        get() = mmkv.decodeBool(isUnlockBpRecordStatKey, false)
        set(value) {
            mmkv.encode(isUnlockBpRecordStatKey, value)
        }

    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val bmiCalculatorCacheKey = "bmiCalculatorCacheKey"
    var bmiCalculatorCache: BmiCalculatorCache
        get() = mmkv
            .decodeParcelable(bmiCalculatorCacheKey, BmiCalculatorCache::class.java)
            ?: BmiCalculatorCache()
        set(value) {
            mmkv.encode(bmiCalculatorCacheKey, value)
        }
    // -------------------------------------------------------------------------------------------

    // -------------------------------------------------------------------------------------------
    private val bsUnitKey = "bsUnitKey"
    var bsUnit: BsUnit
        get() = mmkv.decodeString(bsUnitKey, BsUnit.SI.name)?.let { BsUnit.valueOf(it) }
            ?: BsUnit.SI
        set(value) {
            mmkv.encode(bsUnitKey, value.name)
        }
    // -------------------------------------------------------------------------------------------
}
