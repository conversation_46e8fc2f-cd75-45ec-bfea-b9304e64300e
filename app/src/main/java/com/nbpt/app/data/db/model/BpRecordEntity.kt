package com.nbpt.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.Instant
import java.io.Serializable

@Entity(
    tableName = "bp_records",
    indices = [Index(value = ["uuid"], unique = true)]
)
data class BpRecordEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val uuid: String,
    val instant: Instant,
    val systolic: Int,
    val diastolic: Int,
    val pulse: Int,
    val notes: List<String>,
)

val BpRecordEntity.notesText: String
    get() = buildString {
        notes.forEach { note ->
            append("#$note ")
        }
    }
