package com.nbpt.app.data.adt

import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.Color
import com.nbpt.app.R
import com.nbpt.app.ui.theme.AppTheme
import kotlinx.parcelize.Parcelize

sealed class HrStatus(
    val color: Color,
    @StringRes val titleStringId: Int,
    @StringRes val descriptionStringId: Int,
    @StringRes val analysisStringId: Int,
) : Parcelable {

    @Parcelize
    object Fast : HrStatus(
        AppTheme.HrStatusColor.fast,
        R.string.text_hr_fast,
        R.string.text_hr_fast_range,
        R.string.text_hr_fast_analysis
    )

    @Parcelize
    object Normal : HrStatus(
        AppTheme.HrStatusColor.normal,
        R.string.text_hr_normal,
        R.string.text_hr_normal_range,
        R.string.text_hr_normal_analysis
    )

    @Parcelize
    object Slow : HrStatus(
        AppTheme.HrStatusColor.slow,
        R.string.text_hr_slow,
        R.string.text_hr_slow_range,
        R.string.text_hr_slow_analysis
    )

    companion object {

        fun from(bpm: Int): HrStatus {
            return if (bpm < 60) {
                Slow
            } else if (bpm in 60..100) {
                Normal
            } else {
                Fast
            }
        }

        val entries = listOf(
            Fast,
            Normal,
            Slow,
        )
    }
}
