package com.nbpt.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.data.pojo.BpRecordsNote

@Entity(
    tableName = "bp_records_notes",
    indices = [Index(value = ["content"], unique = true)]
)
data class BpRecordsNoteEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val rank: Double,
    val content: String
) {
    fun toPojo(): BpRecordsNote {
        return BpRecordsNote(rank, content)
    }
}

data class BpRecordsNoteContent(
    val content: String
)
