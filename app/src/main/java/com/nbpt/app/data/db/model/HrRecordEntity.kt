package com.nbpt.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.datetime.Instant

@Entity(
    tableName = "hr_records",
    indices = [Index(value = ["uuid"], unique = true)]
)
data class HrRecordEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val uuid: String,
    val instant: Instant,
    val gender: Int,
    val age: Int,
    val heartRateBpm: Int,
    val notes: List<String>,
)

val HrRecordEntity.notesText: String
    get() = buildString {
        notes.forEach { note ->
            append("#$note ")
        }
    }
