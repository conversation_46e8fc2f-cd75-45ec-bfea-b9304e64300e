package com.nbpt.app.data.typeconverter

import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import com.nbpt.app.data.pojo.BpRecordsNote
import com.ramcosta.composedestinations.navargs.DestinationsNavTypeSerializer
import com.ramcosta.composedestinations.navargs.NavTypeSerializer

@NavTypeSerializer
object BpRecordsNoteSerializer : DestinationsNavTypeSerializer<BpRecordsNote> {
    override fun fromRouteString(routeStr: String): BpRecordsNote {
        return routeStr.toObj() ?: BpRecordsNote(content = "")
    }

    override fun toRouteString(value: BpRecordsNote): String {
        return value.toJsonString() ?: ""
    }
}
