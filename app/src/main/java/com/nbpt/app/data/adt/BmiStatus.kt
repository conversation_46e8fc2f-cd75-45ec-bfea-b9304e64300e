package com.nbpt.app.data.adt

import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.Color
import com.nbpt.app.R
import com.nbpt.app.ui.theme.AppTheme
import kotlinx.parcelize.Parcelize

sealed class BmiStatus(
    val color: Color,
    @StringRes val titleStringId: Int,
    @StringRes val descriptionStringId: Int,
) : Parcelable {

    @Parcelize
    data object VerySeverelyUnderweight : BmiStatus(
        AppTheme.BmiStatusColor.verySeverelyUnderweight,
        R.string.text_bmi_very_severely_underweight,
        R.string.text_bmi_very_severely_underweight_range,
    )

    @Parcelize
    data object SeverelyUnderweight : BmiStatus(
        AppTheme.BmiStatusColor.severelyUnderweight,
        R.string.text_bmi_severely_underweight,
        R.string.text_bmi_severely_underweight_range,
    )

    @Parcelize
    data object Underweight : BmiStatus(
        AppTheme.BmiStatusColor.underweight,
        R.string.text_bmi_underweight,
        R.string.text_bmi_underweight_range,
    )

    @Parcelize
    data object Normal : BmiStatus(
        AppTheme.BmiStatusColor.normal,
        R.string.text_bmi_normal,
        R.string.text_bmi_normal_range,
    )

    @Parcelize
    data object Overweight : BmiStatus(
        AppTheme.BmiStatusColor.overweight,
        R.string.text_bmi_overweight,
        R.string.text_bmi_overweight_range,
    )

    @Parcelize
    data object ObeseClassI : BmiStatus(
        AppTheme.BmiStatusColor.obeseClassI,
        R.string.text_bmi_obese_class_i,
        R.string.text_bmi_obese_class_i_range,
    )

    @Parcelize
    data object ObeseClassII : BmiStatus(
        AppTheme.BmiStatusColor.obeseClassII,
        R.string.text_bmi_obese_class_ii,
        R.string.text_bmi_obese_class_ii_range,
    )

    @Parcelize
    data object ObeseClassIII : BmiStatus(
        AppTheme.BmiStatusColor.obeseClassIII,
        R.string.text_bmi_obese_class_iii,
        R.string.text_bmi_obese_class_iii_range,
    )

    companion object {
        fun from(
            bmi: Float,
        ): BmiStatus {
            return when {
                bmi < 16.0 -> VerySeverelyUnderweight
                bmi >= 16.0 && bmi < 17.0 -> SeverelyUnderweight
                bmi >= 17.0 && bmi < 18.5 -> Underweight
                bmi >= 18.5 && bmi < 25.0 -> Normal
                bmi >= 25.0 && bmi < 30.0 -> Overweight
                bmi >= 30.0 && bmi < 35.0 -> ObeseClassI
                bmi >= 35.0 && bmi < 40.0 -> ObeseClassII
                bmi >= 40 -> ObeseClassIII
                else -> Normal
            }
        }

        val entries = listOf(
            VerySeverelyUnderweight,
            SeverelyUnderweight,
            Underweight,
            Normal,
            Overweight,
            ObeseClassI,
            ObeseClassII,
            ObeseClassIII,
        )
    }
}
