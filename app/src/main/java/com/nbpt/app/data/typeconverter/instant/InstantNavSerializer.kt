package com.nbpt.app.data.typeconverter.instant

import com.ramcosta.composedestinations.navargs.DestinationsNavTypeSerializer
import com.ramcosta.composedestinations.navargs.NavTypeSerializer
import kotlinx.datetime.Instant

@NavTypeSerializer
object InstantNavSerializer : DestinationsNavTypeSerializer<Instant> {
    override fun fromRouteString(routeStr: String): Instant {
        return InstantConverter.longToInstant(routeStr.toLong()) ?: Instant.fromEpochMilliseconds(0)
    }

    override fun toRouteString(value: Instant): String {
        return InstantConverter.instantToLong(value).toString()
    }
}
