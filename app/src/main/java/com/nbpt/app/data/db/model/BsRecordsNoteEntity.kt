package com.nbpt.app.data.db.model

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.nbpt.app.data.pojo.HrRecordsNote

@Entity(
    tableName = "bs_records_notes",
    indices = [Index(value = ["content"], unique = true)]
)
data class BsRecordsNoteEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val rank: Double,
    val content: String
) {
    fun toPojo(): HrRecordsNote {
        return HrRecordsNote(rank, content)
    }
}

data class BsRecordsNoteContent(
    val content: String
)
