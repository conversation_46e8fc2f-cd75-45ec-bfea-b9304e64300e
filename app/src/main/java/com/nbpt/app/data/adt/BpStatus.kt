package com.nbpt.app.data.adt

import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.Color
import com.nbpt.app.R
import com.nbpt.app.ui.theme.AppTheme
import kotlinx.parcelize.Parcelize

sealed class BpStatus(
    val color: Color,
    @StringRes val titleStringId: Int,
    @StringRes val descriptionStringId: Int,
    @StringRes val analysisStringId: Int,
) : Parcelable {

    @Parcelize
    data object Hypotension : BpStatus(
        AppTheme.BpStatusColor.hypotension,
        R.string.text_bp_hypotension,
        R.string.text_bp_hypotension_range,
        R.string.text_bp_hypotension_analysis
    )

    @Parcelize
    data object Normal : BpStatus(
        AppTheme.BpStatusColor.normal,
        R.string.text_bp_normal,
        R.string.text_bp_normal_range,
        R.string.text_bp_normal_analysis
    )

    @Parcelize
    data object Elevated : BpStatus(
        AppTheme.BpStatusColor.elevated,
        R.string.text_bp_elevated,
        R.string.text_bp_elevated_range,
        R.string.text_bp_elevated_analysis
    )

    @Parcelize
    data object HypertensionS1 : BpStatus(
        AppTheme.BpStatusColor.hypertensionS1,
        R.string.text_bp_hypertension_s1,
        R.string.text_bp_hypertension_s1_range,
        R.string.text_hypertension_s1_analysis
    )

    @Parcelize
    data object HypertensionS2 : BpStatus(
        AppTheme.BpStatusColor.hypertensionS2,
        R.string.text_bp_hypertension_s2,
        R.string.text_bp_hypertension_s2_range,
        R.string.text_hypertension_s2_analysis
    )

    @Parcelize
    data object Hypertensive : BpStatus(
        AppTheme.BpStatusColor.hypertensive,
        R.string.text_bp_hypertensive,
        R.string.text_bp_hypertensive_range,
        R.string.text_bp_hypertensive_analysis
    )

    companion object {

        //    Hypotension
        //    SYS <90 or DIA < 60
        //
        //    Normal
        //    SYS 90-119  and DIA 60-79
        //
        //    Elevated
        //    SYS 120-129 and  DIA 60-79
        //
        //    Hypertension·Stage1
        //    SYS 130-139 or DIA 80-89
        //
        //    Hypertension·Stage2
        //    SYS 140-180 or DIA 90-120
        //
        //    Hypertensive
        //    SYS > 180 or DIA > 120

        fun from(
            systolic: Int,
            diastolic: Int
        ): BpStatus {
            return when {
                systolic < 90 || diastolic < 60 -> Hypotension
                systolic > 180 || diastolic > 120 -> Hypertensive
                systolic in 140..180 || diastolic in 90..120 -> HypertensionS2
                systolic in 130..139 || diastolic in 80..89 -> HypertensionS1
                systolic in 120..129 && diastolic in 60..79 -> Elevated
                systolic in 90..119 && diastolic in 60..79 -> Normal
                else -> Normal
            }
        }

        val entries = listOf(
            Hypotension,
            Normal,
            Elevated,
            HypertensionS1,
            HypertensionS2,
            Hypertensive,
        )
    }
}
