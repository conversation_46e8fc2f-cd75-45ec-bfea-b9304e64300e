package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.db.model.BsRecordEntity
import kotlinx.coroutines.flow.Flow
import java.util.UUID
import kotlin.random.Random

@Dao
interface BsRecordDao {
    @Query("SELECT * FROM bs_records ORDER BY instant DESC")
    fun fetchAllRecordsFlow(): Flow<List<BsRecordEntity>>

    @Query("SELECT * FROM bs_records ORDER BY instant")
    fun fetchAllRecordsInstantAscendFlow(): Flow<List<BsRecordEntity>>

    @Query("SELECT * FROM bs_records ORDER BY instant DESC LIMIT :count")
    fun fetchRecordsFlow(count: Int): Flow<List<BsRecordEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(record: BsRecordEntity): Long

    @Query("DELETE FROM bs_records WHERE uuid=:uuid")
    suspend fun delete(uuid: String): Int

    @Query("SELECT * FROM bs_records ORDER BY instant DESC LIMIT 1")
    suspend fun latestRecord(): BsRecordEntity?
}

fun BsRecordDao.mockData(): List<BsRecordEntity> {
    val list = mutableListOf<BsRecordEntity>()
    repeat(100) {
        list.add(
            BsRecordEntity(
                id = it.toLong(),
                uuid = UUID.randomUUID().toString(),
                instant = nowInstant(),
                mmolL = Random.nextDouble(3.9, 8.0).toFloat(),
                state = BsRecordState.Default,
                notes = listOf("lalala")
            )
        )
    }

    return list
}