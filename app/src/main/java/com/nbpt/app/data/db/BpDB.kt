package com.nbpt.app.data.db

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.nbpt.app.bi.BiPendingReportEventEntity
import com.nbpt.app.bi.BiReporterPendingReportEventDao
import com.nbpt.app.data.db.dao.*
import com.nbpt.app.data.db.model.*
import com.nbpt.app.data.typeconverter.bsrecordstate.BsRecordStateConverter
import com.nbpt.app.data.typeconverter.instant.InstantConverter
import com.nbpt.app.data.typeconverter.stringlist.StringListConverter

@Database(
    exportSchema = true,
    version = 2,
    entities = [
        BpRecordEntity::class,
        BpRecordsNoteEntity::class,
        HrRecordEntity::class,
        HrRecordsNoteEntity::class,
        BsRecordEntity::class,
        BsRecordsNoteEntity::class,
        BiPendingReportEventEntity::class,
    ],
    autoMigrations = [
        AutoMigration(from = 1, to = 2),
    ],
)
@TypeConverters(
    InstantConverter::class,
    StringListConverter::class,
    BsRecordStateConverter::class,
)
abstract class BpDB : RoomDatabase() {

    abstract fun bpRecordDao(): BpRecordDao
    abstract fun bpRecordsNoteDao(): BpRecordsNoteDao
    abstract fun hrRecordDao(): HrRecordDao
    abstract fun hrRecordsNoteDao(): HrRecordsNoteDao
    abstract fun bsRecordDao(): BsRecordDao
    abstract fun bsRecordsNoteDao(): BsRecordsNoteDao

    abstract fun biReporterPendingReportEventDao(): BiReporterPendingReportEventDao

}
