package com.nbpt.app.data.typeconverter.stringlist

import androidx.room.TypeConverter
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj

@Suppress("unused")
object StringListConverter {

    @TypeConverter
    fun toList(value: String?): List<String> {
        return value?.toObj<List<String>>() ?: emptyList()
    }

    @TypeConverter
    fun fromList(list: List<String?>?): String {
        return list.toJsonString() ?: ""
    }
}
