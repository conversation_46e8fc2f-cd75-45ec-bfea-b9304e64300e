package com.nbpt.app.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.nbpt.app.data.db.model.HrRecordEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface HrRecordDao {

    @Query("SELECT * FROM hr_records ORDER BY instant DESC")
    fun fetchAllRecordsFlow(): Flow<List<HrRecordEntity>>

    @Query("SELECT * FROM hr_records ORDER BY instant DESC LIMIT :count")
    fun fetchRecordsFlow(count: Int): Flow<List<HrRecordEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun upsert(record: HrRecordEntity): Long

    @Query("DELETE FROM hr_records WHERE uuid=:uuid")
    suspend fun delete(uuid: String): Int

    @Query("SELECT * FROM hr_records ORDER BY instant DESC LIMIT 1")
    suspend fun latestRecord(): HrRecordEntity?
}
