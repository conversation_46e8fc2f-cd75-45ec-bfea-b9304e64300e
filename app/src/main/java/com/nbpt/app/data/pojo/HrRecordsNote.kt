package com.nbpt.app.data.pojo

import android.os.Parcelable
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.data.db.model.*
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class HrRecordsNote(
    val rank: Double = nowInstant().epochSeconds.toDouble(),
    val content: String
) : Parcelable {
    fun toDbEntity(): HrRecordsNoteEntity {
        return HrRecordsNoteEntity(
            rank = rank,
            content = content
        )
    }
}
