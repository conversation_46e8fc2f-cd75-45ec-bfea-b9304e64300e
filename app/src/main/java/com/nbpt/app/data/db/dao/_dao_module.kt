package com.nbpt.app.data.db.dao

import com.nbpt.app.data.db.BpDB
import org.koin.dsl.module

val daoModule = module {
    single {
        get<BpDB>().bpRecordDao()
    }
    single {
        get<BpDB>().bpRecordsNoteDao()
    }
    single {
        get<BpDB>().hrRecordDao()
    }
    single {
        get<BpDB>().hrRecordsNoteDao()
    }
    single {
        get<BpDB>().bsRecordDao()
    }
    single {
        get<BpDB>().bsRecordsNoteDao()
    }
}
