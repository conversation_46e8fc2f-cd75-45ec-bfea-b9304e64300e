package com.nbpt.app.data.typeconverter.bsrecordstate

import com.nbpt.app.data.adt.BsRecordState
import com.ramcosta.composedestinations.navargs.DestinationsNavTypeSerializer
import com.ramcosta.composedestinations.navargs.NavTypeSerializer

@NavTypeSerializer
object BsRecordStateSerializer : DestinationsNavTypeSerializer<BsRecordState> {
    override fun fromRouteString(routeStr: String): BsRecordState {
        return try {
            BsRecordState.valueOf(routeStr.toInt()) ?: BsRecordState.Default
        } catch (e: Exception) {
            BsRecordState.Default
        }
    }

    override fun toRouteString(value: BsRecordState): String {
        return value.sid.toString()
    }
}
