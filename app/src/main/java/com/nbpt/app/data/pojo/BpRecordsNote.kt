package com.nbpt.app.data.pojo

import android.os.Parcelable
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize

@JsonClass(generateAdapter = true)
@Parcelize
data class BpRecordsNote(
    val rank: Double = nowInstant().epochSeconds.toDouble(),
    val content: String
) : Parcelable {
    fun toDbEntity(): BpRecordsNoteEntity {
        return BpRecordsNoteEntity(
            rank = rank,
            content = content
        )
    }
}
