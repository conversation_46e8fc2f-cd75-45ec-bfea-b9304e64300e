package com.nbpt.app.data.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant

private const val TAG = "AnalyticsDataStore"

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = TAG)

class AnalyticsDataStore(
    private val context: Context,
) {

    // ------------------------------------------------------------------------------------------
//    private val tenjinAttrKey = stringPreferencesKey("tenjinAttr")
//
//    suspend fun getTenjinAttr(): TenjinAttr {
//        return context.dataStore.data.map {
//            it[tenjinAttrKey]
//        }.first()?.toObj<TenjinAttr>() ?: TenjinAttr.Empty
//    }
//
//    suspend fun saveTenjinAttr(attr: TenjinAttr) {
//        attr.toJsonString()?.let { attrJson ->
//            context.dataStore.edit { pref ->
//                pref[tenjinAttrKey] = attrJson
//            }
//        }
//    }
    // ------------------------------------------------------------------------------------------


    // ------------------------------------------------------------------------------------------
    private val latestTenjinInitSuccessInstantKey =
        longPreferencesKey("latestTenjinInitSuccessInstant")

    suspend fun getLatestTenjinInitSuccessInstant(): Instant {
        return context.dataStore.data.map {
            (it[latestTenjinInitSuccessInstantKey]) ?: 0L
        }.first().let(Instant::fromEpochSeconds)
    }

    suspend fun storeTenjinInitSuccessInstant(instant: Instant) {
        context.dataStore.edit {
            it[latestTenjinInitSuccessInstantKey] = instant.epochSeconds
        }
    }
    // ------------------------------------------------------------------------------------------


    // ------------------------------------------------------------------------------------------
    private val isTenjinRequestedKey = booleanPreferencesKey("isTenjinRequested")
    private val isTenjinRequestedFlow = context.dataStore.data
        .map { it[isTenjinRequestedKey] ?: false }

    fun doingTenjinRequestForEventRecord(block: () -> Unit) {
        GlobalScope.launch(Dispatchers.Main) {
            if (!isTenjinRequestedFlow.first()) {
                block()
                context.dataStore.edit {
                    it[isTenjinRequestedKey] = true
                }
            }
        }
    }
    // ------------------------------------------------------------------------------------------


    // ------------------------------------------------------------------------------------------
    private val isTenjinRequestResultKey = booleanPreferencesKey("isTenjinRequestResult")
    private val isTenjinRequestResultFlow = context.dataStore.data
        .map { it[isTenjinRequestResultKey] ?: false }

    fun doneTenjinRequestResultForEventRecord(block: () -> Unit) {
        GlobalScope.launch(Dispatchers.Main) {
            if (!isTenjinRequestResultFlow.first()) {
                block()
                context.dataStore.edit {
                    it[isTenjinRequestResultKey] = true
                }
            }
        }
    }
    // ------------------------------------------------------------------------------------------


    // ------------------------------------------------------------------------------------------
    private val totalAdRevenueKey = doublePreferencesKey("totalAdRevenue")

    suspend fun getTotalAdRevenue(): Double {
        return context.dataStore.data.map {
            it[totalAdRevenueKey]
        }.first() ?: 0.0
    }

    suspend fun storeTotalAdRevenue(newTotalRevenue: Double) {
        context.dataStore.edit {
            it[totalAdRevenueKey] = newTotalRevenue
        }
    }
    // ------------------------------------------------------------------------------------------
}
