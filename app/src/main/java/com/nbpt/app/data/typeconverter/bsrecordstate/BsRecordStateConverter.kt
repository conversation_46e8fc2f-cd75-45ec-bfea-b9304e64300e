package com.nbpt.app.data.typeconverter.bsrecordstate

import androidx.room.TypeConverter
import com.nbpt.app.data.adt.BsRecordState

object BsRecordStateConverter {
    @TypeConverter
    fun intToState(value: Int?): BsRecordState = try {
        BsRecordState.valueOf(value!!) ?: BsRecordState.Default
    } catch (e: Exception) {
        BsRecordState.Default
    }

    @TypeConverter
    fun stateToInt(state: BsRecordState?): Int? = state?.sid
}
