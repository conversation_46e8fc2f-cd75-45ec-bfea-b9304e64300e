package com.nbpt.app.data.adt

import android.os.Parcelable
import androidx.annotation.StringRes
import androidx.compose.ui.graphics.Color
import com.nbpt.app.R
import com.nbpt.app.ui.theme.AppTheme
import kotlinx.parcelize.Parcelize

private val _entries = listOf(
    BsStatus.Low,
    BsStatus.Normal,
    BsStatus.PreDiabetes,
    BsStatus.Diabetes
)

val BsStatus.Companion.entries
    get() = _entries


sealed class BsStatus(
    val color: Color,
    @StringRes val titleStringId: Int,
    @StringRes val descriptionStringIdForSi: Int,
    @StringRes val descriptionStringIdForNonSi: Int,
) : Parcelable {

    @Parcelize
    data object Low : BsStatus(
        AppTheme.BsStatusColor.low,
        R.string.text_bs_low,
        R.string.text_bs_low_si_range,
        R.string.text_bs_low_non_si_range,
    )

    @Parcelize
    data object Normal : BsStatus(
        AppTheme.BsStatusColor.normal,
        R.string.text_bs_normal,
        R.string.text_bs_normal_si_range,
        R.string.text_bs_normal_non_si_range,
    )

    @Parcelize
    data object PreDiabetes : BsStatus(
        AppTheme.BsStatusColor.preDiabetes,
        R.string.text_bs_pre_diabetes,
        R.string.text_bs_pre_diabetes_si_range,
        R.string.text_bs_pre_diabetes_non_si_range,
    )

    @Parcelize
    data object Diabetes : BsStatus(
        AppTheme.BsStatusColor.diabetes,
        R.string.text_bs_diabetes,
        R.string.text_bs_diabetes_si_range,
        R.string.text_bs_diabetes_non_si_range,
    )

    companion object {
//        val entries = listOf(
//            Low,
//            Normal,
//            PreDiabetes,
//            Diabetes
//        )

        //    <string name="text_bs_low_si_range"><![CDATA[< 4.0]]></string>
        //    <string name="text_bs_normal_si_range"><![CDATA[4.0 – 5.5]]></string>
        //    <string name="text_bs_pre_diabetes_si_range"><![CDATA[5.5 – 7.0]]></string>
        //    <string name="text_bs_diabetes_si_range"><![CDATA[> 7.0]]></string>
        fun fromMmolL(
            mmolL: Float
        ): BsStatus {
            return when {
                mmolL < 4f -> Low
                mmolL >= 4f && mmolL < 5.5f -> Normal
                mmolL >= 5.5f && mmolL < 7f -> PreDiabetes
                mmolL >= 7f -> Diabetes
                else -> Normal
            }
        }

        //    <string name="text_bs_low_non_si_range"><![CDATA[< 72.0]]></string>
        //    <string name="text_bs_normal_non_si_range"><![CDATA[72.0 – 99.0]]></string>
        //    <string name="text_bs_pre_diabetes_non_si_range"><![CDATA[99.0 – 126.0]]></string>
        //    <string name="text_bs_diabetes_non_si_range"><![CDATA[> 126.0]]></string>
        fun fromMgDl(
            mgDl: Float
        ): BsStatus {
            return when {
                mgDl < 72f -> Low
                mgDl >= 72f && mgDl < 99f -> Normal
                mgDl >= 99f && mgDl < 126f -> PreDiabetes
                mgDl >= 126f -> Diabetes
                else -> Normal
            }
        }
    }
}
