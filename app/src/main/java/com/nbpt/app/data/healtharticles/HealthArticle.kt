@file:Suppress("MoveVariableDeclarationIntoWhen")

package com.nbpt.app.data.healtharticles

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.nbpt.app.R
import com.nbpt.app.Support
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import java.util.Locale

enum class HealthArticleType(@StringRes val descriptionStringId: Int) {
    HR(R.string.text_heart_rate),
    BP(R.string.text_blood_pressure),
    BS(R.string.text_blood_sugar),
}

@Parcelize
data class HealthArticle(
    val sid: Int,
    val title: String,
    val content: String,
    @DrawableRes val titleIllustrationResId: Int? = null
) : Parcelable {

    val imgRes get() = when(sid) {
        1 -> R.drawable.img_article_item_a
        2 -> R.drawable.img_article_item_b
        3 -> R.drawable.img_article_item_c
        4 -> R.drawable.img_article_item_d
        5 -> R.drawable.img_article_item_e
        6 -> R.drawable.img_article_item_f
        7 -> R.drawable.img_article_item_g
        8 -> R.drawable.img_article_item_h
        9 -> R.drawable.img_article_item_a
        else -> R.drawable.img_article_item_a
    }
}

object HealthArticles {

    suspend fun fetch(locale: Locale = Locale.getDefault()):Map<HealthArticleType, List<HealthArticle>> = withContext(Dispatchers.IO) {
        val compatibleLanguage = Support.compatibleLanguage(locale)

        when (compatibleLanguage) {
            Support.En -> HealthArticlesEn.fetch()
            Support.Ar -> HealthArticlesAr.fetch()
            Support.De -> HealthArticlesDe.fetch()
            Support.Fr -> HealthArticlesFr.fetch()
            Support.Ko -> HealthArticlesKo.fetch()
            Support.Ms -> HealthArticlesMs.fetch()
            Support.Pt -> HealthArticlesPt.fetch()
            Support.Ja -> HealthArticlesJa.fetch()
            Support.Th -> HealthArticlesTh.fetch()
            Support.Tr -> HealthArticlesTr.fetch()
            Support.Es -> HealthArticlesEs.fetch()
            Support.It -> HealthArticlesIt.fetch()
            Support.In -> HealthArticlesIn.fetch()
            Support.Vi -> HealthArticlesVi.fetch()
            Support.Zh_HK -> HealthArticlesZhHK.fetch()
            Support.Zh_TW -> HealthArticlesZhTW.fetch()
            else -> HealthArticlesEn.fetch()
        }
    }
}
