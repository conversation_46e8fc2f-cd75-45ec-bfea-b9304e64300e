package com.nbpt.app

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordRingtoneAndNotifyManager
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object NotificationActionNavigator : KoinComponent {

    private const val TAG = "NotificationActionNavigator"
    private const val EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE = "${TAG}_noti_nav_destination"
    private const val EXTRA_KEY_NOTI_ID = "${TAG}_noti_id"
    private const val EXTRA_KEY_NOTI_WITH_RINGTONE = "${TAG}_noti_with_ringtone"
    private const val EXTRA_KEY_CLICK_FOR_EVENT_RECORD = "${TAG}_click_for_event_record"


    private val context: Context by inject()
    private val splashManager: SplashManager by inject()
    private val remindToRecordRingtoneAndNotifyManager: RemindToRecordRingtoneAndNotifyManager by inject()


    private val androidNotificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        context.getSystemService(NotificationManager::class.java)
    } else {
        null
    }

    fun handleIntentAction(
        intent: Intent?,
        isActivityInForeground: Boolean = false
    ) {
        val withRingtone = intent?.getBooleanExtra(EXTRA_KEY_NOTI_WITH_RINGTONE, false) ?: false
        if (withRingtone) {
            remindToRecordRingtoneAndNotifyManager.cancelRingtoneIfNeeded()
        }

        val route = intent?.getStringExtra(EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE)
        val notiId = intent?.getIntExtra(EXTRA_KEY_NOTI_ID, -1) ?: -1
        val clickEventRecord = intent?.getStringExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORD)
        if (route.isNullOrEmpty()) {
            splashManager.doSkipSplash(false)
            return
        }

        if (!clickEventRecord.isNullOrEmpty()) {
            logEventRecord(clickEventRecord)
        }

        if (notiId != -1) {
            androidNotificationManager?.cancel(notiId)
        }

        val isFinalRouteToHome = route.startsWith("home", ignoreCase = true)


        when {
            isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
                    popBackStack(
                        HomeDestination.route,
                        false
                    )
                    popBackStack()
                    navigate(route)
                }
            }

            isFinalRouteToHome && !isActivityInForeground -> {
                splashManager.doSkipSplash(true)
                GlobalNavigator.navigate {
                    popBackStack(HomeDestination.route, false)
                    popBackStack()
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            !isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
                    navigate(route)
                }
            }

            !isFinalRouteToHome && !isActivityInForeground -> {
                splashManager.doSkipSplash(true)
                GlobalNavigator.navigate {
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            else -> {}
        }
    }

    fun createNavigateIntent(
        context: Context,
        notificationId: Int,
        route: String,
        clickEventRecord: String?,
        flags: Int = pendingIntentDefaultFlags,
        withRingtone: Boolean = false,
    ): PendingIntent {
        val navIntent = Intent(context, MainActivity::class.java).apply {
            putExtra(EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE, route)
            putExtra(EXTRA_KEY_NOTI_ID, notificationId)
            putExtra(EXTRA_KEY_NOTI_WITH_RINGTONE, withRingtone)
            clickEventRecord?.let { _ ->
                putExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORD, clickEventRecord)
            }
        }

        return PendingIntent.getActivity(
            context,
            notificationId,
            navIntent,
            flags
        )
    }
}
