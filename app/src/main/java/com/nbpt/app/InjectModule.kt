package com.nbpt.app

import com.nbpt.app.androidcomponent.androidComponentModule
import com.nbpt.app.bi.biModule
import com.nbpt.app.biz.admanager.adManagerModule
import com.nbpt.app.biz.analytics.analyticsModule
import com.nbpt.app.biz.bizModule
import com.nbpt.app.biz.heartrate.heartRateModule
import com.nbpt.app.biz.rating.ratingModule
import com.nbpt.app.biz.remoteconfig.firebaseRemoteConfigModule
import com.nbpt.app.common.commonComponentModule
import com.nbpt.app.data.datastore.dataStoreModule
import com.nbpt.app.data.db.dao.daoModule
import com.nbpt.app.data.db.dbModule
import com.nbpt.app.ui.screen.viewModelsModule
import org.koin.core.module.Module

val injectModules: List<Module> = listOf(
    viewModelsModule,
    dbModule,
    daoModule,
    commonComponentModule,
    dataStoreModule,
    adManagerModule,
    analyticsModule,
    ratingModule,
    androidComponentModule,
    firebaseRemoteConfigModule,
    bizModule,
    heartRateModule,
    biModule
)
