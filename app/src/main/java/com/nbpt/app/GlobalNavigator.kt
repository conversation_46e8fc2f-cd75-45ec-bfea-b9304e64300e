package com.nbpt.app

import android.annotation.SuppressLint
import androidx.navigation.NavHostController
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import com.ramcosta.composedestinations.spec.DestinationSpec
import com.ramcosta.composedestinations.utils.currentDestinationFlow
import com.ramcosta.composedestinations.utils.destination
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext

typealias GlobalNavigateBlock = suspend NavHostController.() -> Unit

object GlobalNavigator {

    @SuppressLint("StaticFieldLeak")
    private lateinit var navController: NavHostController
    private lateinit var lifecycleScope: CoroutineScope

    private val navEvent = EventFlow<GlobalNavigateBlock>()

    fun navigate(navBlock: GlobalNavigateBlock) {
        debugLog(tag = "GlobalNavigator") { "send navBlock: ${navBlock.hashCode()}" }
        navEvent.send(navBlock)
    }

    suspend fun navigateImmediate(navBlock: GlobalNavigateBlock) = withContext(Dispatchers.Main.immediate) {
        runCatching {
            navBlock.invoke(navController)
        }
    }

    fun navigate(route: String) {
        debugLog(tag = "GlobalNavigator") { "navigate() route: $route" }

        navigate {
            navigate(route)
        }
    }

    suspend fun currentDestination(): DestinationSpec<*> {
        return navController.currentDestinationFlow.first()
    }

    fun previousDestination(): DestinationSpec<*>? {
        return navController.previousBackStackEntry?.destination()
    }

    fun configure(
        navController: NavHostController,
        lifecycleScope: CoroutineScope
    ) {
        this.navController = navController
        this.lifecycleScope = lifecycleScope

        navEvent.onEach {
            debugLog(tag = "GlobalNavigator") { "navEvent.onEach: ${it.hashCode()}" }
            it(GlobalNavigator.navController)
        }.launchIn(GlobalNavigator.lifecycleScope)
    }
}
