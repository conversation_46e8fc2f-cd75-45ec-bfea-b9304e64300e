package com.nbpt.app.androidcomponent.fixednoti

import android.app.ActivityManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.nbpt.app.androidcomponent.DeviceInfo
import com.nbpt.app.common.logger.debugLog
//import com.google.firebase.crashlytics.ktx.crashlytics
//import com.google.firebase.ktx.Firebase
import org.koin.android.ext.android.inject

class FixedNotiService : Service() {

    private val fixedNotificationHelper: FixedNotificationHelper by inject()

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {

        fixedNotificationHelper.startNoti(this)

        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) START_STICKY else START_NOT_STICKY
    }

    companion object {
        private const val TAG = "FixedNotiService"

        fun startServiceIfNeeded(context: Context) {
            if (isForegroundServiceRunning(context, FixedNotiService::class.java).apply {
                    debugLog(tag = TAG) { "Foreground service FixedNotiService is already running: $this" }
                }) return

            debugLog(tag = TAG) { "Starting FixedNotiService before" }
            startService(context)
        }

        fun startService(context: Context) {
            runCatching {
                val intent = Intent(context, FixedNotiService::class.java)

                if (DeviceInfo.isSamsungDevice && Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1) {
                    context.startService(intent)
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    kotlin.runCatching {
                        context.startForegroundService(intent)
                    }.onFailure {
                        Firebase.crashlytics.recordException(
                            Exception("startForeground FixedNotiService failure", it)
                        )
                        debugLog { "startForeground FixedNotiService failure" }
                        it.printStackTrace()
                    }
                } else {
                    context.startService(intent)
                }
            }.onFailure {
                Firebase.crashlytics.recordException(
                    FixedNotiServiceException("start FixedNotiService failure", it)
                )
                debugLog { "start FixedNotiService failure" }
                it.printStackTrace()
            }
        }
    }
}

class FixedNotiServiceException(message: String? = null, cause: Throwable? = null) :
    Exception(message, cause)


fun isForegroundServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
    try {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
            ?: return false

        activityManager.getRunningServices(Integer.MAX_VALUE).forEach { serviceInfo ->
            if (serviceClass.name == serviceInfo.service.className) {
                return serviceInfo.foreground
            }
        }
    } catch (e: SecurityException) {
        debugLog { "Failed to get running service info due to insufficient permissions" }
    } catch (e: Exception) {
        debugLog { "Failed to get running service info due to exception" }
    }

    return false
}