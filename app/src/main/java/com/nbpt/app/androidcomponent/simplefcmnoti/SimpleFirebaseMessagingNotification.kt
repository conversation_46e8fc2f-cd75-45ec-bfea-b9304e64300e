package com.nbpt.app.androidcomponent.simplefcmnoti

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.MainActivity
import com.nbpt.app.R
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.HistoryDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
import org.koin.core.component.KoinComponent

class SimpleFirebaseMessagingNotification(
    private val context: Context,
) : KoinComponent {

    companion object {
        const val EXTRA_KEY_FCM_NOTI_TYPE = "noti_type"

        private const val TAG = "FcmNotification"

        private const val NOTI_ID_STARTING_NUMBER = 10000
    }


    private val androidNotificationManager
        get() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            context.getSystemService(NotificationManager::class.java)
        } else {
            null
        }

    fun notify(
        context: Context,
        title: String,
        content: String,
//        channelId: String,
        fcmNotiType: Int,
    ) {
        debugLog(tag = TAG) { "$TAG notify() fcmNotiType: $fcmNotiType" }

        val notificationId = NOTI_ID_STARTING_NUMBER + fcmNotiType
        val channelId = TAG

        val navIntent = Intent(context, MainActivity::class.java).apply {
            putExtra(EXTRA_KEY_FCM_NOTI_TYPE, fcmNotiType.toString())
        }

        val pendingIntentFlags = pendingIntentDefaultFlags

        val pendingIntent = PendingIntent.getActivity(
            context,
            notificationId,
            navIntent,
            pendingIntentFlags
        )

        val notificationBuilder = NotificationCompat.Builder(
            context, channelId
        ).apply {
            setSmallIcon(R.drawable.ic_noti_heart)
            setContentTitle(title)
            setContentText(content)
            setContentIntent(pendingIntent)
            setGroup(TAG)
//            setAutoCancel(true)
        }

        createNotificationChannel(context, channelId)

        androidNotificationManager?.notify(notificationId, notificationBuilder.build())
    }

    fun handleIntentAction(
        intent: Intent?,
        isActivityInForeground: Boolean = false
    ) {
        var fcmNotiType = -1
        runCatching {
            fcmNotiType = intent?.extras?.getString(EXTRA_KEY_FCM_NOTI_TYPE)?.toIntOrNull() ?: -1
        }

        debugLog(tag = TAG) { "$TAG handleIntent() fcmNotiType: $fcmNotiType" }

        if (fcmNotiType == -1) {
            androidNotificationManager?.cancel(NOTI_ID_STARTING_NUMBER + fcmNotiType)
            return
        }

        // match noti type value to navigate target destination
        val destination = when (fcmNotiType) {
            else -> HomeDestination
        }

        val route = destination.route

        val isFinalRouteToHome = route.startsWith("home", ignoreCase = true)

        when {
            isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
                    popBackStack(
                        HomeDestination.route,
                        false
                    )
                    popBackStack()
                    navigate(route)
                }
            }

            isFinalRouteToHome && !isActivityInForeground -> {
                GlobalNavigator.navigate {
                    popBackStack(HomeDestination.route, false)
                    popBackStack()
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            !isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
                    navigate(route)
                }
            }

            !isFinalRouteToHome && !isActivityInForeground -> {
                GlobalNavigator.navigate {
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            else -> {}
        }


        androidNotificationManager?.cancel(NOTI_ID_STARTING_NUMBER + fcmNotiType)
    }

    private fun createNotificationChannel(
        context: Context,
        channelId: String
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                channelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )

            androidNotificationManager?.createNotificationChannel(serviceChannel)
        }
    }
}