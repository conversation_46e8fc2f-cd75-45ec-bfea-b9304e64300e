package com.nbpt.app.androidcomponent.alarm.remindtorecord

import android.content.Context
import com.nbpt.app.common.datetime.toLocalDatetime
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant

class RemindToRecordAlarm(
    private val remindToRecordAlarmDataStore: RemindToRecordAlarmDataStore,
    private val remindToRecordRingtoneAndNotifyManager: RemindToRecordRingtoneAndNotifyManager,
) {
    private val AlarmDispatcher = newSingleThreadContext("AlarmDispatcher")

    suspend fun tryToAlarm(context: Context, instant: Instant) = withContext(AlarmDispatcher) {
        val currentLocalDatetime = instant.toLocalDatetime()
        val currentDayOfWeek = currentLocalDatetime.dayOfWeek
        val currentLocalTime = currentLocalDatetime.time

        val allAlarmGroups = remindToRecordAlarmDataStore.fetchAlarmGroups()

        allAlarmGroups.forEach alarmGroupsLoop@{ alarmGroup ->

            alarmGroup.dayOfWeekIntValues.forEach dayOfWeeksLoop@{ dayOfWeekIntValue ->
                val sameDayOfWeek = dayOfWeekIntValue == currentDayOfWeek.value
                val sameHour = currentLocalTime.hour == alarmGroup.hour
                val sameMinute = currentLocalTime.minute == alarmGroup.minute

                if (sameDayOfWeek && sameHour && sameMinute) {

                    val alarmType = alarmGroup.type

                    withContext(Dispatchers.Main.immediate) {
                        doAlarm(
                            alarmType = alarmType,
                            remindToRecordRingtone = RemindToRecordRingtone(
                                soundEnable = alarmGroup.soundEnable,
                                vibrateEnable = alarmGroup.vibrateEnable
                            ),
                            remindToRecordNoti = RemindToRecordNoti(
                                title = context.getString(alarmType.titleStringId),
                                content = context.getString(alarmType.contentStringId),
                                goToRecordNaviRoute = alarmType.goToRecordNaviRoute
                            )
                        )
                    }
                }
            }
        }
    }

    private fun doAlarm(
        alarmType: RemindToRecordAlarmType,
        remindToRecordRingtone: RemindToRecordRingtone,
        remindToRecordNoti: RemindToRecordNoti,
    ) {
        remindToRecordRingtoneAndNotifyManager.ringtoneAndNotify(
            alarmType = alarmType,
            remindToRecordRingtone = remindToRecordRingtone,
            remindToRecordNoti = remindToRecordNoti,
        )
    }

}
