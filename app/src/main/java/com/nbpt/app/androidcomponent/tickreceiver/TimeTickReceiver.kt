package com.nbpt.app.androidcomponent.tickreceiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarm
import com.nbpt.app.androidcomponent.lockreceiver.ScreenSwitchReceiver
import com.nbpt.app.androidcomponent.repeatnoti.ArticleRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemindRepeatNoti
import com.nbpt.app.bi.BiReporter
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.FirebaseRemoteConfig
//import com.nbpt.app.androidcomponent.repeatnoti.MonoRepeatNoti
//import com.nbpt.app.bi.BiReporter
//import com.nbpt.app.bi.reportTenjinMmpIfNeeded
//import com.nbpt.app.biz.remoteconfig.RemoteConfig
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.isNotificationPermissionGranted
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object TimeTickReceiver : BroadcastReceiver(), KoinComponent {

    private val firebaseFirebaseRemoteConfigHelper: FirebaseRemoteConfig by inject()

    private val remindToRecordAlarm: RemindToRecordAlarm by inject()

    private val biReporter: BiReporter by inject()

    override fun onReceive(context: Context, intent: Intent?) {
        if (intent?.action == Intent.ACTION_TIME_TICK) {
            logEventRecord("receiver_tick")
            handleTimeTick(context)
        }
    }

    fun registerTimeTickReceiver(context: Context) {
        ContextCompat.registerReceiver(
            context,
            TimeTickReceiver,
            IntentFilter(Intent.ACTION_TIME_TICK),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
    }

    fun handleTimeTick(context: Context) {
        val instant = nowInstant()
        firebaseFirebaseRemoteConfigHelper.tryToUpdateConfig(instant)

        if (context.isNotificationPermissionGranted()) {
            GlobalScope.launch {
                remindToRecordAlarm.tryToAlarm(
                    context = context,
                    instant = instant,
                )
            }
        }

        if (context.isNotificationPermissionGranted()
            && ScreenSwitchReceiver.isScreenOnAndUnlocked().apply {
                debugLog(tag = "TimeTickReceiver") { "Current screen is on and unlocked: $this" }
            }
        ) {
            GlobalScope.launch {

                launch {
                    RemindRepeatNoti.tryToShowNotification(
                        instant = instant,
                        randomDelaySeconds = 52,
                    )
                }
                launch {
                    ArticleRepeatNoti.tryToShowNotification(
                        instant = instant,
                        randomDelaySeconds = 52,
                    )
                }
            }
        }

        GlobalScope.launch {
            delay((2000L..9000L).random())
            biReporter.handlePendingReportEvents()
        }
    }

}
