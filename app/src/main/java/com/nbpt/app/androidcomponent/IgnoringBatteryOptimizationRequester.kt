package com.nbpt.app.androidcomponent

import android.app.Activity
import android.content.Context
import android.os.Build
import android.os.PowerManager
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.android.openBatteryOptimizationSettings
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.mmkv.mmkvWithId
import com.nbpt.app.ui.screen.destinations.IgnoringBatteryOptimizationDialogDestination
import kotlinx.datetime.Instant
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "IgnoringBatteryOptimizationRequester"

class IgnoringBatteryOptimizationRequester {

    private val mmkv = mmkvWithId(TAG)

    fun hasIgnoring(activity: Activity): Boolean? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = activity.getSystemService(Context.POWER_SERVICE) as PowerManager?

            powerManager?.isIgnoringBatteryOptimizations(activity.packageName)
        } else {
            null
        }
    }

    fun tryToOpenBatteryOptimizationDialog(activity: Activity, force: Boolean = false): Boolean {
        if (force) {
            GlobalNavigator.navigate {
                navigate(IgnoringBatteryOptimizationDialogDestination(fromPermissionsManager = true).route)
            }
            return true
        }

        val now = nowInstant()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M
            && hasIgnoring(activity) == false
            && requestTimes < REQUEST_LIMIT
            && (now >= latestOpenBatteryOptimizationSettingsInstant() + 12.toDuration(DurationUnit.HOURS))
        ) {
            GlobalNavigator.navigate {
                navigate(IgnoringBatteryOptimizationDialogDestination().route)
            }
            return true
        }

        return false
    }

    fun tryToOpenBatteryOptimizationSystemSettings(
        activity: Activity,
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            activity.openBatteryOptimizationSettings()
        }
    }

    fun latestOpenBatteryOptimizationSettingsInstant(): Instant {
        return mmkv.decodeLong(LATEST_OPEN_BATTERY_OPTIMIZATION_SETTINGS_INSTANT_KEY, 0L)
            .let(Instant::fromEpochSeconds)
    }

    fun storeOpenBatteryOptimizationSettingsInstant(instant: Instant = nowInstant()) {
        mmkv.encode(LATEST_OPEN_BATTERY_OPTIMIZATION_SETTINGS_INSTANT_KEY, instant.epochSeconds)
    }

    var requestTimes
        get() = mmkv.decodeInt(REQUEST_TIMES_KEY, 0)
        set(value) {
            mmkv.encode(REQUEST_TIMES_KEY, value)
        }

    companion object {
        private const val REQUEST_LIMIT = 3

        private const val LATEST_OPEN_BATTERY_OPTIMIZATION_SETTINGS_INSTANT_KEY =
            "latest_open_battery_optimization_settings_instant_key"

        private const val REQUEST_TIMES_KEY = "request_times_key"

        val ignoringEventFlow = EventFlow<Boolean?>()
    }
}