package com.nbpt.app.androidcomponent.fixednoti

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.content.IntentCompat
import androidx.navigation.NavHostController
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.MainActivity
import com.nbpt.app.androidcomponent.fixednoti.NotiNavAction.*
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.ui.screen.bloodsugarrecordeditor.BsRecordEditorMode
import com.nbpt.app.ui.screen.bprecordeditor.BpRecordEditorMode
import com.nbpt.app.ui.screen.destinations.BpDestination
import com.nbpt.app.ui.screen.destinations.BpRecordEditorDestination
import com.nbpt.app.ui.screen.destinations.BsDestination
import com.nbpt.app.ui.screen.destinations.BsRecordEditorDestination
import com.nbpt.app.ui.screen.destinations.HeartRateMeasureDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.HrDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination

enum class NotiNavAction(val actionName: String) {
    HOME_TOOLS("noti_home_tools"),
    HOME_ARTICLES("noti_home_articles"),
    HR("noti_hr"),
    BP("noti_bp"),
    BS("noti_bs"),
    FN_HR("fixed_noti_hr"),
    FN_BP("fixed_noti_bp"),
    FN_BS("fixed_noti_bs"),
    FN_HR_MEASURE("fixed_noti_hr_measure"),
    FN_BP_RECORD("fixed_noti_bp_record"),
    FN_BS_RECORD("fixed_noti_bs_record"), ;
}


class NotiClickManager(
    private val context: Context,
    private val splashManager: SplashManager
) {
    companion object {
        private const val TAG = "FixedNotiClickManager"
        const val EXTRA_KEY_NAV_ACTION = "${TAG}_nav_action"
    }

    fun createActionPendingIntent(
        action: NotiNavAction
    ): PendingIntent {
        return PendingIntent.getActivity(
            context,
            action.hashCode(),
            Intent(context, MainActivity::class.java).apply {
                putExtra(EXTRA_KEY_NAV_ACTION, action)
            },
            pendingIntentDefaultFlags
        )
    }

    fun handleAction(
        intent: Intent?,
        isActivityInForeground: Boolean = false
    ) {
        debugLog(tag = TAG) { "Handle action called with intent: $intent" }
        if (intent == null) {
            splashManager.doSkipSplash(false)
            return
        }

        val navAction = IntentCompat.getSerializableExtra(
            intent,
            EXTRA_KEY_NAV_ACTION,
            NotiNavAction::class.java
        ) ?: return

        val navActionName = navAction.actionName

        logEventRecord("click_$navActionName")

        when (navAction) {
            HOME_TOOLS -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    popBackStack()
                    finalNavigate(isActivityInForeground, HomeDestination(tabIndex = 0).route)
                }
            }

            HOME_ARTICLES -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    popBackStack()
                    finalNavigate(isActivityInForeground, HomeDestination(tabIndex = 1).route)
                }
            }

            HR, FN_HR -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    finalNavigate(isActivityInForeground, HrDestination.route)
                }
            }

            BP, FN_BP -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    finalNavigate(isActivityInForeground, BpDestination.route)
                }
            }

            BS, FN_BS -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    finalNavigate(isActivityInForeground, BsDestination.route)
                }
            }

            FN_HR_MEASURE -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    navigate(HrDestination.route)
                    finalNavigate(isActivityInForeground, HeartRateMeasureDestination.route)
                }
            }

            FN_BP_RECORD -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    navigate(BpDestination.route)
                    finalNavigate(
                        isActivityInForeground,
                        BpRecordEditorDestination(editorMode = BpRecordEditorMode.Add).route
                    )
                }
            }

            FN_BS_RECORD -> {
                GlobalNavigator.navigate {
                    backToHomeToolsTab()
                    navigate(BsDestination.route)
                    finalNavigate(
                        isActivityInForeground,
                        BsRecordEditorDestination(editorMode = BsRecordEditorMode.Add).route
                    )
                }
            }
        }
    }
}

private fun NavHostController.finalNavigate(
    isActivityInForeground: Boolean,
    route: String
) {
    if (isActivityInForeground) {
        navigate(route)
    } else {
        navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
    }
}

private fun NavHostController.backToHomeToolsTab() {
    val homeToolsTabRoute = HomeDestination(tabIndex = 0).route
    navigate(homeToolsTabRoute) {
        popUpTo(homeToolsTabRoute) {
            inclusive = false
        }
    }
}
