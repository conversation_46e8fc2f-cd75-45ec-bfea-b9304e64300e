

package com.nbpt.app.androidcomponent

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Parcelable
import androidx.core.content.ContextCompat
import kotlinx.parcelize.Parcelize
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import androidx.core.os.BundleCompat

object HandleRemoteViewsEventReceiver : BroadcastReceiver(), KoinComponent {

    private const val TAG = "HandleRemoteViewsEventReceiver_HT"

    private const val EXTRA_KEY_REMOTE_VIEWS_EVENT = "${TAG}_extra_key_remote_views_event"

    private const val ACTION_HANDLE_REMOTE_VIEWS_EVENT = "${TAG}_action_handle_remote_views_event"

    private val context: Context by inject()

    private val androidNotificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        context.getSystemService(NotificationManager::class.java)
    } else {
        null
    }

    sealed class RemoteViewsEvent : Parcelable

    @Parcelize
    data class CloseNotification(val notiId: Int) : RemoteViewsEvent() {

        companion object {
            fun createIntent(notiId: Int): Intent {
                return Intent().apply {
                    setPackage(context.packageName)
                    action = ACTION_HANDLE_REMOTE_VIEWS_EVENT
                    putExtra(EXTRA_KEY_REMOTE_VIEWS_EVENT, CloseNotification(notiId = notiId))
                }
            }
        }
    }


    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == ACTION_HANDLE_REMOTE_VIEWS_EVENT) {
            val event = intent.getParcelableInternal<RemoteViewsEvent>(EXTRA_KEY_REMOTE_VIEWS_EVENT)

            when (event) {
                is CloseNotification -> {
                    androidNotificationManager?.cancel(event.notiId)
                }

                null -> {}
            }
        }
    }

    private inline fun <reified T> Intent.getParcelableInternal(extraKey: String): T? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val extras = this.extras
            if (extras == null) {
                null
            } else {
                BundleCompat.getParcelable(extras, extraKey, T::class.java)
            }
        } else {
            this.getParcelableExtra(extraKey)
        }
    }

    fun register(context: Context) {
        ContextCompat.registerReceiver(
            context,
            HandleRemoteViewsEventReceiver,
            IntentFilter(ACTION_HANDLE_REMOTE_VIEWS_EVENT),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
    }
}
