package com.nbpt.app.androidcomponent

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.provider.Settings

@Suppress("MemberVisibilityCanBePrivate")
object DeviceInfo {
    val manufacturer: String? = Build.MANUFACTURER
    val model: String? = Build.MODEL

    val isSamsungDevice
        get() = manufacturer.equals("Samsung", true)
                || model?.startsWith("SM-") == true

    fun isDarkMode(context: Context): Boolean {
        val nightModeFlags =
            context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK

        return (nightModeFlags == Configuration.UI_MODE_NIGHT_YES)
    }

//    fun adbEnabled(context: Context): Boolean = run {
//        val usbAdb =
//            Settings.Global.getInt(context.contentResolver, Settings.Global.ADB_ENABLED, 0) == 1
//        val wifiAdb = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            Settings.Global.getInt(context.contentResolver, "adb_wifi_enabled", 0) == 1
//        } else {
//            false
//        }
//        usbAdb || wifiAdb
//    }
}
