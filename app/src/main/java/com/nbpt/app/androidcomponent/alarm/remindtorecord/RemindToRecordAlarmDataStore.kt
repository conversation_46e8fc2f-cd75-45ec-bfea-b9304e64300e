package com.nbpt.app.androidcomponent.alarm.remindtorecord

import android.content.Context
import androidx.annotation.Keep
import androidx.annotation.StringRes
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.nbpt.app.R
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import com.nbpt.app.ui.screen.destinations.BpDestination
import com.nbpt.app.ui.screen.destinations.BsDestination
import com.nbpt.app.ui.screen.destinations.HrDestination
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.datetime.DayOfWeek
import java.util.UUID

private val _remindToRecordAlarmTypeEntries: List<RemindToRecordAlarmType> = listOf(
    RemindToRecordAlarmType.BloodPressure,
    RemindToRecordAlarmType.HeartRate,
    RemindToRecordAlarmType.BloodSugar
)

val RemindToRecordAlarmType.Companion.entries get() = _remindToRecordAlarmTypeEntries

sealed class RemindToRecordAlarmType(
    val sid: Int,
    val typeName: String,
    @StringRes val typeStringId: Int,
    @StringRes val titleStringId: Int,
    @StringRes val contentStringId: Int,
    @StringRes val actionButtonStringId: Int,
    val goToRecordNaviRoute: String
) {
    data object HeartRate : RemindToRecordAlarmType(
        sid = 0,
        typeName = "Heart Rate",
        typeStringId = R.string.text_alarm_type_heart_rate,
        titleStringId = R.string.text_alarm_type_heart_rate_title,
        contentStringId = R.string.text_alarm_type_heart_rate_content,
        actionButtonStringId = R.string.text_go_to_measure,
        goToRecordNaviRoute = HrDestination.route
    )

    data object BloodPressure : RemindToRecordAlarmType(
        sid = 1,
        typeName = "Blood Pressure",
        typeStringId = R.string.text_alarm_type_blood_pressure,
        titleStringId = R.string.text_alarm_type_blood_pressure_title,
        contentStringId = R.string.text_alarm_type_blood_pressure_content,
        actionButtonStringId = R.string.text_go_to_record,
        goToRecordNaviRoute = BpDestination.route
    )

    data object BloodSugar : RemindToRecordAlarmType(
        sid = 2,
        typeName = "Blood Sugar",
        typeStringId = R.string.text_alarm_type_blood_sugar,
        titleStringId = R.string.text_alarm_type_blood_sugar_title,
        contentStringId = R.string.text_alarm_type_blood_sugar_content,
        actionButtonStringId = R.string.text_go_to_record,
        goToRecordNaviRoute = BsDestination.route
    )

    val iconResId: Int
        get() = when (sid) {
            HeartRate.sid -> R.drawable.img_alarm_hr
            BloodPressure.sid -> R.drawable.img_alarm_bp
            BloodSugar.sid -> R.drawable.img_alarm_bs
            else -> R.drawable.img_alarm_bp
        }

    companion object {
        fun of(sid: Int): RemindToRecordAlarmType =
            when (sid) {
                HeartRate.sid -> HeartRate
                BloodPressure.sid -> BloodPressure
                BloodSugar.sid -> BloodSugar
                else -> throw IllegalAccessException("can not match this sid: $sid")
            }
    }
}

@Keep
@JsonClass(generateAdapter = true)
data class AlarmGroup(
    val id: String = UUID.randomUUID().toString(),
    val typeSid: Int,
    val hour: Int?,
    val minute: Int?,
    val soundEnable: Boolean,
    val vibrateEnable: Boolean,
    val dayOfWeekIntValues: List<Int>
) {

    @Json(ignore = true)
    val type: RemindToRecordAlarmType = RemindToRecordAlarmType.of(typeSid)

    @Json(ignore = true)
    val dayOfWeek: List<DayOfWeek> = dayOfWeekIntValues.map(::DayOfWeek)

    companion object {
        val Default = AlarmGroup(
            typeSid = 1,
            hour = 9,
            minute = 0,
            soundEnable = true,
            vibrateEnable = true,
            dayOfWeekIntValues = java.time.DayOfWeek.entries.map { it.value }
        )
    }
}

private const val TAG = "RemindToRecordAlarmDataStore"

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = TAG)

class RemindToRecordAlarmDataStore(
    private val context: Context
) {
    private val alarmGroupsKey = stringPreferencesKey("alarmGroupsKey")

    suspend fun fetchAlarmGroups(): List<AlarmGroup> {
        return context.dataStore.data.map {
            it[alarmGroupsKey]?.toObj<List<AlarmGroup>>()
        }.first() ?: emptyList()
    }

    suspend fun addAlarmGroup(alarmGroup: AlarmGroup) {
        val allAlarmGroups = fetchAlarmGroups()

        val newAlarmGroups = (allAlarmGroups + alarmGroup).toMutableSet().toList()

        saveAlarmGroups(newAlarmGroups)
    }

    suspend fun editAlarmGroup(alarmGroup: AlarmGroup): Boolean {
        val allAlarmGroups = fetchAlarmGroups().toMutableList()

        val pendingEditIndex = allAlarmGroups.indexOfFirst { it.id == alarmGroup.id }

        return if (pendingEditIndex != -1) {
            allAlarmGroups[pendingEditIndex] = alarmGroup
            saveAlarmGroups(allAlarmGroups)
            true
        } else {
            false
        }
    }

    suspend fun deleteAlarmGroup(id: String): Boolean {
        val allAlarmGroups = fetchAlarmGroups()
        if (allAlarmGroups.isEmpty()) return false

        val pendingDeleteAlarmGroup = allAlarmGroups.find { it.id == id } ?: return false

        val newAlarmGroups = allAlarmGroups - pendingDeleteAlarmGroup
        saveAlarmGroups(newAlarmGroups)

        return true
    }

    private suspend fun saveAlarmGroups(alarmGroups: List<AlarmGroup>) {
        context.dataStore.edit {
            it[alarmGroupsKey] = alarmGroups.toJsonString() ?: ""
        }
    }
}
