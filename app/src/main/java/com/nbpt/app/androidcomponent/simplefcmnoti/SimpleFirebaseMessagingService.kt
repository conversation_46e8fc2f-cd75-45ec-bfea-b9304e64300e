package com.nbpt.app.androidcomponent.simplefcmnoti

import com.nbpt.app.androidcomponent.tickreceiver.TimeTickReceiver
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.datetime.nowInstant
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import org.koin.android.ext.android.inject

class SimpleFirebaseMessagingService : FirebaseMessagingService() {

    private val sfmn: SimpleFirebaseMessagingNotification by inject()

    sealed class MsgType(val rawValue: String) {
        data object FirebaseConsole : MsgType("wm_fcm_console")
        data object Server : MsgType("wm_fcm_server")

        companion object {
            fun of(rawValue: String?): MsgType? {
                return when (rawValue) {
                    FirebaseConsole.rawValue -> FirebaseConsole
                    Server.rawValue -> Server
                    else -> null
                }
            }
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
    }

    override fun onMessageReceived(message: RemoteMessage) {
//        super.onMessageReceived(message)
        logEventRecord("receiver_fcm")
        handleMessage(message)
        executeTimeTickTaskIfNeeded()
    }

    private fun handleMessage(message: RemoteMessage) {
        val msgTypeRawValue = message.data["type"]

        when (MsgType.of(msgTypeRawValue)) {
            MsgType.FirebaseConsole -> handleConsoleMessage(message)
            MsgType.Server -> handleServerMessage(message)
            null -> {}
        }
    }

    private fun handleConsoleMessage(message: RemoteMessage) {
        val title = message.notification?.title ?: return
        val content = message.notification?.body ?: return

        val data = message.data

        val notiType = data[SimpleFirebaseMessagingNotification.EXTRA_KEY_FCM_NOTI_TYPE] ?: "-1"

        val notiTypeInt = try {
            notiType.toInt()
        } catch (e: Exception) {
            -1
        }

        sfmn.notify(
            this,
            title = title,
            content = content,
            fcmNotiType = notiTypeInt
        )
        logEventRecord("fcm_handle_console_message")
    }

    private fun handleServerMessage(message: RemoteMessage) {
        super.onMessageReceived(message)
        logEventRecord("fcm_handle_server_message")
    }


    private var latestExecuteTimeTickTaskSeconds = 0L
    private fun executeTimeTickTaskIfNeeded() {
        val nowSeconds = nowInstant().epochSeconds

        if (nowSeconds - 60 >= latestExecuteTimeTickTaskSeconds) {
            TimeTickReceiver.handleTimeTick(this.applicationContext)
            latestExecuteTimeTickTaskSeconds = nowSeconds
        }
    }
}