package com.nbpt.app.androidcomponent

import com.nbpt.app.androidcomponent.alarm.remindtorecord.*
import com.nbpt.app.androidcomponent.fixednoti.NotiClickManager
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.androidcomponent.repeatnoti.HandsUpNotiManager
import com.nbpt.app.androidcomponent.simplefcmnoti.SimpleFirebaseMessagingNotification
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module

val androidComponentModule = module {
    singleOf(::FixedNotificationHelper)
    singleOf(::HandsUpNotiManager)
    singleOf(::RemindToRecordAlarm)
    singleOf(::RemindToRecordRingtoneAndNotifyManager)
    singleOf(::RemindToRecordAlarmDataStore)
    singleOf(::SimpleFirebaseMessagingNotification)
    singleOf(::NotificationPermissionRequester)
    singleOf(::IgnoringBatteryOptimizationRequester)
    singleOf(::PermissionRequestManager)
    singleOf(::NotiClickManager)
}
