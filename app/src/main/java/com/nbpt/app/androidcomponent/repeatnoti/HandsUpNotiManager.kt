package com.nbpt.app.androidcomponent.repeatnoti

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.View
import android.widget.RemoteViews
import androidx.annotation.AnyRes
import androidx.annotation.DrawableRes
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.app.NotificationCompat
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.DeviceInfo
import com.nbpt.app.androidcomponent.HandleRemoteViewsEventReceiver
import com.nbpt.app.androidcomponent.PendingIntentPassedToIntentExtra
import com.nbpt.app.androidcomponent.fixednoti.NotiClickManager
import com.nbpt.app.androidcomponent.fixednoti.NotiNavAction
import com.nbpt.app.androidcomponent.notireceiver.RepeatNotiRemoveEventReceiver
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.common.send
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination

class HandsUpNotiManager(
    private val context: Context,
    private val splashManager: SplashManager
) {

    companion object {
        private const val TAG = "HandsUpNotiManager"
        private const val EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE = "${TAG}_noti_nav_destination"
        private const val EXTRA_KEY_NOTI_ID = "${TAG}_noti_id"
        private const val EXTRA_KEY_CLICK_FOR_EVENT_RECORDS = "${TAG}_click_for_event_records"
    }

    private val androidNotificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        context.getSystemService(NotificationManager::class.java) ?: null
    } else {
        null
    }

    private data class NotificationParams(
        val title: String,
        val content: String,
        val channelId: String,
        val notificationId: Int,
        val imageRes: Int?,
        val clickEventRecords: ArrayList<String>?,
        val useFullScreenIntent: Boolean = false,
        val disableSwipeToCloseAndShowCloseIcon: Boolean = false
    )

    fun notify(
        context: Context,
        title: String,
        content: String,
        channelId: String,
        notificationId: Int,
        notiNavAction: NotiNavAction,
        @DrawableRes imageRes: Int?,
        clickEventRecords: ArrayList<String>?,
        useFullScreenIntent: Boolean = false,
        disableSwipeToCloseAndShowCloseIcon: Boolean = false
    ) {
        val params = NotificationParams(
            title, content, channelId, notificationId, imageRes, 
            clickEventRecords, useFullScreenIntent, disableSwipeToCloseAndShowCloseIcon
        )
        
        val navIntent = PendingIntentPassedToIntentExtra
            .createIntent(context, "cus_noti")
            .apply {
                putExtra(NotiClickManager.EXTRA_KEY_NAV_ACTION, notiNavAction)
                putExtra(EXTRA_KEY_NOTI_ID, notificationId)
                clickEventRecords?.let {
                    putExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORDS, clickEventRecords)
                }
            }
        
        showNotification(context, params, navIntent)
        debugLog(tag = TAG) { "notify() notiNavAction:$notiNavAction notiId:$notificationId" }
    }

    fun notify(
        context: Context,
        title: String,
        content: String,
        channelId: String,
        notificationId: Int,
        route: String,
        @DrawableRes imageRes: Int?,
        clickEventRecords: ArrayList<String>?,
        useFullScreenIntent: Boolean = false,
        disableSwipeToCloseAndShowCloseIcon: Boolean = false
    ) {
        val params = NotificationParams(
            title, content, channelId, notificationId, imageRes, 
            clickEventRecords, useFullScreenIntent, disableSwipeToCloseAndShowCloseIcon
        )
        
        val navIntent = PendingIntentPassedToIntentExtra
            .createIntent(context, "cus_noti")
            .apply {
                putExtra(EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE, route)
                putExtra(EXTRA_KEY_NOTI_ID, notificationId)
                clickEventRecords?.let {
                    putExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORDS, clickEventRecords)
                }
            }
        
        showNotification(context, params, navIntent)
        debugLog(tag = TAG) { "notify() route:$route notiId:$notificationId" }
    }

    private fun showNotification(context: Context, params: NotificationParams, navIntent: Intent) {
        if (!context.checkNotificationResIdAvailable()) return
        
        val fullScreenPendingIntent = if (params.useFullScreenIntent) {
            PendingIntent.getActivity(
                context,
                params.notificationId,
                Intent().apply {
                    setPackage(context.packageName)
                },
                pendingIntentDefaultFlags
            )
        } else null

        val clickNotiPendingIntent = PendingIntent.getActivity(
            context,
            params.notificationId,
            navIntent,
            pendingIntentDefaultFlags
        )

        val repeatNotiRemoveIntent = RepeatNotiRemoveEventReceiver.repeatNotiRemoveIntent(
            context,
            params.notificationId,
            pendingIntentDefaultFlags
        )

        val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

        val _notiFoldView = if (isApiGreaterThanOrEqual31) createNotiView(
            title = params.title,
            content = params.content,
            imageRes = params.imageRes,
            isExpand = false,
            withCloseIcon = false
        ) else null

        val _notiExpandView: RemoteViews = createNotiView(
            title = params.title,
            content = params.content,
            imageRes = params.imageRes,
            isExpand = true,
            withCloseIcon = params.disableSwipeToCloseAndShowCloseIcon
        )

        if (params.disableSwipeToCloseAndShowCloseIcon) {
            val closeNotiIntent =
                HandleRemoteViewsEventReceiver.CloseNotification.createIntent(params.notificationId)

            val closeNotiPendingIntent = PendingIntent
                .getBroadcast(context, params.notificationId, closeNotiIntent, pendingIntentDefaultFlags)

            _notiExpandView.setOnClickPendingIntent(R.id.iv_close_noti, closeNotiPendingIntent)
        }

        val notificationBuilder = NotificationCompat.Builder(
            context, params.channelId
        ).apply {
            setSmallIcon(R.drawable.ic_noti_heart)

            if (_notiFoldView == null) {
                setCustomContentView(_notiExpandView)
                setCustomHeadsUpContentView(_notiExpandView)
            } else {
                setCustomContentView(_notiFoldView)
                setCustomHeadsUpContentView(_notiFoldView)
                setCustomBigContentView(_notiExpandView)
            }

            setContentIntent(clickNotiPendingIntent)
            setAutoCancel(true)
            setDeleteIntent(repeatNotiRemoveIntent)
            setVibrate(longArrayOf(0))
            fullScreenPendingIntent?.let {
                setFullScreenIntent(it, true)
            }
            setOngoing(params.disableSwipeToCloseAndShowCloseIcon)
            setGroup(TAG)

            priority = NotificationCompat.PRIORITY_HIGH
        }

        createNotificationChannel(context, params.channelId)

        androidNotificationManager?.notify(params.notificationId, notificationBuilder.build())
    }

    fun handleNotiIntent(
        intent: Intent?,
        isActivityInForeground: Boolean = false
    ) {
        debugLog(tag = TAG) { "handleNotiIntent()" }

        val route = intent?.getStringExtra(EXTRA_KEY_NOTI_NAV_DESTINATION_ROUTE)
        val notiId = intent?.getIntExtra(EXTRA_KEY_NOTI_ID, -1) ?: -1
        val clickEventRecords = intent?.getStringArrayListExtra(EXTRA_KEY_CLICK_FOR_EVENT_RECORDS)
        if (route.isNullOrEmpty() || notiId == -1) {
            splashManager.doSkipSplash(false)
            debugLog(tag = TAG) { "route.isNullOrEmpty() || notiId == -1" }
            return
        }

        if (!clickEventRecords.isNullOrEmpty()) {
            clickEventRecords.forEach { eventRecord ->
                eventRecord?.let(::logEventRecord)
            }
        }

//        repeatNotiGotRemoveEventFlow.send(RepeatNotiEvent(notiId))

        val isFinalRouteToHome = route.startsWith("home", ignoreCase = true)

        debugLog(tag = TAG) { "isFinalRouteToHome $isFinalRouteToHome && isActivityInForeground $isActivityInForeground" }

        @Suppress("KotlinConstantConditions")
        when {
            isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
//                    val backQueueHomeEntry = backQueue.findLast {
//                        it.destination.route == HomeDestination.route
//                    }
//
//                    debugLog(tag = TAG) { "backQueue.first(): ${backQueueHomeEntry?.destination}" }

                    popBackStack(
//                        backQueueHomeEntry?.destination?.route ?: HomeDestination.route,
                        HomeDestination.route,
                        false
                    )
                    popBackStack()
                    navigate(route)
                }
            }

            isFinalRouteToHome && !isActivityInForeground -> {
                GlobalNavigator.navigate {
                    popBackStack(HomeDestination.route, false)
                    popBackStack()
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            !isFinalRouteToHome && isActivityInForeground -> {
                GlobalNavigator.navigate {
                    navigate(route)
                }
            }

            !isFinalRouteToHome && !isActivityInForeground -> {
                GlobalNavigator.navigate {
                    navigate(SplashDestination(afterLaunchDestinationRoute = route).route)
                }
            }

            else -> {}
        }

    }

    private fun createNotificationChannel(
        context: Context,
        channelId: String
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                channelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                setSound(null, null)
            }

            androidNotificationManager?.createNotificationChannel(serviceChannel)
        }
    }

    private fun createNotiView(
        title: String,
        content: String,
        @DrawableRes imageRes: Int?,
        isExpand: Boolean,
        withCloseIcon: Boolean
    ): RemoteViews {
        val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

        val remoteViewsLayoutRes = when {
            isApiGreaterThanOrEqual31 && !isExpand -> R.layout.layout_repeat_noti
            isApiGreaterThanOrEqual31 && isExpand -> R.layout.layout_repeat_noti_expand
            !isApiGreaterThanOrEqual31 && !isExpand -> R.layout.layout_repeat_noti_for_no_dec
            !isApiGreaterThanOrEqual31 && isExpand -> R.layout.layout_repeat_noti_expand_for_no_dec
            else -> R.layout.layout_repeat_noti
        }

        return RemoteViews(
            context.packageName,
            remoteViewsLayoutRes
        ).apply {
            setTextViewText(R.id.tv_noti_title, title)
            setTextViewText(R.id.tv_noti_content, content)
            imageRes?.let { setImageViewResource(R.id.iv_noti_image, imageRes) }

            if (DeviceInfo.isDarkMode(context)) {
                val textColor = Color.White.toArgb()
                setTextColor(R.id.tv_noti_title, textColor)
                setTextColor(R.id.tv_noti_content, textColor)
            }

            if (withCloseIcon) {
                setViewVisibility(R.id.iv_close_noti, View.VISIBLE)
            }

        }
    }
}

internal fun Context.checkNotificationResIdAvailable(): Boolean {
    return if (Build.VERSION.SDK_INT in Build.VERSION_CODES.M..Build.VERSION_CODES.N) {
        isResourceIdAvailable(R.drawable.ic_noti_heart)
    } else {
        true
    }
}

// https://stackoverflow.com/a/6677932
fun Context.isResourceIdAvailable(@AnyRes resId: Int): Boolean {
    return try {
        !resources?.getResourceEntryName(resId).isNullOrEmpty()
    } catch (e: Exception) {
        e.printStackTrace()
        false
    }
}