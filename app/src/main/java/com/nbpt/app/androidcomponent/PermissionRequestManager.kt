package com.nbpt.app.androidcomponent

import android.app.Activity
import android.os.Build
import androidx.navigation.NavHostController
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.ui.screen.destinations.GuideDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
import com.ramcosta.composedestinations.spec.DestinationStyle
import com.ramcosta.composedestinations.spec.DestinationStyleBottomSheet
import com.ramcosta.composedestinations.utils.currentDestinationFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class PermissionRequestManager(
    private val notificationPermissionRequester: NotificationPermissionRequester,
    private val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester
) {

    fun registerRequesterIfNeeded(
        activity: Activity,
        navController: NavHostController,
        lifecycleScope: CoroutineScope,
    ) {
        debugLog(tag = TAG) { "registerRequesterIfNeeded" }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return

        navController.currentDestinationFlow.onEach {
            delay(100)

            val currentInSplashOrGuideOrHome = it in arrayOf(SplashDestination, GuideDestination, HomeDestination)

            val currentInDialog =
                it.style is DestinationStyle.Dialog || it.style is DestinationStyleBottomSheet

            debugLog(tag = TAG) { " registerRequesterIfNeeded currentInSplashOrGuideOrHome:$currentInSplashOrGuideOrHome currentInDialog:$currentInDialog" }

            if (!currentInSplashOrGuideOrHome
                && !currentInDialog
            ) {
                doRequestIfNeeded(activity)
            }
        }.launchIn(lifecycleScope)
    }

    fun doRequestIfNeeded(activity: Activity) {
        debugLog(tag = TAG) { "doRequestIfNeeded" }
        val requestNotificationPermission = notificationPermissionRequester.tryToRequestIfNeeded(activity)

        if (!requestNotificationPermission) {
            ignoringBatteryOptimizationRequester.tryToOpenBatteryOptimizationDialog(activity)
        }
    }

    companion object {
        private val TAG = "PermissionRequestManager"
    }
}