package com.nbpt.app.androidcomponent.repeatnoti

import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.mmkv.mmkvWithId
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaInstant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import java.time.LocalDate
import java.time.ZoneId

object RepeatNotiLimiter : KoinComponent {
    private const val TAG = "RepeatNotiLimiter"
    private const val KEY_DATE = "last_date"
    private const val KEY_COUNT = "noti_count"

    private val mmkv = mmkvWithId(TAG)
    val remoteConfig: RealRemoteConfig by inject()

    private val dailyNotiTimes
        get() = remoteConfig.dailyNotiTimes

    fun needExecute(instant: Instant): Boolean {
        return needExecute(instant.toJavaInstant())
    }

    /**
     * 检查是否需要执行通知
     * @param jInstant 当前时间
     * @return 如果可以执行返回true，否则返回false
     */
    fun needExecute(jInstant: java.time.Instant): Boolean {
        val currentDate = jInstant.atZone(ZoneId.systemDefault()).toLocalDate()
        val lastDateStr = mmkv.decodeString(KEY_DATE)

        // 如果是新的一天，重置计数
        if (lastDateStr == null || LocalDate.parse(lastDateStr) != currentDate) {
            resetCount(currentDate)
            debugLog(tag = TAG) { "resetCount return true" }
            return true
        }

        val currentCount = mmkv.decodeInt(KEY_COUNT)

        debugLog(tag = TAG) { "currentCount:$currentCount, dailyNotiTimes:$dailyNotiTimes return ${currentCount < dailyNotiTimes}" }

        // 检查是否超过每日限制
        return currentCount < dailyNotiTimes
    }

    /**
     * 重置计数器
     * @param date 当前日期
     */
    private fun resetCount(date: LocalDate) {
        mmkv.encode(KEY_DATE, date.toString())
        mmkv.encode(KEY_COUNT, 0)
    }

    /**
     * 增加通知计数
     */
    fun incrementCount() {
        val currentCount = mmkv.decodeInt(KEY_COUNT)
        mmkv.encode(KEY_COUNT, currentCount + 1)
    }

    /**
     * 获取今天剩余的通知次数
     * @return 剩余次数，如果是新的一天则返回配置的总次数
     */
    fun getRemainingCount(jInstant: java.time.Instant): Int {
        val currentDate = jInstant.atZone(ZoneId.systemDefault()).toLocalDate()
        val lastDateStr = mmkv.decodeString(KEY_DATE)

        if (lastDateStr == null || LocalDate.parse(lastDateStr) != currentDate) {
            return dailyNotiTimes
        }

        val currentCount = mmkv.decodeInt(KEY_COUNT)
        return (dailyNotiTimes - currentCount).coerceAtLeast(0)
    }
}