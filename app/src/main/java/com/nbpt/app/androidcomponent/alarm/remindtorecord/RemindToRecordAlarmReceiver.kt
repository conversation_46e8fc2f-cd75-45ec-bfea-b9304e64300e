@file:Suppress("MemberVisibilityCanBePrivate")

package com.nbpt.app.androidcomponent.alarm.remindtorecord

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.pendingIntentDefaultFlags
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object RemindToRecordAlarmReceiver : BroadcastReceiver(), KoinComponent {

    private val remindToRecordRingtoneAndNotifyManager: RemindToRecordRingtoneAndNotifyManager by inject()

    private const val TAG = "RemindToRecordAlarmReceiver"
    private const val REQUEST_CODE = 0x098

    const val ACTION_R2R_CLICK_NOT_NOW = "${TAG}_ACTION_R2R_CLICK_NOT_NOW"
    const val ACTION_R2R_REMOVE_NOTI = "${TAG}_ACTION_R2R_REMOVE_NOTI"

    override fun onReceive(context: Context?, intent: Intent?) {

        debugLog(tag = TAG) { "intent.action -> ${intent?.action}" }

        when (intent?.action) {
            ACTION_R2R_CLICK_NOT_NOW, ACTION_R2R_REMOVE_NOTI -> {
                logEventRecord("click_alarm_noti_not_now")

                remindToRecordRingtoneAndNotifyManager.cancelRingtoneIfNeeded()
            }

            else -> {

            }
        }
    }

    fun register(context: Context) {
        debugLog(tag = "Alarm") { "register(context: Context)" }
        ContextCompat.registerReceiver(
            context.applicationContext,
            RemindToRecordAlarmReceiver,
            IntentFilter().apply {
                addAction(ACTION_R2R_CLICK_NOT_NOW)
                addAction(ACTION_R2R_REMOVE_NOTI)
            },
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
    }

    private val pendingIntentFlags = pendingIntentDefaultFlags

    fun clickNotNowIntent(
        context: Context,
        flags: Int = pendingIntentFlags,
    ): PendingIntent {
        val intent = Intent(ACTION_R2R_CLICK_NOT_NOW).apply {
            setPackage(context.packageName)
        }

        return PendingIntent.getBroadcast(
            context,
            REQUEST_CODE,
            intent,
            flags
        )
    }

    fun removeNotiIntent(
        context: Context,
        flags: Int = pendingIntentFlags,
    ): PendingIntent {
        val intent = Intent(ACTION_R2R_REMOVE_NOTI).apply {
            setPackage(context.packageName)
        }

        return PendingIntent.getBroadcast(
            context,
            REQUEST_CODE,
            intent,
            flags
        )
    }

}
