package com.nbpt.app.androidcomponent.notireceiver

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object RepeatNotiRemoveEventReceiver : BroadcastReceiver(), KoinComponent {

    private val appContext: Context by inject()

    @Suppress("MemberVisibilityCanBePrivate")
    val ACTION_REPEAT_NOTI_REMOVE = "${appContext.packageName}.ACTION_REPEAT_NOTI_REMOVE"

    private const val EXTRA_KEY_NOTI_ID = "extra_key_noti_id"

    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent?.action == ACTION_REPEAT_NOTI_REMOVE) {
            val notiId = intent.getIntExtra(EXTRA_KEY_NOTI_ID, -1)
        }
    }

    fun register(context: Context) {
        ContextCompat.registerReceiver(
            context,
            RepeatNotiRemoveEventReceiver,
            IntentFilter(ACTION_REPEAT_NOTI_REMOVE),
            ContextCompat.RECEIVER_NOT_EXPORTED
        )
    }

    fun repeatNotiRemoveIntent(
        context: Context,
        notiId: Int,
        flags: Int
    ): PendingIntent? {
        val intent = Intent().apply {
            setPackage(context.packageName)
            action = ACTION_REPEAT_NOTI_REMOVE
            putExtra(EXTRA_KEY_NOTI_ID, notiId)
        }

        return PendingIntent.getBroadcast(
            context,
            notiId,
            intent,
            flags
        )
    }

}
