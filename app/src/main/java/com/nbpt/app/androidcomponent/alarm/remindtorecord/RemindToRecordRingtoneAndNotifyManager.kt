@file:Suppress("ConstPropertyName")

package com.nbpt.app.androidcomponent.alarm.remindtorecord

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.Ringtone
import android.media.RingtoneManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.compose.ui.graphics.toArgb
import androidx.core.app.NotificationCompat
import com.nbpt.app.NotificationActionNavigator
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.ui.theme.AppTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.core.graphics.scale

data class RemindToRecordRingtone(
    val durationMillis: Long = 60 * 1000L,
    val soundEnable: Boolean = true,
    val vibrateEnable: Boolean = true,
)

data class RemindToRecordNoti(
    val title: String,
    val content: String,
    val goToRecordNaviRoute: String,
)

private const val RemindToRecordNotiChannelId = "RemindToRecordNotiChannelId"
private const val RemindToRecordNotiId = 0x2
private const val TAG = "RemindToRecordRingtoneAndNotifyManager"

private val defaultVibratePattern = longArrayOf(0, 1000, 1000)

class RemindToRecordRingtoneAndNotifyManager(
    private val context: Context,
) {
    private val androidVibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val vibratorManager =
            context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as? VibratorManager
        vibratorManager?.defaultVibrator
    } else {
        @Suppress("DEPRECATION")
        context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
    }

    private val androidNotificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        context.getSystemService(NotificationManager::class.java)
    } else {
        null
    }

    private var ringtone: Ringtone? = null

    fun ringtoneAndNotify(
        alarmType: RemindToRecordAlarmType,
        remindToRecordRingtone: RemindToRecordRingtone,
        remindToRecordNoti: RemindToRecordNoti,
    ) {
        ringtone(remindToRecordRingtone)
        notify(alarmType, remindToRecordNoti)
    }

    fun cancelRingtoneIfNeeded() {
        androidVibrator?.cancel()
        ringtone?.stop()
        ringtone = null
    }

    private var autoStopRingtoneJob: Job? = null
    fun ringtone(
        remindToRecordRingtone: RemindToRecordRingtone
    ) {
        autoStopRingtoneJob?.cancel()
        cancelRingtoneIfNeeded()

        if (remindToRecordRingtone.soundEnable) {
            val createRingtone: Ringtone? = RingtoneManager.getRingtone(
                context,
                RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM)
            )
            ringtone = createRingtone

            createRingtone?.play()
        }

        if (remindToRecordRingtone.vibrateEnable) {
            doVibrate()
        }

        autoStopRingtoneJob = GlobalScope.launch(Dispatchers.Main) {
            delay(remindToRecordRingtone.durationMillis)
            cancelRingtoneIfNeeded()
        }
    }

    fun notify(
        alarmType: RemindToRecordAlarmType,
        remindToRecordNoti: RemindToRecordNoti
    ) {
//        val fullScreenPendingIntent = PendingIntent.getActivity(
//            context,
//            RemindToRecordNotiId,
//            Intent().apply {
//                setPackage(context.packageName)
//            },
//            pendingIntentDefaultFlags
//        )
        val notificationId = alarmType.hashCode()

        val goToRecord =
            SpannableString(context.getString(alarmType.actionButtonStringId)).apply {
                setSpan(
                    ForegroundColorSpan(AppTheme.Color.Primary.toArgb()),
                    0,
                    length,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }

        val goToRecordPendingIntent = clickGoToRecordIntent(
            context,
            remindToRecordNoti.goToRecordNaviRoute,
            notificationId
        )

        val goToRecordAction = NotificationCompat.Action.Builder(
            0,
            goToRecord,
            goToRecordPendingIntent
        ).build()

        val notNow = SpannableString(context.getString(R.string.text_not_now)).apply {
            setSpan(
                ForegroundColorSpan(AppTheme.Color.Red.toArgb()),
                0,
                length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        val notNowAction = NotificationCompat.Action.Builder(
            0,
            notNow,
            RemindToRecordAlarmReceiver.clickNotNowIntent(context)
        ).build()

        val removeNotiIntent = RemindToRecordAlarmReceiver.removeNotiIntent(context)

        val alarmRes = alarmType.iconResId

        val rawBitmap = BitmapFactory.decodeResource(
            context.resources,
            alarmRes
        )
        val iconWidth: Int =
            context.resources.getDimensionPixelSize(android.R.dimen.notification_large_icon_width)
        val scaledBitmap = rawBitmap.scale(iconWidth, iconWidth, false)

        val notificationBuilder =
            NotificationCompat.Builder(context, RemindToRecordNotiChannelId).apply {
                priority = NotificationCompat.PRIORITY_HIGH
                setContentIntent(goToRecordPendingIntent)
                setDeleteIntent(removeNotiIntent)
                setSmallIcon(R.drawable.ic_noti_alarm)
                setLargeIcon(scaledBitmap)
                setContentTitle(remindToRecordNoti.title)
                setContentText(remindToRecordNoti.content)
                addAction(notNowAction)
                addAction(goToRecordAction)
                setGroup(TAG)
//                setFullScreenIntent(fullScreenPendingIntent, true)
            }

        createNotificationChannel()

        androidNotificationManager?.notify(
            notificationId,
            notificationBuilder.build()
        )

        logEventRecord("alarm_noti_show")
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                RemindToRecordNotiChannelId,
                "R2R",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                setSound(null, null)
            }

            androidNotificationManager?.createNotificationChannel(serviceChannel)
        }
    }

    private fun doVibrate() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrationEffect =
                VibrationEffect.createWaveform(defaultVibratePattern, 0)
            androidVibrator?.vibrate(vibrationEffect)
        } else {
            androidVibrator?.vibrate(defaultVibratePattern, -1)
        }
    }

    private fun clickGoToRecordIntent(
        context: Context,
        naviRoute: String,
        notificationId: Int,
        flags: Int = pendingIntentDefaultFlags,
    ): PendingIntent {
        return NotificationActionNavigator.createNavigateIntent(
            context = context,
            notificationId = notificationId,
            route = naviRoute,
            clickEventRecord = "click_alarm_noti_go_to_record",
            flags = flags,
            withRingtone = true
        )
    }
}
