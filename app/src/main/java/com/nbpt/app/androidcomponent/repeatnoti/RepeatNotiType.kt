package com.nbpt.app.androidcomponent.repeatnoti

import com.nbpt.app.R
import com.nbpt.app.androidcomponent.fixednoti.NotiNavAction

//language=markdown
//private val md = """
//    tid 1 -> home tools
//    tid 2 -> home articles
//    tid 3 -> hr
//    tid 4 -> bp
//    tid 5 -> bs
//""".trimIndent()

sealed class RepeatNotiType(
    val tid: Int,
    val name: String,
    val navAction: NotiNavAction,
) {
    data object HealthRemindDefault : RepeatNotiType(
        tid = 1,
        name = "health_remind",
        navAction = NotiNavAction.HOME_TOOLS,
    )

    data object HealthArticleDefault : RepeatNotiType(
        tid = 2,
        name = "health_article",
        navAction = NotiNavAction.HOME_ARTICLES,
    )

    data object HealthRemindHR : RepeatNotiType(
        tid = 3,
        name = "health_remind_hr",
        navAction = NotiNavAction.HR,
    )

    data object HealthRemindBP : RepeatNotiType(
        tid = 4,
        name = "health_remind_bp",
        navAction = NotiNavAction.BP,
    )

    data object HealthRemindBS : RepeatNotiType(
        tid = 5,
        name = "health_remind_bs",
        navAction = NotiNavAction.BS,
    )


    fun imgResId(index: Int): Int {
        return when (this) {
            HealthRemindDefault -> when (index) {
                0 -> R.drawable.img_noti_1_1
                1 -> R.drawable.img_noti_1_2
                2 -> R.drawable.img_noti_1_3
                else -> R.drawable.img_noti_1_2
            }

            HealthArticleDefault -> when (index) {
                0 -> R.drawable.img_noti_2_1
                1 -> R.drawable.img_noti_2_2
                2 -> R.drawable.img_noti_2_3
                3 -> R.drawable.img_noti_2_4
                4 -> R.drawable.img_noti_2_5
                5 -> R.drawable.img_noti_2_6
                else -> R.drawable.img_noti_2_4
            }

            HealthRemindHR -> R.drawable.img_noti_1_3
            HealthRemindBP -> R.drawable.img_noti_1_1
            HealthRemindBS -> R.drawable.img_noti_1_2
        }
    }

    companion object {
        fun valueOf(tid: Int?): RepeatNotiType? {
            return when (tid) {
                HealthRemindDefault.tid -> HealthRemindDefault
                HealthArticleDefault.tid -> HealthArticleDefault
                HealthRemindHR.tid -> HealthRemindHR
                HealthRemindBP.tid -> HealthRemindBP
                HealthRemindBS.tid -> HealthRemindBS
                else -> null
            }
        }
    }
}