@file:Suppress("LocalVariableName", "PrivatePropertyName", "PropertyName")


package com.nbpt.app.androidcomponent.repeatnoti

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.nbpt.app.androidcomponent.fixednoti.NotiNavAction
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.biz.remoteconfig.RepeatNotiGroup
import com.nbpt.app.biz.remoteconfig.RepeatNotiPushStrategy
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.random.Random
import kotlin.time.Duration.Companion.seconds
import kotlin.time.DurationUnit
import kotlin.time.toDuration

val hasShowNoti = MutableStateFlow(false)

sealed class RemoteConfigMessageRepeatNotification(
    val tag: String,
    private val notiIdStartingNumber: Int,
) : KoinComponent {

    protected abstract val notiGroupConfig: RepeatNotiGroup?
    abstract suspend fun notiPushStrategy(): RepeatNotiPushStrategy

    private val NEXT_NOTI_INSTANT = longPreferencesKey("${tag}_next_noti_instant")
    private val NEXT_NOTI_INDEX = intPreferencesKey("${tag}_next_noti_index")

    protected val context: Context by inject()
    protected val remoteConfig: RealRemoteConfig by inject()
    protected val userBehaviorDataStore: UserBehaviorDataStore by inject()

    private val noti: HandsUpNotiManager by inject()

    private val Context._dataStore: DataStore<Preferences> by preferencesDataStore(name = tag)

    private suspend fun nextNotiInstant(): Instant {
        val nextNotiInstantSeconds = context
            ._dataStore.data
            .map { it[NEXT_NOTI_INSTANT] }
            .first() ?: 0L

        return Instant.fromEpochSeconds(nextNotiInstantSeconds)
    }

    private suspend fun setNextNotiInstant(instant: Instant) {
        debugLog(tag = tag) { "setNextNotiInstant instant: $instant" }

        context._dataStore.edit {
            it[NEXT_NOTI_INSTANT] = instant.epochSeconds
        }
    }

    private suspend fun nextNotiIndex(): Int {
        return context
            ._dataStore.data
            .map { it[NEXT_NOTI_INDEX] }
            .first() ?: 0
    }

    private suspend fun setNextNotiIndex(index: Int) {
        context._dataStore.edit {
            it[NEXT_NOTI_INDEX] = index
        }
    }

    suspend fun configureFirstNextNotiInstant(
        instant: Instant = nowInstant(),
        forceConfigure: Boolean = false
    ) {
        if (hasShowNoti.first()) return

        if (nextNotiInstant().epochSeconds == 0L || forceConfigure) {
            setNextNotiInstant(
                instant + notiPushStrategy()
                    .firstPushDelayMinutes
                    .toDuration(DurationUnit.MINUTES)
            )

            debugLog(tag = tag) { "configureFirstNextNotiInstant() finish" }
        }
    }

    @Volatile
    private var isProcessingShowNotification: Boolean = false

    open suspend fun tryToShowNotification(
        instant: Instant,
        immediately: Boolean = false,
        randomDelaySeconds: Int? = null,
    ) {
        if (
            instant >= nextNotiInstant() || immediately
        ) {
            val messages = notiGroupConfig?.sortedMessages
            if (messages?.isNotEmpty() == true) {
                val _nextNotiIndex = nextNotiIndex()

                val _actualNextNotiIndex = if (messages.indices.contains(_nextNotiIndex)) {
                    _nextNotiIndex
                } else {
                    0
                }

                val notiMsg = messages.getOrNull(_actualNextNotiIndex) ?: return

                val notiType = notiMsg.typeId.let(RepeatNotiType::valueOf)

                debugLog(tag = tag) { "isProcessingShowNotification: $isProcessingShowNotification" }

                if (isProcessingShowNotification) return
                isProcessingShowNotification = true

                val actionEvent = notiType?.name

                val notiId = notiIdStartingNumber + _actualNextNotiIndex
                val title = notiMsg.title
                val content = notiMsg.content
                val imageRes = notiType?.imgResId(_actualNextNotiIndex)

                randomDelaySeconds?.let { _ ->
                    delay(randomInXSecondsDelay(randomDelaySeconds).seconds)
                }

                if ((instant >= nextNotiInstant() || immediately).not()) {
                    isProcessingShowNotification = false
                    return
                }

                if (RepeatNotiLimiter.needExecute(instant).not()) {
                    isProcessingShowNotification = false
                    return
                }

                debugLog(tag = tag) { "start to noti" }
                withContext(Dispatchers.Main.immediate) {
                    noti.notify(
                        context = context,
                        title = title,
                        content = content,
                        channelId = tag,
                        notificationId = notiId,
                        notiNavAction = notiType?.navAction ?: NotiNavAction.HOME_TOOLS,
                        imageRes = imageRes,
                        clickEventRecords = arrayListOf<String>().apply {
                            actionEvent?.let { _ -> add("click_pollin_noti_$actionEvent") }
                            add("click_pollin_noti")
                        },
                    )
                }
                RepeatNotiLimiter.incrementCount()
                hasShowNoti.emit(true)

                actionEvent?.let { _ ->
                    logEventRecord("show_notification_$actionEvent")
                }

                val calculateNextNotiIndex =
                    if (messages.indices.contains(_actualNextNotiIndex + 1)) {
                        _actualNextNotiIndex + 1
                    } else {
                        0
                    }
                setNextNotiIndex(calculateNextNotiIndex)

                if (!immediately) {
                    val calculateNextNotiInstant =
                        instant + notiPushStrategy().repeatIntervalOfMinutes.toDuration(DurationUnit.MINUTES)
                    setNextNotiInstant(calculateNextNotiInstant)
                }

                isProcessingShowNotification = false
            }
        }
    }

    private fun randomInXSecondsDelay(x: Int): Int {
        return Random.nextInt(from = 0, until = x).apply {
            debugLog(tag = tag) { "randomInXSecondsDelay: $this" }
        }
    }
}
