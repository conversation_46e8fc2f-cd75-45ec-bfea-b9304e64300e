package com.nbpt.app.androidcomponent.repeatnoti

import com.nbpt.app.biz.remoteconfig.RepeatNotiGroup
import com.nbpt.app.biz.remoteconfig.RepeatNotiPushStrategy

data object ArticleRepeatNoti : RemoteConfigMessageRepeatNotification(
    tag = "ARN",
    notiIdStartingNumber = 1100,
) {
    override val notiGroupConfig: RepeatNotiGroup?
        get() = remoteConfig.articleNotiGroup
//    = RepeatNotiGroup.Test1

    override suspend fun notiPushStrategy(): RepeatNotiPushStrategy {
        return remoteConfig.repeatNotiPushStrategy( this)
//        return RepeatNotiPushStrategy(1,1)
    }
}