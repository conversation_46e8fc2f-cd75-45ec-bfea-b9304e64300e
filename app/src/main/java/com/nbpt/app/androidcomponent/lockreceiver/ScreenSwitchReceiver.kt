package com.nbpt.app.androidcomponent.lockreceiver

import android.app.KeyguardManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.nbpt.app.androidcomponent.repeatnoti.ArticleRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemindRepeatNoti
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.isNotificationPermissionGranted
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch


object ScreenSwitchReceiver : BroadcastReceiver() {

    var screenOn: Boolean = true
        private set

    var isProcessorForeground: Boolean? = null
        private set

    private var keyguardManager: KeyguardManager? = null

    override fun onReceive(context: Context?, intent: Intent?) {
        context ?: return
        when (intent?.action) {
            Intent.ACTION_SCREEN_OFF -> {
                debugLog(tag = "ScreenSwitchReceiver") { "Intent.ACTION_SCREEN_OFF" }
                screenOn = false
            }

            Intent.ACTION_SCREEN_ON -> {
                debugLog(tag = "ScreenSwitchReceiver") { "Intent.ACTION_SCREEN_ON" }
                screenOn = true
                handle(context)
            }

            Intent.ACTION_USER_PRESENT -> {
                debugLog(tag = "ScreenSwitchReceiver") { "Intent.ACTION_USER_PRESENT" }
                screenOn = true
                handle(context)
            }
        }
    }

    fun register(context: Context) {
        keyguardManager = context.getSystemService<KeyguardManager>()

        ContextCompat.registerReceiver(
            context,
            ScreenSwitchReceiver,
            IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_SCREEN_ON)
                addAction(Intent.ACTION_USER_PRESENT)
            },
            ContextCompat.RECEIVER_NOT_EXPORTED
        )

        ProcessLifecycleOwner.get().lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                debugLog(tag = "ScreenSwitchReceiver") { "Processor onStart" }
                isProcessorForeground = true
            }

            override fun onStop(owner: LifecycleOwner) {
                debugLog(tag = "ScreenSwitchReceiver") { "Processor onStop" }
                isProcessorForeground = false
            }
        })
    }

    fun isScreenOnAndUnlocked(): Boolean {
        if (isProcessorForeground == true) {
            return true
        }

        return screenOn && isScreenUnlocked()
    }

    fun isScreenUnlocked(): Boolean {
        return !(keyguardManager?.isKeyguardLocked ?: true)
    }

    private fun handle(context: Context) {
        if (context.isNotificationPermissionGranted()
            && isScreenOnAndUnlocked().apply {
                debugLog(tag = "ScreenSwitchReceiver") { "Current screen is on and unlocked: $this" }
            }
        ) {
            val instant = nowInstant()
            GlobalScope.launch {
                launch {
                    RemindRepeatNoti.tryToShowNotification(
                        instant,
                        randomDelaySeconds = 20,
                    )
                }

                launch {
                    ArticleRepeatNoti.tryToShowNotification(
                        instant,
                        randomDelaySeconds = 20,
                    )
                }
            }
        }
    }
}
