package com.nbpt.app.androidcomponent.fixednoti

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.util.TypedValue
import android.widget.RemoteViews
import androidx.compose.ui.text.intl.Locale
import androidx.core.app.NotificationCompat
import androidx.core.app.ServiceCompat
import com.nbpt.app.MainActivity
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.calculate.mmolLToMgDL
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.pendingIntentDefaultFlags
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.db.dao.BsRecordDao
import com.nbpt.app.data.db.dao.HrRecordDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.newFixedThreadPoolContext
import kotlinx.coroutines.withContext
import java.math.RoundingMode


class FixedNotificationHelper(
    private val context: Context,
    private val hrRecordDao: HrRecordDao,
    private val bpRecordDao: BpRecordDao,
    private val bsRecordDao: BsRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore,
    private val notiClickManager: NotiClickManager
) {
    companion object {
        private const val NotiServiceId = 0x1
        private const val NotiChannelId = "FixedNotification"

        val NOTI_CLICK_EVENT = "fixed_noti_click_event"

        data class NotiViewState(
            val bpm: String,
            val bp: String,
            val bs: String,
            val bsUnit: String
        ) {
            companion object {
                val Empty = NotiViewState(
                    bpm = "--", bp = "--/--", bs = "--", bsUnit = ""
                )
            }
        }
    }

    private val notiViewState: MutableStateFlow<NotiViewState> =
        MutableStateFlow(NotiViewState.Empty)

    private val updateNotiStateDispatcher =
        newFixedThreadPoolContext(1, "update_noti_state_dispatcher")

    private val isApiGreaterThanOrEqual31 = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

    private val androidNotificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        context.getSystemService(NotificationManager::class.java)
    } else {
        null
    }

    private val clickNotiIntent =
        Intent(context, MainActivity::class.java).putExtra(NOTI_CLICK_EVENT, NOTI_CLICK_EVENT)

    private val pendingIntent = PendingIntent.getActivity(
        context,
        NotiServiceId,
        clickNotiIntent,
        pendingIntentDefaultFlags
    )

    fun handleClickEvent(intent: Intent) {
        if (intent.getStringExtra(NOTI_CLICK_EVENT) == NOTI_CLICK_EVENT) {
            logEventRecord("click_notification_bar")
        }
    }

    fun startNoti(
        service: FixedNotiService?,
        context: Context = this.context,
        notiViewState: NotiViewState = NotiViewState.Empty
    ) {
        val notiView = createNotiRemoteViews(notiViewState)

        val notiViewExpanded = if (isApiGreaterThanOrEqual31)
            createNotiRemoteViewsExpanded(notiViewState)
        else null

        val noti = NotificationCompat.Builder(context, NotiChannelId).apply {
            setSmallIcon(R.drawable.ic_noti_heart)
            setContentTitle(context.getString(R.string.app_name))
//            setContentText(context.getString(R.string.text_fixed_noti_content))
            setContentIntent(pendingIntent)

            setCustomContentView(notiView)
            setCustomBigContentView(notiViewExpanded)

            setOngoing(true)
            setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            setVibrate(null)
        }.build()

        createChannelIfNeeded()

        if (service == null) {
            androidNotificationManager?.notify(
                NotiServiceId,
                noti
            )
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                ServiceCompat.startForeground(
                    service,
                    NotiServiceId,
                    noti,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
                )
            } else {
                service.startForeground(NotiServiceId, noti)
            }
        }
    }

    fun updateNoti(context: Context = this.context) {
        GlobalScope.launch(updateNotiStateDispatcher) {
            runCatching {
                debugLog(tag = "FixedNotificationHelper") { "updateNoti" }

                val latestBpmText = hrRecordDao.latestRecord()?.heartRateBpm?.toString() ?: "--"
                val latestBpText = bpRecordDao.latestRecord()?.let {
                    buildString {
                        append(it.systolic.toString())
                        append("/")
                        append(it.diastolic.toString())
                    }
                } ?: "--/--"
                val latestBsMmolL = bsRecordDao.latestRecord()?.mmolL

                val (latestBsText, bsUnitText) = when (userBehaviorDataStore.bsUnit) {
                    BsUnit.SI -> {
                        (latestBsMmolL?.toString() ?: "--") to "mmol/L"
                    }

                    BsUnit.NonSI -> {
                        (latestBsMmolL?.let(::mmolLToMgDL)
                            ?.scale(1, RoundingMode.DOWN)
                            ?.toString() ?: "--") to "mg/dL"
                    }
                }

                val newNotiViewState = NotiViewState(
                    bpm = latestBpmText,
                    bp = latestBpText,
                    bs = latestBsText,
                    bsUnit = bsUnitText
                )

                if (notiViewState.first() != newNotiViewState) {
                    debugLog(tag = "FixedNotificationHelper") { "Update notification view state: $newNotiViewState" }

                    withContext(Dispatchers.Main) {
                        startNoti(null, context, newNotiViewState)
                    }
                    notiViewState.update { newNotiViewState }
                } else {
                    debugLog(tag = "FixedNotificationHelper") { "No update needed for notification view state" }
                }
            }.onFailure {
                it.printStackTrace()
            }
        }
    }


    private var channelCreated = false
    private fun createChannelIfNeeded() {
        if (channelCreated) return

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                NotiChannelId,
                context.getString(R.string.app_name),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                setSound(null, null)
            }

            androidNotificationManager?.createNotificationChannel(serviceChannel).apply {
                channelCreated = true
            }
        }
    }

    private fun createNotiRemoteViews(
        state: NotiViewState
    ): RemoteViews {
        return RemoteViews(
            context.packageName,
            R.layout.layout_health_tracker_noti
        ).apply {
            configureBasic(state)
        }
    }


    private fun createNotiRemoteViewsExpanded(
        state: NotiViewState
    ): RemoteViews {
        return RemoteViews(
            context.packageName,
            R.layout.layout_health_tracker_noti_expanded
        ).apply {
            configureBasic(state)
            configureExpanded(state)
        }
    }

    private fun RemoteViews.configureBasic(state: NotiViewState) {
        setTextViewText(R.id.tv_heart_rate_value, state.bpm)
        setTextViewText(R.id.tv_blood_pressure_value, state.bp)
        setTextViewText(R.id.tv_blood_sugar_value, state.bs)
        setTextViewText(R.id.tv_blood_sugar_unit, " " + state.bsUnit)

        setOnClickPendingIntent(
            R.id.card_heart_rate,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_HR)
        )
        setOnClickPendingIntent(
            R.id.card_blood_pressure,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_BP)
        )
        setOnClickPendingIntent(
            R.id.card_blood_sugar,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_BS)
        )
    }

    private fun RemoteViews.configureExpanded(state: NotiViewState) {
        setTextViewText(R.id.tv_heart_rate_title, context.getString(R.string.text_heart_rate))
        setTextViewText(
            R.id.tv_blood_pressure_title,
            context.getString(R.string.text_blood_pressure)
        )
        setTextViewText(R.id.tv_blood_sugar_title, context.getString(R.string.text_blood_sugar))

        val textSize = when (Locale.current.language) {
            "zh", "ko", "ja" -> 14f
            else -> 12f
        }
        setTextViewTextSize(R.id.tv_blood_pressure_title, TypedValue.COMPLEX_UNIT_SP, textSize)

        setTextViewText(R.id.btn_measure_hr, context.getString(R.string.text_measure))
        setTextViewText(R.id.btn_record_bp, context.getString(R.string.text_record))
        setTextViewText(R.id.btn_record_bs, context.getString(R.string.text_record))

        setOnClickPendingIntent(
            R.id.btn_measure_hr,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_HR_MEASURE)
        )
        setOnClickPendingIntent(
            R.id.btn_record_bp,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_BP_RECORD)
        )
        setOnClickPendingIntent(
            R.id.btn_record_bs,
            notiClickManager.createActionPendingIntent(NotiNavAction.FN_BS_RECORD)
        )
    }
}
