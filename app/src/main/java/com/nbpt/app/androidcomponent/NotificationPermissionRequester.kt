package com.nbpt.app.androidcomponent

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.navigation.NavHostController
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.MainActivity
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.openNotificationSettings
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.mmkv.mmkvWithId
import com.nbpt.app.configureFirebaseMessagingTopicIfNeeded
import com.nbpt.app.ui.screen.destinations.GuideDestination
import com.nbpt.app.ui.screen.destinations.NotificationPermissionRequesterDialogDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
//import com.google.firebase.crashlytics.ktx.crashlytics
//import com.google.firebase.ktx.Firebase
//import com.nbpt.app.biz.analytics.logEventRecord
import com.ramcosta.composedestinations.spec.DestinationStyle
import com.ramcosta.composedestinations.spec.DestinationStyleBottomSheet
import com.ramcosta.composedestinations.utils.currentDestinationFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlin.time.DurationUnit
import kotlin.time.toDuration

private const val TAG = "NotificationPermissionRequester"

class NotificationPermissionRequester(
    private val context: Context,
    private val fixedNotificationHelper: FixedNotificationHelper
) {

    private val mmkv = mmkvWithId(TAG)

    private var requestPermissionCode: Int? = null

    private var requestTimes
        get() = mmkv.decodeInt("requestTimes", 0)
        set(value) {
            mmkv.encode("requestTimes", value)
            Unit
        }

    private var latestRequestSeconds
        get() = mmkv.decodeLong("latestRequestSeconds", 0L)
        set(value) {
            mmkv.encode("latestRequestSeconds", value)
            Unit
        }

    private var requestPermissionLauncher: ActivityResultLauncher<String>? = null


    fun hasPermission(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
    }

    fun tryToRequestIfNeeded(
        activity: Activity,
    ): Boolean {
        debugLog(tag = TAG) { "tryToRequestIfNeeded requestTimes:$requestTimes" }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) return false

        if (latestRequestSeconds + 12.toDuration(DurationUnit.HOURS).inWholeSeconds > nowInstant().epochSeconds) return false

        return when (requestTimes) {
            0, 1 -> {
                showSystemRequester(activity)
            }

            2, 3, 4 -> {
                showCustomRequester(activity, false)
            }

            else -> false
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun showSystemRequester(
        activity: Activity
    ): Boolean {
        debugLog(tag = TAG) { "showSystemRequester()" }
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
//            val randomNumber = Random.nextInt(100..2000)
//
//            requestPermissionCode = randomNumber
//
//            ActivityCompat.requestPermissions(
//                activity,
//                arrayOf(Manifest.permission.POST_NOTIFICATIONS),
//                randomNumber
//            )

            requestPermissionLauncher?.run {
                try {
                    launch(Manifest.permission.POST_NOTIFICATIONS)
                    requestCounterIncrement()
                    return true
                } catch (e: Exception) {
//                    Firebase.crashlytics.recordException(e)
                    e.printStackTrace()
                }
            }
        }
        return false
    }

    fun showCustomRequester(activity: Activity, fromAlarmsModify: Boolean = false, fromPermissionsManager: Boolean = false): Boolean {
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            GlobalNavigator.navigate(
                NotificationPermissionRequesterDialogDestination(
                    fromAlarmsModify = fromAlarmsModify,
                    fromPermissionsManager = fromPermissionsManager
                ).route
            )
            return true

//            requestCounterIncrement()
        }

        return false
    }

    fun showDefaultFixedNotification() {
        fixedNotificationHelper.updateNoti(context)
//        FixedNotiService.startService(context)
    }

    fun tryToShowDefaultFixedNotification(context: Context) {
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            showDefaultFixedNotification()
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun customRequesterOpenToNotificationSettings(
        activity: Activity
    ) {
        debugLog(tag = TAG) { "customRequesterOpenToNotificationSettings()" }
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            activity.openNotificationSettings()
        }
    }

    fun registerPermissionResult(
        activity: MainActivity
    ) {
        requestPermissionLauncher =
            activity.registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (isGranted) {
                    showDefaultFixedNotification()
                    configureFirebaseMessagingTopicIfNeeded(activity)
                    logEventRecord("notification_permission_request_success")
                    debugLog { "notification_permission_request_success" }
                } else {
                    debugLog { "notification_permission_request_fail" }
                }
            }
    }

    fun requestCounterIncrement() {
        latestRequestSeconds = nowInstant().epochSeconds
        requestTimes++
        logEventRecord("notification_permission_request")
        debugLog { "notification_permission_request" }
    }
}