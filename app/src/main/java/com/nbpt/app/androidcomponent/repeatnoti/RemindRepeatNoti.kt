package com.nbpt.app.androidcomponent.repeatnoti

import com.nbpt.app.biz.remoteconfig.RepeatNotiGroup
import com.nbpt.app.biz.remoteconfig.RepeatNotiPushStrategy

data object RemindRepeatNoti : RemoteConfigMessageRepeatNotification(
    tag = "RRN",
    notiIdStartingNumber = 1000,
) {
    override val notiGroupConfig: RepeatNotiGroup?
        get() = remoteConfig.remindNotiGroup
//    = RepeatNotiGroup.Test2

    override suspend fun notiPushStrategy(): RepeatNotiPushStrategy {
        return remoteConfig.repeatNotiPushStrategy(this)
//        return RepeatNotiPushStrategy(1,1)
    }
}