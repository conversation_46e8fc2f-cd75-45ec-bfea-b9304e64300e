package com.nbpt.app.androidcomponent

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.nbpt.app.common.logger.debugLog

class TReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        val action = intent?.action
        debugLog(tag = "TReceiver") { "TReceiver onReceive, action=$action" }
    }
}