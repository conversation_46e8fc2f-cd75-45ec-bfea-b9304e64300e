package com.nbpt.app.androidcomponent

import android.content.Context
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.nbpt.app.androidcomponent.tickreceiver.TimeTickReceiver
import com.nbpt.app.common.logger.debugLog
import java.util.concurrent.TimeUnit

class DetectionScheduler(private val context: Context) {

  class DetectionWorker(private val context: Context, params: WorkerParameters) : Worker(context, params) {
    override fun doWork(): Result {
      performDetection(context)
      return Result.success()
    }

    private fun performDetection(context: Context) {
      debugLog(tag = "DetectionScheduler") { "performDetection()" }
      TimeTickReceiver.handleTimeTick(context)
    }
  }

  fun scheduleDetectionWork() {
    val detectionWorkRequest = PeriodicWorkRequestBuilder<DetectionWorker>(
      15, TimeUnit.MINUTES
    ).build()

    WorkManager.getInstance(context).enqueueUniquePeriodicWork(
      "periodicDetection",
      ExistingPeriodicWorkPolicy.UPDATE,
      detectionWorkRequest
    )
  }
}