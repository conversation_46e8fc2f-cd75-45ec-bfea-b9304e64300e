package com.nbpt.app

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.startup.Initializer
import co.touchlab.kermit.Logger
import co.touchlab.kermit.koin.KermitKoinLogger

import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.messaging
import com.google.firebase.remoteconfig.remoteConfig
import com.nbpt.app.androidcomponent.fixednoti.FixedNotiService
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.androidcomponent.repeatnoti.ArticleRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemindRepeatNoti
import com.nbpt.app.bi.BiApiRemoteConfig
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.mmkv.mmkvInit
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BpRecordsNoteDao
import com.nbpt.app.data.db.dao.BsRecordsNoteDao
import com.nbpt.app.data.db.dao.HrRecordsNoteDao
import com.nbpt.app.data.db.dao.preInsertNotes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

import kotlinx.datetime.Instant
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.GlobalContext
import org.koin.core.context.startKoin
import org.koin.java.KoinJavaComponent

import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Suppress("unused")
class AppInitializer : Initializer<Unit> {
    override fun create(context: Context) {
        context.mmkvInit()
        configureDi(context)
        configure1stTimeLaunchAppIfNeeded()
//        configureAdMob(context)
        configureFirebaseRemoteConfig()
        configureBiApiRemoteConfig()
        configureRemoteConfigMessageRepeatNotifications(8.toDuration(DurationUnit.SECONDS))
//        configureFixedNoti(context)
        loggingFirebaseToken()
        configureDefaultHeartRateRecordNotes()


    }

    override fun dependencies(): MutableList<Class<out Initializer<*>>> = mutableListOf()
}

private fun configureDi(context: Context) {
    startKoin {
        logger(KermitKoinLogger(Logger.withTag("koin")))
        androidContext(context)
        modules(injectModules)
    }
}



private fun configureFixedNoti(context: Context) {
    GlobalScope.launch(Dispatchers.Main) {
        delay(300)
        FixedNotiService.startService(context)
    }
}

private fun configureFirebaseRemoteConfig() {
    val remoteConfig = Firebase.remoteConfig

    remoteConfig.apply {
        setDefaultsAsync(R.xml.remote_config_defaults)
        fetchAndActivate().addOnCompleteListener { task ->
            debugLog { "remoteConfig fetchAndActivate() isSuccessful: ${task.isSuccessful}" }
            configureRemoteConfigMessageRepeatNotifications()
        }
    }
}

private fun configureRemoteConfigMessageRepeatNotifications(
    delayDuration: Duration? = null,
    instant: Instant? = null,
    forceConfigure: Boolean = false
) {
    GlobalScope.launch {
        delayDuration?.let { delay(delayDuration) }

        val actualInstant = if (delayDuration == null) {
            instant ?: nowInstant()
        } else {
            (instant ?: nowInstant()) - delayDuration
        }

        delayDuration?.let { delay(delayDuration) }

        RemindRepeatNoti.configureFirstNextNotiInstant(actualInstant, forceConfigure)
        ArticleRepeatNoti.configureFirstNextNotiInstant(actualInstant, forceConfigure)
    }
}

fun getFirebaseToken(block: ((String?) -> Unit)) {
    FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->
        debugLog(tag = "getFirebaseToken") { "addOnCompleteListener" }

        if (!task.isSuccessful) {
            debugLog(tag = "getFirebaseToken") { "Fetching FCM registration token failed" + task.exception }
            return@OnCompleteListener
        }

        // Get new FCM registration token
        val token = task.result ?: null

        block(token)
        debugLog(tag = "getFirebaseToken") { "loggingFirebaseToken() token: $token" }
    })
}

private fun loggingFirebaseToken() {
    if (!BuildConfig.DEBUG) return
    getFirebaseToken {}
}


private fun configureDefaultHeartRateRecordNotes() {
    GlobalScope.launch {
        val userBehaviorDataStore: UserBehaviorDataStore =
            KoinJavaComponent.get(UserBehaviorDataStore::class.java)

        val hasConfigureDefaultHeartRateRecordNotes =
            userBehaviorDataStore.hasConfigureDefaultRecordNotes()


        if (!hasConfigureDefaultHeartRateRecordNotes) {
            GlobalContext.get().get<BpRecordsNoteDao>().preInsertNotes()
            GlobalContext.get().get<HrRecordsNoteDao>().preInsertNotes()
            GlobalContext.get().get<BsRecordsNoteDao>().preInsertNotes()
            userBehaviorDataStore.setHasConfigureDefaultRecordNotes(true)
        }
    }
}

private var hasConfigureFirebaseMessagingTopic: Boolean = false
fun configureFirebaseMessagingTopicIfNeeded(context: Context) {
    GlobalScope.launch(Dispatchers.Main.immediate) {
        if (hasConfigureFirebaseMessagingTopic) return@launch

        if (!context.isNotificationPermissionGranted()) return@launch

        hasConfigureFirebaseMessagingTopic = true

        logEventRecord("fcm_subscribe_topic")
        Firebase.messaging.subscribeToTopic("wm_noti_a")
            .addOnCompleteListener { task ->
                hasConfigureFirebaseMessagingTopic = task.isSuccessful
                logEventRecord("fcm_subscribe_topic_successful_${task.isSuccessful}")
            }
    }
}

fun Context.isNotificationPermissionGranted(): Boolean {
    return when {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
            ContextCompat.checkSelfPermission(
                this,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        }

//        Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
//            // 对于 Android 8.0 (API 26) 到 Android 12 (API 32)
//            // 检查通知渠道是否启用
//            NotificationManagerCompat.from(this).areNotificationsEnabled()
//        }

        else -> {
            // 对于 Android 7.1 (API 25) 及以下版本
            // 通知权限默认被授予
            true
        }
    }
}

fun configureBiApiRemoteConfig() {
    val koin = GlobalContext.get()

    koin.get<BiApiRemoteConfig>().init(
        onUnstableUpdateConfig = {
            GlobalScope.launch {
                configureRemoteConfigMessageRepeatNotifications(
                    instant = koin.get<UserBehaviorDataStore>().get1stTimeLaunchAppInstant(),
                    forceConfigure = true
                )
            }
        }
    )
}

private fun configure1stTimeLaunchAppIfNeeded() {
    val ubds = GlobalContext.get().get<UserBehaviorDataStore>()

    GlobalScope.launch {
        if (ubds.get1stTimeLaunchAppInstant().epochSeconds == 0L) {
            ubds.firstTimeLaunchApp(nowInstant())
        }
    }
}
