package com.nbpt.app.ui.screen.language

import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.Support
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.NavigateAction
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import java.util.Locale

@Destination
@Composable
fun Language(
    navigator: DestinationsNavigator
) {
    val context: Context = LocalContext.current
    val viewModel: LanguageViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()
    val composeLocale = androidx.compose.ui.text.intl.Locale.current

    val maxInterstitialAdHelper: MaxInterstitialAdManager = koinInject()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    LaunchedEffect(composeLocale) {
        viewModel.onRefresh(composeLocale.toJavaLocal())
    }

    val onTryToShowInterAdAndNavAction: (NavigateAction) -> Unit = {
        if (useLegacyAd) {
            maxInterstitialAdHelper.tryToShowAd(
                from = "language",
                onAdLoadingAfter = { it(navigator) }
            )
        } else {
            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                activity = context.findActivity(),
                navAction = it,
                adPlaceName = "language"
            )
        }
    }

    val onBack = {
        if (viewState.showBackIcon) {
            onTryToShowInterAdAndNavAction {
                navigator.navigateUp()
            }
        } else {
            onTryToShowInterAdAndNavAction {
                val compatible = Support.compatibleLanguage(Locale.getDefault()) ?: Locale.ENGLISH
                viewModel.onConfirmLanguage(compatible)
            }
        }
    }

    Language(
        navigator = navigator,
        viewModel = viewModel,
        viewState = viewState,
        composeLocale = composeLocale,
        onConfirmLanguage = {
            onTryToShowInterAdAndNavAction {
                viewModel.onConfirmLanguage()
            }
        },
        onBack = onBack
    )


    BackHandler(onBack = onBack)
}

@Composable
private fun Language(
    navigator: DestinationsNavigator,
    viewModel: LanguageViewModel,
    viewState: LanguageViewState,
    composeLocale: androidx.compose.ui.text.intl.Locale,
    onConfirmLanguage: () -> Unit,
    onBack: () -> Unit
) {
    Scaffold(
        topBar = {
            LanguageAppBar(
                viewState = viewState,
                navUp = onBack,
            )
        },
        bottomBar = {
            ListAdaptiveRectangleAd(
                bannerAdPlace = BannerAdPlaceholder.Language,
                nativeAdPlace = NativeAdPlaceholder.Language,
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding(),
            )
        },
        floatingActionButtonPosition = FabPosition.Center,
        floatingActionButton = {
            Box(
                modifier = Modifier
                    .padding(bottom = 12.dp)
                    .fillMaxWidth(0.6f)
                    .clip(CircleShape)
                    .background(
                        brush = AppTheme.Color.PrimaryBrush,
                        shape = CircleShape
                    )
                    .clickable(onClick = onConfirmLanguage),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(id = R.string.text_apply),
                    modifier = Modifier.padding(vertical = 12.dp),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        },
        containerColor = AppTheme.Color.background
    ) {

        val lazyListState = rememberLazyListState()
        var hasLazyListRender by remember { mutableStateOf(false) }

        LaunchedEffect(composeLocale, hasLazyListRender) {
            if (hasLazyListRender) {
                val compatibleLanguage = Support.compatibleLanguage(composeLocale.toJavaLocal())
                val indexOf = Support.localeList.indexOf(compatibleLanguage)
                lazyListState.scrollToItem(
                    if (indexOf < 0) {
                        0
                    } else indexOf * 2
                )
            }
        }

        LazyColumn(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            state = lazyListState,
        ) {
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }

            viewState.localeList.forEach { locale: Locale ->
                item {
                    LanguageSelectItem(
                        onClick = { viewModel.onLanguageSelect(locale) },
                        locale = locale,
                        selectedLocale = viewState.selectedLocale
                    )
                }
                item {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }

            item {
                Text(
                    text = stringResource(R.string.text_other_languages_coming_soon),
                    modifier = Modifier
                        .padding(horizontal = 32.dp, vertical = 20.dp)
                        .bodyWidth(),
                    textAlign = TextAlign.Center
                )
            }

            item {
                Spacer(modifier = Modifier.height((12 * 2 + 56).dp))
            }

            hasLazyListRender = true
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LanguageAppBar(
    viewState: LanguageViewState,
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_language)) },
        modifier = modifier,
        navigationIcon = {
            if (viewState.showBackIcon) {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            }
        }
    )
}

@Composable
private fun LanguageSelectItem(
    onClick: (Locale) -> Unit,
    locale: Locale,
    selectedLocale: Locale,
    modifier: Modifier = Modifier,
) {
    val (backgroundColor, textColor) = if (selectedLocale == locale) {
        AppTheme.Color.Primary to Color.White
    } else {
        MaterialTheme.colorScheme.surface to MaterialTheme.colorScheme.onSurface
    }

    val languageText = locale.localeLanguage()

    Surface(
        onClick = { onClick(locale) },
        shape = RoundedCornerShape12Dp,
        color = backgroundColor,
        modifier = modifier.defShadow()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = languageText,
                color = textColor,
                modifier = Modifier.padding(vertical = 16.dp)
            )

            Spacer(modifier = Modifier.weight(1f))

            if (selectedLocale == locale) {
                Icon(
                    imageVector = Icons.Rounded.Check,
                    contentDescription = null,
                    modifier = Modifier.size(26.dp)
                )
            }
        }
    }
}

private fun Locale.localeLanguage(): String {
    return buildString {
        append(getDisplayLanguage(this@localeLanguage))

        val localeRegion = getDisplayCountry(this@localeLanguage)
        if (localeRegion.trim().isNotEmpty()) {
            append(" - $localeRegion")
        }
    }
}

private fun androidx.compose.ui.text.intl.Locale.toJavaLocal(): Locale {
    return Locale(this.language, this.region)
}

@Preview(locale = "en")
@Composable
private fun LanguagePreview() {
    Language(navigator = EmptyDestinationsNavigator)
}
