package com.nbpt.app.ui.screen.bmicalculator

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.adt.BmiCalculateUnit
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BodyHeightSelectorContentImperial
import com.nbpt.app.ui.common.BodyHeightSelectorContentMetric
import com.nbpt.app.ui.common.BodyWeightSelectorContentImperial
import com.nbpt.app.ui.common.BodyWeightSelectorContentMetric
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.destinations.BmiResultDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bmi.BmiTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun BmiCalculator(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
    val viewModel: BmiCalculatorViewModel = koinViewModel()

    val viewState by viewModel.collectAsState()


    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    viewModel.collectSideEffect {
        when (it) {
            is BmiCalculatorSideEffect.ToResult -> {
                if (useLegacyAd) {
                    interstitialAdManager.tryToShowAd(from = "bmi_done", onAdLoadingAfter = {
                        navigator.navigate(
                            BmiResultDestination(
                                bmi = it.bmi,
                                bmiStatus = it.bmiStatus
                            )
                        )
                    })
                } else {
                    admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                        activity = context.findActivity(),
                        navAction = {
                            navigate(
                                BmiResultDestination(
                                    bmi = it.bmi,
                                    bmiStatus = it.bmiStatus
                                )
                            )
                        },
                        adPlaceName = "bmi_done"
                    )
                }
            }
        }
    }

    BmiTheme {
        BmiCalculator(
            navigator = navigator,
            viewModel = koinViewModel(),
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState
        )
    }
}

@Composable
fun BmiCalculator(
    navigator: DestinationsNavigator,
    viewModel: BmiCalculatorViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: BmiCalculatorViewState,
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
    val ratingManager: RatingManager = koinInject()

    val backAction: DestinationsNavigator.() -> Unit = {
        debugLog { "viewState.hasCalculate: ${viewState.hasCalculate}" }

        if (viewState.hasCalculate) {
            viewModel.onCacheData()
            navigateUp()
            GlobalScope.launch {
                ratingManager.tryToOpenRatingSheet(context.findActivity())
            }
        } else {
            viewModel.onCacheData()
            navigateUp()
        }
    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd(from = "bmi_back_home", onAdLoadingAfter = {
                backAction(navigator)
            })
        } else {
            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                activity = context.findActivity(),
                navAction = { backAction(this) },
                adPlaceName = "bmi_back_home"
            )
        }
        Unit
    }

    BackHandler {
        onBack()
    }

    Scaffold(
        topBar = {
            BmiCalculatorAppBar(navUp = onBack)
        },
        bottomBar = {
            SmartRectAd(
                pageType = SmartAdPageType.FEAT_BOTTOM_BAR,
                bannerAdPlace = BannerAdPlaceholder.BMI,
                nativeAdPlace = NativeAdPlaceholder.Bmi,
                modifier = Modifier.navigationBarsPadding(),
            )
        },
        containerColor = AppTheme.Color.BmiBackground
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
        ) {
            BlankSpacer(height = 16.dp)

            BmiCalculateUnitSwitch(
                currentUnit = viewState.bmiCalculateUnit,
                onSwitch = viewModel::onUnitSwitch,
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 16.dp)

            BmiCalculateBodyHeightContent(
                viewState = viewState,
                onCmChange = viewModel::onCmChange,
                onFtChange = viewModel::onFtChange,
                onInChange = viewModel::onInChange,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .defShadow()
            )

            BlankSpacer(height = 16.dp)

            BmiCalculateBodyWeightContent(
                viewState = viewState,
                onKgChange = viewModel::onKgChange,
                onLbsChange = viewModel::onLbsChange,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .defShadow()
            )

            BlankSpacer(height = 20.dp)

            SmartRectAd(
                pageType = SmartAdPageType.FEAT_CONTENT,
                bannerAdPlace = BannerAdPlaceholder.BMI,
                nativeAdPlace = NativeAdPlaceholder.Bmi,
            )

            BlankSpacer(height = 20.dp)

            CardButton(
                text = stringResource(R.string.text_calculate),
                onClick = viewModel::onCalculate,
                containerBrush = AppTheme.Color.BmiBrush,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 84.dp)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BmiCalculatorAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier) {
        TopAppBar(
            title = { Text(text = stringResource(R.string.text_bmi_calculator)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.FEAT_TOP_BAR,
            bannerAdPlace = BannerAdPlaceholder.BMI,
            nativeAdPlace = NativeAdPlaceholder.Bmi,
        )
    }
}

@Composable
private fun BmiCalculateUnitSwitch(
    currentUnit: BmiCalculateUnit,
    onSwitch: (BmiCalculateUnit) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Surface(
            border = BorderStroke(2.dp, AppTheme.Color.BmiPrimary),
            shape = RoundedCornerShape12Dp
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                BmiCalculateUnit.entries.forEach {
                    val (textColor, backgroundColor) = if (currentUnit == it) {
                        Color.White to AppTheme.Color.BmiPrimary
                    } else {
                        AppTheme.Color.BmiPrimary.copy(.7f) to Color.White
                    }

                    Box(
                        modifier = Modifier
                            .noRippleClickable {
                                onSwitch(it)
                            }
                            .background(backgroundColor)
                    ) {
                        Text(
                            text = it.specificUnit,
                            modifier = Modifier.padding(vertical = 10.dp, horizontal = 24.dp),
                            style = MaterialTheme.typography.bodyMedium.copy(color = textColor)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun BmiCalculateBodyHeightContent(
    viewState: BmiCalculatorViewState,
    onCmChange: (Int) -> Unit,
    onFtChange: (Int) -> Unit,
    onInChange: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(modifier = modifier, shape = RoundedCornerShape12Dp) {
        when (viewState.bmiCalculateUnit) {
            BmiCalculateUnit.Metric -> {
                BodyHeightSelectorContentMetric(
                    title = stringResource(R.string.text_body_height),
                    cm = viewState.cm,
                    onCmChange = onCmChange,
                )
            }

            BmiCalculateUnit.Imperial -> {
                BodyHeightSelectorContentImperial(
                    title = stringResource(R.string.text_body_height),
                    ft = viewState.ft,
                    `in` = viewState.`in`,
                    onFtChange = onFtChange,
                    onInChange = onInChange,
                )
            }
        }
    }
}

@Composable
private fun BmiCalculateBodyWeightContent(
    viewState: BmiCalculatorViewState,
    onKgChange: (Float) -> Unit,
    onLbsChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(modifier = modifier, shape = RoundedCornerShape12Dp) {
        when (viewState.bmiCalculateUnit) {
            BmiCalculateUnit.Metric -> {
                BodyWeightSelectorContentMetric(
                    kg = viewState.kg,
                    onKgChange = onKgChange,
                )
            }

            BmiCalculateUnit.Imperial -> {
                BodyWeightSelectorContentImperial(
                    lbs = viewState.lbs,
                    onLbsChange = onLbsChange
                )
            }
        }
    }
}

@Preview
@Composable
private fun BmiCalculateUnitSwitchPreview() {
    BmiCalculateUnitSwitch(currentUnit = BmiCalculateUnit.Imperial, onSwitch = {})
}

@Preview
@Composable
private fun BBmiCalculateBodyHeightContentPreview() {
    BmiCalculateBodyHeightContent(
        viewState = BmiCalculatorViewState(BmiCalculateUnit.Metric),
        onCmChange = {},
        onFtChange = {},
        onInChange = {}
    )
}

@Preview
@Composable
private fun BBmiCalculateBodyWeightContentPreview() {
    BmiCalculateBodyWeightContent(
        viewState = BmiCalculatorViewState(BmiCalculateUnit.Imperial),
        onKgChange = {},
        onLbsChange = {}
    )
}
