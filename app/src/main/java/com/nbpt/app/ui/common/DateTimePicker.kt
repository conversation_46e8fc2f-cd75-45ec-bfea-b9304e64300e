package com.nbpt.app.ui.common

import android.text.format.DateFormat
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.commandiron.wheel_picker_compose.WheelDateTimePicker
import com.commandiron.wheel_picker_compose.core.SelectorProperties
import com.commandiron.wheel_picker_compose.core.TimeFormat
import com.commandiron.wheel_picker_compose.core.WheelPickerDefaults
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.common.logger.debugLog
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.toJavaLocalDateTime
import kotlinx.datetime.toKotlinLocalDateTime

@Composable
fun DateTimePicker(
    dateTime: LocalDateTime?,
    onDateTimeChange: (LocalDateTime) -> Unit,
    modifier: Modifier = Modifier,
    timeFormat: TimeFormat = if (DateFormat.is24HourFormat(LocalContext.current)) TimeFormat.HOUR_24 else TimeFormat.AM_PM,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(
        border = BorderStroke(2.dp, MaterialTheme.colorScheme.primary.copy(.9f))
    )
) {
    val nowLocalDateTime = nowInstant().toLocalDatetime().toJavaLocalDateTime()
    val minDateTime = LocalDateTime(nowLocalDateTime.year, 1, 1, 0, 0).toJavaLocalDateTime()
    val maxDateTime = LocalDateTime(nowLocalDateTime.year + 2, 1, 1, 0, 0).toJavaLocalDateTime()

    val yearsRange = IntRange(minDateTime.year, maxDateTime.year - 1)

    if (dateTime == null) {
        WheelDateTimePicker(
            startDateTime = nowLocalDateTime,
            minDateTime = minDateTime,
            maxDateTime = maxDateTime,
            yearsRange = yearsRange,
            modifier = modifier,
            timeFormat = timeFormat,
            textStyle = textStyle,
            selectorProperties = selectorProperties,
        )
    } else {
        WheelDateTimePicker(
            startDateTime = dateTime.toJavaLocalDateTime(),
            minDateTime = minDateTime,
            maxDateTime = maxDateTime,
            yearsRange = yearsRange,
            modifier = modifier,
            timeFormat = timeFormat,
            textStyle = textStyle,
            selectorProperties = selectorProperties,
            onSnappedDateTime = { selectDateTime ->
                onDateTimeChange(selectDateTime.toKotlinLocalDateTime())
            }
        )
    }
}
