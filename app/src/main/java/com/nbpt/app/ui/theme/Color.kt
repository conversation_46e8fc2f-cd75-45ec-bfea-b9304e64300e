package com.nbpt.app.ui.theme
import androidx.compose.ui.graphics.Color


val seed = Color(0xFF43435E)

val md_theme_light_primary = Color(0xFF7A4CC6)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
//val md_theme_light_primaryContainer = Color(0xFFD2E4FF)
val md_theme_light_primaryContainer = /*Color(0xFFD2E4FF)*/ Color.White
val md_theme_light_onPrimaryContainer = /*Color(0xFF001C37)*/ seed
val md_theme_light_secondary = Color(0xFF7A4CC6)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFF0DAFF)
val md_theme_light_onSecondaryContainer = Color(0xFF220048)
val md_theme_light_tertiary = Color(0xFF7A4CC6)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = /*Color(0xFFDAE2FF)*/ md_theme_light_primaryContainer
val md_theme_light_onTertiaryContainer = /*Color(0xFF001848)*/ seed
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFF6F0FD)
val md_theme_light_onBackground = /*Color(0xFF001B3D)*/ seed
val md_theme_light_surface = /*Color(0xFFFDFBFF)*/ md_theme_light_primaryContainer
val md_theme_light_onSurface = /*Color(0xFF001B3D)*/ seed
val md_theme_light_surfaceVariant = /*Color(0xFFDFE2EB)*/ md_theme_light_primaryContainer
val md_theme_light_onSurfaceVariant = Color(0xFF43474E)
val md_theme_light_outline = Color(0xFF73777F)
val md_theme_light_inverseOnSurface = Color(0xFFF5ECFF)
val md_theme_light_inverseSurface = Color(0xFF310062)
val md_theme_light_inversePrimary = Color(0xFFC5A1FF)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = /*Color(0xFF0F61A4)*/ md_theme_light_primaryContainer
val md_theme_light_outlineVariant = Color(0xFFC7C3CF)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFFA1C9FF)
val md_theme_dark_onPrimary = Color(0xFF00325A)
val md_theme_dark_primaryContainer = Color(0xFF004880)
val md_theme_dark_onPrimaryContainer = Color(0xFFD2E4FF)
val md_theme_dark_secondary = Color(0xFFB2C5FF)
val md_theme_dark_onSecondary = Color(0xFF002B73)
val md_theme_dark_secondaryContainer = Color(0xFF0040A2)
val md_theme_dark_onSecondaryContainer = Color(0xFFDAE2FF)
val md_theme_dark_tertiary = Color(0xFFB2C5FF)
val md_theme_dark_onTertiary = Color(0xFF002B73)
val md_theme_dark_tertiaryContainer = Color(0xFF0040A2)
val md_theme_dark_onTertiaryContainer = Color(0xFFDAE2FF)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF001B3D)
val md_theme_dark_onBackground = Color(0xFFD6E3FF)
val md_theme_dark_surface = Color(0xFF001B3D)
val md_theme_dark_onSurface = Color(0xFFD6E3FF)
val md_theme_dark_surfaceVariant = Color(0xFF43474E)
val md_theme_dark_onSurfaceVariant = Color(0xFFC3C6CF)
val md_theme_dark_outline = Color(0xFF8D9199)
val md_theme_dark_inverseOnSurface = Color(0xFF001B3D)
val md_theme_dark_inverseSurface = Color(0xFFD6E3FF)
val md_theme_dark_inversePrimary = Color(0xFF0F61A4)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFFA1C9FF)
val md_theme_dark_outlineVariant = Color(0xFF43474E)
val md_theme_dark_scrim = Color(0xFF000000)
