@file:Suppress("LocalVariableName")

package com.nbpt.app.ui.screen.heartrateeditor

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.compose.HrTheme
import com.nbpt.app.R
import com.nbpt.app.common.android.showToast
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import org.koin.core.component.KoinComponent

enum class Gender(@StringRes val textStringId: Int, val number: Int) : KoinComponent {
    Male(R.string.text_male, 1),
    Female(R.string.text_female, 2),
    Others(R.string.text_others, 0);

    companion object {
        fun numberOf(number: Int?): Gender {
            return when (number) {
                1 -> Male
                2 -> Female
                else -> Others
            }
        }
    }
}

@Composable
internal fun GenderDialog(
    gender: Gender?,
    onSelect: (Gender) -> Unit,
    onDismiss: () -> Unit
) {
    var _gender by remember {
        mutableStateOf(gender)
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
        ) {
            Surface(shape = RoundedCornerShape12Dp, color = AppTheme.Color.White) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Layout.bodyMargin),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BlankSpacer(height = 8.dp)

                    Text(
                        text = stringResource(R.string.text_please_choose_your_gender),
                        style = MaterialTheme.typography.titleMedium
                    )

                    BlankSpacer(height = 20.dp)

                    enumValues<Gender>().forEach {
                        GenderItem(
                            onClick = { select ->
                                _gender = select
                            },
                            gender = it,
                            isSelect = it == _gender,
                            modifier = Modifier.fillMaxWidth().padding(horizontal = 32.dp)
                        )
                        BlankSpacer(height = 14.dp)
                    }

                    BlankSpacer(height = 22.dp)

                    CardButton(
                        text = stringResource(R.string.text_confirm),
                        onClick = { _gender?.let { onSelect(it) } },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 40.dp),
                        containerBrush = AppTheme.Color.HrBrush
                    )

                    BlankSpacer(height = 8.dp)
                }
            }

        }
    }
}

@Composable
private fun GenderItem(
    onClick: (Gender) -> Unit,
    gender: Gender,
    isSelect: Boolean,
    modifier: Modifier = Modifier
) {
    val (checkBorderColor, checkTextColor) =
        if (isSelect)
            AppTheme.Color.HrPrimary to AppTheme.Color.textPrimary.copy(.9f)
        else
            Color.LightGray to Color.LightGray

    Surface(
        onClick = { onClick(gender) },
        modifier = modifier.defShadow(),
        shape = RoundedCornerShape12Dp,
        border = BorderStroke(if (isSelect) 3.dp else 2.dp, checkBorderColor)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = stringResource(id = gender.textStringId),
                modifier = Modifier
                    .padding(12.dp)
                    .bodyWidth(),
                style = MaterialTheme.typography.bodyLarge.copy(color = checkTextColor),
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Visible
            )
        }
    }
}

@Preview
@Composable
private fun GenderDialogPreview() {
    var selection by remember {
        mutableStateOf(Gender.Male)
    }

    val context = LocalContext.current
    LaunchedEffect(selection) {
        context.showToast(context.getString(selection.textStringId))
    }

    AppMd3Theme {
        HrTheme {
            GenderDialog(gender = selection, onSelect = { selection = it }, onDismiss = {})
        }
    }
}
