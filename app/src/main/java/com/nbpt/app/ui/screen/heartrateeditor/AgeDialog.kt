@file:Suppress("LocalVariableName")

package com.nbpt.app.ui.screen.heartrateeditor

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.nbpt.app.R
import com.nbpt.app.common.android.showToast
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.sd.lib.compose.wheel_picker.FVerticalWheelPicker
import com.sd.lib.compose.wheel_picker.FWheelPickerFocusVertical
import com.sd.lib.compose.wheel_picker.rememberFWheelPickerState

@Composable
internal fun AgeDialog(
    age: Int,
    onAgeSelect: (Int) -> Unit,
    onDismiss: () -> Unit,
) {

    var _age by remember {
        mutableStateOf(age)
    }

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
        ) {
            Surface(shape = RoundedCornerShape12Dp) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Layout.bodyMargin),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BlankSpacer(height = 8.dp)

                    Text(
                        text = stringResource(R.string.text_please_choose_your_age),
                        style = MaterialTheme.typography.titleMedium
                    )

                    BlankSpacer(height = 18.dp)

                    NumberPicker(
                        currentValue = _age,
                        minValue = 1,
                        maxValue = 120,
                        displayItemCount = 5,
                        onValueChange = {
                            _age = it
                        },
                        dividerColor = AppTheme.Color.HrPrimary
                    )

                    BlankSpacer(height = 22.dp)

                    CardButton(
                        text = stringResource(R.string.text_confirm),
                        onClick = { onAgeSelect(_age) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 40.dp),
                        containerBrush = AppTheme.Color.HrBrush
                    )

                    BlankSpacer(height = 8.dp)
                }
            }

        }

    }
}

@Composable
internal fun NumberPicker(
    currentValue: Int,
    minValue: Int,
    maxValue: Int,
    onValueChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
    displayItemCount: Int = 3,
    selectedTextColor: Color = AppTheme.Color.textPrimary,
    unselectTextColor: Color = AppTheme.Color.textSecondary,
    dividerColor: Color = AppTheme.Color.textPrimary,
    textStyle: TextStyle = MaterialTheme.typography.headlineMedium,
) {


    Column(modifier, horizontalAlignment = Alignment.CenterHorizontally) {

        val surfaceColor = AppTheme.Color.White
        Surface(color = surfaceColor, shape = RoundedCornerShape12Dp) {


            val wheelPickerState = rememberFWheelPickerState()

            var updateCount by remember { mutableIntStateOf(0) }

            LaunchedEffect(currentValue) {
                wheelPickerState.scrollToIndex(currentValue - minValue)
            }

            LaunchedEffect(Unit) {
                snapshotFlow {
                    wheelPickerState.currentIndex
                }.collect {
                    ++updateCount

                    // 1st update is collect -1 (default index)
                    // 2nd update is collect ViewState.Empty value
                    if (updateCount >= 3) {
                        onValueChange(wheelPickerState.currentIndex + minValue)
                    }
                }
            }

            FVerticalWheelPicker(
                count = maxValue - minValue + 1,
                state = wheelPickerState,
                modifier = Modifier.requiredWidthIn(max = 100.dp),
                unfocusedCount = (displayItemCount - 1) / 2,
                focus = {
                    FWheelPickerFocusVertical(
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp),
                        dividerSize = 2.dp,
                        dividerColor = dividerColor
                    )
                },
                itemHeight = 42.dp
            ) {
                val style = textStyle.copy(color = if (it == wheelPickerState.currentIndex) selectedTextColor else unselectTextColor)
                Text(
                    text = (it + minValue).toString(),
                    style = style
                )
            }
        }
    }

}

@Preview
@Composable
private fun AgeDialogPreview() {

    val context = LocalContext.current

    AgeDialog(age = 10, onAgeSelect = {
        context.showToast(it.toString())
    }) {

    }
}
