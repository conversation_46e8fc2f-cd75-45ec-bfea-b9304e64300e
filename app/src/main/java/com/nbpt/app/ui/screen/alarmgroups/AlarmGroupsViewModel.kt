package com.nbpt.app.ui.screen.alarmgroups

import android.annotation.SuppressLint
import android.app.Activity
import androidx.compose.runtime.Composable
import androidx.lifecycle.ViewModel
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmDataStore
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.ui.screen.info.InfoSideEffect
import com.ramcosta.composedestinations.spec.Direction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class AlarmGroupsViewModel(
    private val remindToRecordAlarmDataStore: RemindToRecordAlarmDataStore,
//    private val interstitialAdManager: InterstitialAdManager
) : ViewModel(), ContainerHost<AlarmGroupsViewState, AlarmGroupsSideEffect> {

    override val container: Container<AlarmGroupsViewState, AlarmGroupsSideEffect> =
        container(AlarmGroupsViewState.Empty)

    fun onRefresh() = intent {
        val alarmGroups = remindToRecordAlarmDataStore.fetchAlarmGroups()
        reduce {
            state.copy(
                alarmGroups = alarmGroups,
                showAlarmGroupEmpty = alarmGroups.isEmpty()
            )
        }
    }

    fun onShowRemindTypeLay() = intent {
        reduce { state.copy(showRemindTypeChooseLay = true) }
    }

    fun onDismissRemindTypeLay() = intent {
        reduce { state.copy(showRemindTypeChooseLay = false) }
    }

//    sealed interface ExecuteInterAd {
//        object Navigate : ExecuteInterAd
//        object OnBack : ExecuteInterAd
//    }
//
//    private val executeInterAdFlow = MutableStateFlow<ExecuteInterAd?>(null)
//
//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    private var showInterAdAfterDestination: Direction? = null
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.Navigate }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun onBack(
//        activity: Activity,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.OnBack }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = null
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//                    showInterAdContainer = null
//
//
//                    when (executeInterAdFlow.first()) {
//                        ExecuteInterAd.Navigate -> {
//                            showInterAdAfterDestination?.let { destination ->
//                                showInterAdAfterDestination = null
//                                postSideEffect(AlarmGroupsSideEffect.NavTo(destination))
//                            }
//                        }
//
//                        ExecuteInterAd.OnBack -> {
//                            postSideEffect(AlarmGroupsSideEffect.NavUp)
//                        }
//
//                        null -> {}
//                    }
//
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//                    showInterAdContainer = null
//
//                    when (executeInterAdFlow.first()) {
//                        ExecuteInterAd.Navigate -> {
//                            showInterAdAfterDestination?.let { destination ->
//                                showInterAdAfterDestination = null
//                                postSideEffect(AlarmGroupsSideEffect.NavTo(destination))
//                            }
//                        }
//
//                        ExecuteInterAd.OnBack -> {
//                            postSideEffect(AlarmGroupsSideEffect.NavUp)
//                        }
//
//                        null -> {}
//                    }
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
}
