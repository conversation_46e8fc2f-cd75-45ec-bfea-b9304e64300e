package com.nbpt.app.ui.common.camera

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.FileProvider
import java.io.File

@Composable
fun TakePictureContainer(
    onStarted: () -> Unit = {},
    onComplete: (Uri) -> Unit = {},
    onCanceled: () -> Unit = {},
    onFailed: (String) -> Unit = {},
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
    content: @Composable (start: () -> Unit) -> Unit
) {
    val context = LocalContext.current
    
    val photoUri = remember {
        createTempImageUri(context)
    }
    
    val takePictureLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        if (success) {
            onComplete(photoUri)
        } else {
            onCanceled()
        }
    }
    
    val startCamera = {
        try {
            onStarted()
            takePictureLauncher.launch(photoUri)
        } catch (e: Exception) {
            onFailed(e.message ?: "Failed to start camera")
        }
    }

    Box(modifier) {
        content(startCamera)
    }
}

private fun createTempImageUri(context: Context): Uri {
    val tempFile = File.createTempFile(
        "photo_${System.currentTimeMillis()}",
        ".jpg",
        context.cacheDir
    )
    return FileProvider.getUriForFile(
        context,
        "${context.packageName}.provider",
        tempFile
    )
}