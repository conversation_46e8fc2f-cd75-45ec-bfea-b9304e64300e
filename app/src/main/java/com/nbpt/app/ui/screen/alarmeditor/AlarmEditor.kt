package com.nbpt.app.ui.screen.alarmeditor

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.DeleteForever
import androidx.compose.material.icons.rounded.Vibration
import androidx.compose.material.icons.rounded.VolumeUp
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.ui.theme.alarm.AlarmTheme
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import com.nbpt.app.androidcomponent.alarm.remindtorecord.AlarmGroup
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmType
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.rating.RatingManager
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.datetime.dayOfWeeks
import com.nbpt.app.ui.common.AlarmTimePicker
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.common.CircleCheckBox
import com.nbpt.app.ui.common.DiscardThisXTipsDialog
import com.nbpt.app.ui.common.LinearProgressLoadingDialog

import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.datetime.DayOfWeek
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.time.format.TextStyle
import java.util.Locale

enum class AlarmEditorMode {
    Add,
    Edit,
}

data class AlarmEditorNavArgs(
    val editorMode: AlarmEditorMode = AlarmEditorMode.Add,
    val alarmGroup: AlarmGroup = AlarmGroup.Default,
)

@Composable
@Destination(
    navArgsDelegate = AlarmEditorNavArgs::class
)
fun AlarmEditor(
    navigator: DestinationsNavigator,
) {
    val context = LocalContext.current
    val viewModel: AlarmEditorViewModel = koinViewModel()
    val viewState: AlarmEditorViewState by viewModel.collectAsState()
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()
    val ratingManager: RatingManager = koinInject()

    viewModel.collectSideEffect {
        when (it) {
            is AlarmEditorSideEffect.NavUp -> {
                if (it.showNotificationPermissionRequester) {
                    navigator.navigateUp()
                    notificationPermissionRequester.showCustomRequester(
                        context.findActivity(),
                        fromAlarmsModify = true
                    )
                } else {
                    navigator.navigateUp()
                }
            }

            AlarmEditorSideEffect.AddAlarmAndNavUp -> {
                navigator.navigateUp()
                val showNotificationPermissionRequester =
                    notificationPermissionRequester.showCustomRequester(
                        context.findActivity(),
                        fromAlarmsModify = true
                    )

                if (!showNotificationPermissionRequester) {
                    ratingManager.tryToOpenRatingSheet(context.findActivity())
                }
            }
        }
    }

//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//    }

    AlarmTheme {
//        if (viewState.showInterstitialAdLoadingDialog) {
//            AdLoadingDialog()
//        }

        AlarmEditor(
            navigator = navigator,
            viewModel = viewModel,
            viewState = viewState,
        )
    }

//    BackHandler(onBack = viewModel::onBack)
}

@Composable
private fun AlarmEditor(
    navigator: DestinationsNavigator,
    viewModel: AlarmEditorViewModel,
    viewState: AlarmEditorViewState,
) {
    val context = LocalContext.current

    if (viewState.showCrudProcessingDialog) {
        LinearProgressLoadingDialog(
            navUp = { },
            title = stringResource(R.string.text_processing),
            userDismissEnable = false
        )
    }

    if (viewState.showDeleteAlarmDialog) {
        AlertDialog(
            onDismissRequest = viewModel::onDismissDeleteAlarmDialog,
            confirmButton = {
                TextButton(onClick = {
                    viewModel.onDismissDeleteAlarmDialog()
                    viewModel.onDelete()
                    logEventRecord("click_alarm_delete")
                }) {
                    Text(text = stringResource(R.string.text_delete))
                }
            },
            dismissButton = {
                TextButton(onClick = viewModel::onDismissDeleteAlarmDialog) {
                    Text(text = stringResource(R.string.text_cancel))
                }
            },
            title = {
                Text(
                    text = stringResource(R.string.text_delete_alarm),
                    style = MaterialTheme.typography.titleLarge,
                )
            },
            text = {
                Text(text = stringResource(R.string.text_content_delete_alarm_tips))
            }
        )
    }

    if (viewState.showExitEditorTipsDialog && viewModel.navArgs.editorMode == AlarmEditorMode.Add) {
        DiscardThisXTipsDialog(
            onDismiss = viewModel::onDismissExitEditorTipsDialog,
            onDiscard = {
                viewModel.onDismissExitEditorTipsDialog()
                navigator.navigateUp()
            },
            title = stringResource(R.string.text_content_discard_alarm_tips),
        )
    }

    Scaffold(
        topBar = {
            AlarmEditorAppBar(
                editorMode = viewModel.editorMode,
                alarmType = viewState.remindType,
//                    navUp = viewModel::onBack,
                navUp = navigator::navigateUp,
                onDeleteIconClick = viewModel::onShowDeleteAlarmDialog
            )
        },
        bottomBar = {
            AlarmEditorBottomBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
            )
        },
        containerColor = AppTheme.Color.AlarmBackground
    ) {

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = Layout.bodyMargin * 1.3f)

            AlarmEditorDayOfWeekMultiPicker(
                selections = viewState.dayOfWeekSelections,
                onSelect = viewModel::onSelect,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Layout.bodyMargin)
                    .defShadow()
            )

//                BlankSpacer(height = Layout.bodyMargin)
//
//                NativeAd(place = NativeAdPlace.AlarmEditor)

            BlankSpacer(height = Layout.bodyMargin)

            Surface(
                shape = RoundedCornerShape12Dp,
                color = AppTheme.Color.White,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = Layout.bodyMargin)
                    .defShadow()
            ) {
                AlarmTimePicker(
                    time = viewState.localTime,
                    onTimeChange = viewModel::onTimeChange,
                    modifier = Modifier.bodyWidth()
                )
            }

            BlankSpacer(height = Layout.bodyMargin)

            Row(
                modifier = Modifier.fillMaxWidth().padding(horizontal = Layout.bodyMargin),
                horizontalArrangement = Arrangement.Center
            ) {

                Surface(
                    onClick = {
                        viewModel.onSoundSettingChange(!viewState.soundEnable)
                    },
                    color = AppTheme.Color.White,
                    modifier = Modifier
                        .weight(1f)
                        .defShadow(),
                    shape = RoundedCornerShape12Dp
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 14.dp, end = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Image(
                            imageVector = Icons.Rounded.VolumeUp,
                            contentDescription = null,
                            modifier = Modifier.size(26.dp)
                        )
                        BlankSpacer(width = Layout.gutter)
                        Text(
                            text = stringResource(R.string.text_sound),
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        BlankSpacer(width = Layout.gutter)
                        Switch(
                            checked = viewState.soundEnable,
                            onCheckedChange = viewModel::onSoundSettingChange,
                            colors = SwitchDefaults.colors(
                                checkedTrackColor = AppTheme.Color.AlarmPrimaryLight,
                            ),
                            modifier = Modifier.scale(0.72f)
                        )
                    }
                }

                BlankSpacer(width = 10.dp)

                Surface(
                    onClick = {
                        viewModel.onVibrateSettingChange(!viewState.vibrateEnable)
                    },
                    color = AppTheme.Color.White,
                    modifier = Modifier
                        .weight(1f)
                        .defShadow(),
                    shape = RoundedCornerShape12Dp
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 14.dp, end = 4.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Image(
                            imageVector = Icons.Rounded.Vibration,
                            contentDescription = null,
                            modifier = Modifier.size(26.dp)
                        )
                        BlankSpacer(width = Layout.gutter)
                        Text(
                            text = stringResource(R.string.text_vibrate),
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.weight(1f)
                        )
                        BlankSpacer(width = Layout.gutter)
                        Switch(
                            checked = viewState.vibrateEnable,
                            onCheckedChange = viewModel::onVibrateSettingChange,
                            colors = SwitchDefaults.colors(
                                checkedTrackColor = AppTheme.Color.AlarmPrimaryLight,
                            ),
                            modifier = Modifier.scale(0.72f)
                        )
                    }
                }
            }
            BlankSpacer(20.dp)

            SmartRectAd(
                pageType = SmartAdPageType.FEAT_CONTENT,
                bannerAdPlace = BannerAdPlaceholder.ALARM_EDITOR,
                nativeAdPlace = NativeAdPlaceholder.AlarmEditor,
            )

            BlankSpacer(20.dp)

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Layout.bodyMargin * 2f)
            ) {
                CardButton(
                    text = stringResource(R.string.text_cancel),
//                        onClick = viewModel::onBack,
                    onClick = navigator::navigateUp,
                    modifier = Modifier.weight(1f),
                    containerBrush = Brush.verticalGradient(
                        listOf(
                            AppTheme.Color.textSecondary.copy(alpha = .4f),
                            AppTheme.Color.textSecondary.copy(alpha = .6f)
                        )
                    )
                )
                BlankSpacer(width = 20.dp)

                val activity = LocalContext.current.findActivity()
                CardButton(
                    text = stringResource(R.string.text_save),
                    onClick = {
                        viewModel.onSave(activity)
                    },
                    modifier = Modifier.weight(1f),
                    containerBrush = AppTheme.Color.AlarmBrush
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AlarmEditorAppBar(
    editorMode: AlarmEditorMode,
    alarmType: RemindToRecordAlarmType?,
    navUp: () -> Unit,
    onDeleteIconClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = { Text(text = if (alarmType == null) "" else stringResource(id = alarmType.titleStringId)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                if (editorMode == AlarmEditorMode.Edit) {
                    IconButton(onClick = onDeleteIconClick) {
                        Icon(
                            imageVector = Icons.Rounded.DeleteForever,
                            contentDescription = "delete",
                        )
                    }
                }
            }
        )
        SmartRectAd(
            pageType = SmartAdPageType.FEAT_TOP_BAR,
            bannerAdPlace = BannerAdPlaceholder.ALARM_EDITOR,
            nativeAdPlace = NativeAdPlaceholder.AlarmEditor,
        )
    }

}

@Composable
private fun AlarmEditorBottomBar(
    modifier: Modifier = Modifier,
) {
    SmartRectAd(
        pageType = SmartAdPageType.FEAT_BOTTOM_BAR,
        bannerAdPlace = BannerAdPlaceholder.ALARM_EDITOR,
        nativeAdPlace = NativeAdPlaceholder.AlarmEditor,
        modifier = modifier
    )
}

private val dayOfWeeks = dayOfWeeks()

@Composable
fun AlarmEditorDayOfWeekMultiPicker(
    selections: Set<DayOfWeek>,
    onSelect: (DayOfWeek, Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(modifier = modifier, shape = RoundedCornerShape12Dp) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            dayOfWeeks.forEach {
                DayOfWeekSelector(
                    dayOfWeek = it,
                    isSelect = it in selections,
                    onSelect = onSelect,
                    modifier = Modifier
                        .weight(1f)
                        .padding(top = 10.dp)
                )
            }
        }
    }
}

@Composable
private fun DayOfWeekSelector(
    dayOfWeek: DayOfWeek,
    isSelect: Boolean,
    onSelect: (DayOfWeek, Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.noRippleClickable {
            onSelect(dayOfWeek, !isSelect)
        },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.getDefault()),
        )

        CircleCheckBox(checked = isSelect, onCheck = {
            onSelect(dayOfWeek, it)
        })
    }
}
