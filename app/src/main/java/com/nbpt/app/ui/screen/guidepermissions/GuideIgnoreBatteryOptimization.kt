package com.nbpt.app.ui.screen.guidepermissions

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.IgnoringBatteryOptimizationRequester
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.compose.koinInject

@Destination
@Composable
fun GuideIgnoreBatteryOptimization(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()

    val onSkip = {
        navigator.popBackStack()
        navigator.navigate(HomeDestination())
    }

    LaunchedEffect(Unit) {
        if (ignoringBatteryOptimizationRequester.hasIgnoring(context.findActivity()) == true) {
            onSkip()
            return@LaunchedEffect
        }

        IgnoringBatteryOptimizationRequester.ignoringEventFlow.onEach {
            if (it == true) {
                onSkip()
            }
        }.launchIn(this)
    }

    GuidePermissionContent(
        icon = painterResource(id = R.drawable.ic_permission_battery),
        title = stringResource(R.string.text_ensure_the_app_runs_smoothly),
        description = stringResource(R.string.exclude_app_from_battery_optimization_for_background_operation),
        onNext = {
            if (ignoringBatteryOptimizationRequester.hasIgnoring(context.findActivity()) == true) {
                onSkip()
            } else {
                ignoringBatteryOptimizationRequester.tryToOpenBatteryOptimizationSystemSettings(context.findActivity())
            }
        },
        onSkip = onSkip
    )
}