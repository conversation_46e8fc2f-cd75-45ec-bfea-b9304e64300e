package com.nbpt.app.ui.screen.article

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.ui.screen.navArgs
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class ArticleViewModel(
    savedStateHandle: SavedStateHandle,
) : ViewModel(), ContainerHost<ArticleViewState, ArticleSideEffect>, KoinComponent {

    override val container: Container<ArticleViewState, ArticleSideEffect> =
        container(ArticleViewState.Empty)

    init {
        val navArgs = savedStateHandle.navArgs<ArticleNavArgs>()
        intent {
            reduce { state.copy(article = navArgs.article) }
        }
    }
}
