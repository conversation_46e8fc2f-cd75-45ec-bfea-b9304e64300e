package com.nbpt.app.ui.screen.anr

import android.os.Parcelable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.LocationOn
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.data.adt.HrStatus
import com.nbpt.app.data.healtharticles.HealthArticle
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BpRecordStat
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.screen.destinations.HeartRateHistoryDestination
import com.nbpt.app.ui.screen.destinations.HistoryDestination
import com.nbpt.app.ui.screen.info.ArticleItem
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bp.BpTheme
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

sealed interface AnrMode : Parcelable {
    @Parcelize
    data class BP(val bpStatus: BpStatus) : AnrMode

    @Parcelize
    data class HR(val hrStatus: HrStatus) : AnrMode
}

data class AnrNavArgs(
    val anrMode: AnrMode? = null
)

@Destination(
    navArgsDelegate = AnrNavArgs::class
)
@Composable
fun Anr(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current

    val viewModel = koinViewModel<AnrViewModel>()

    val viewState by viewModel.collectAsState()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    BpTheme {
        Anr(
            navigator = navigator,
            viewModel = viewModel,
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState,
        )
    }

//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//        viewModel.registerInterRewardedAdEventFlow(this)
//    }
//
//    BackHandler {
//        viewModel.onBack(
//            activity = context.findActivity(),
//        )
//    }

    viewModel.collectSideEffect {
        when (it) {
            is AnrSideEffect.NavTo -> navigator.navigate(it.destination)
            is AnrSideEffect.NavUp -> navigator.navigateUp()
        }
    }
}

@Composable
private fun Anr(
    navigator: DestinationsNavigator,
    viewModel: AnrViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: AnrViewState,
) {
    val context = LocalContext.current

//    if (viewState.showAdLoadingDialog) {
//        AdLoadingDialog()
//    }

    Scaffold(
        topBar = {
            AnrAppBar(navUp = {
//                    viewModel.onBack(
//                        activity = context.findActivity(),
//                    )
                navigator.navigateUp()
            }, onHistoryClick = {
                val (destination, adPlaceName) = when (viewState.anrMode) {
                    is AnrMode.BP -> HistoryDestination to "enter_bp_list"
                    is AnrMode.HR -> HeartRateHistoryDestination to "enter_hr_list"
                    null -> return@AnrAppBar
                }
                navigator.navigate(destination)

//                    viewModel.onTryToShowInterAdAndNavTo(
//                        activity = context.findActivity(),
//                        destination = destination,
//                        adPlaceName = adPlaceName
//                    )
            })
        },
        bottomBar = {
//            if (viewState.anrMode != null) {
//                BannerAd(
//                    adPlace = when (viewState.anrMode) {
//                        is AnrMode.BP -> BannerAdPlace.AnrBp
//                        is AnrMode.HR -> BannerAdPlace.AnrHr
//                    },
//                    modifier = Modifier.navigationBarsPadding()
//                )
//            }
        },
        containerColor = AppTheme.Color.BpBackground
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)

            when (viewState.anrMode) {
                is AnrMode.BP -> {
                    BpAnrInfo(
                        bpStatus = viewState.anrMode.bpStatus,
//                        isUnlockAnalysis = viewState.isUnlockAnalysis,
                        isUnlockAnalysis = true,
                        onUnlockAnalysisInfo = {
//                                viewModel.onUnlockAnalysisInfo(
//                                    activity = context.findActivity(),
//                                    adPlaceName = "bp_analyze"
//                                )
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    )
                }

                is AnrMode.HR -> {
                    HrAnrInfo(
                        hrStatus = viewState.anrMode.hrStatus,
//                        isUnlockAnalysis = viewState.isUnlockAnalysis,
                        isUnlockAnalysis = true,
                        onUnlockAnalysisInfo = {
//                                viewModel.onUnlockAnalysisInfo(
//                                    activity = context.findActivity(),
//                                    adPlaceName = "hr_analyze"
//                                )
                        },
                        modifier = Modifier
                            .defShadow()
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                    )
                }

                null -> {}
            }

            when (viewState.anrMode) {
                is AnrMode.BP -> {
                    BlankSpacer(height = 16.dp)

//                        NativeAd(
//                            place = NativeAdPlace.AnrBp,
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .padding(horizontal = 16.dp)
//                        )
//
//                        BlankSpacer(height = 16.dp)

                    BpRecordStat(
//                        isUnlock = viewState.isUnlockBpRecordStat,
                        isUnlock = true,
                        onWatchAd = {
//                                viewModel.onUnlockBpRecordStat(
//                                    activity = context.findActivity(),
//                                    adPlaceName = "bp_access_trend_data"
//                                )
                        },
                        records = viewState.bpRecords.reversed(),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                            .height(180.dp)
                    )
                }

                is AnrMode.HR -> {
//                        BlankSpacer(height = 16.dp)
//
//                        NativeAd(
//                            place = NativeAdPlace.AnrHr,
//                            modifier = Modifier
//                                .fillMaxWidth()
//                                .padding(horizontal = 16.dp)
//                        )
                }

                null -> {}
            }

            BlankSpacer(height = 16.dp)

            val interstitialAdManager: MaxInterstitialAdManager = koinInject()
            RecommendedReading(
                title = stringResource(R.string.text_recommended_reading),
                onArticleClick = { article ->
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_article",onAdLoadingAfter = {
                            navigator.navigate(ArticleDestination(article))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(ArticleDestination(article)) },
                            adPlaceName = "enter_article"
                        )
                    }
                },
                articles = viewState.recommendedArticles,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@Composable
fun RecommendedReading(
    title: String,
    onArticleClick: (HealthArticle) -> Unit,
    articles: List<HealthArticle>,
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Text(text = title)
        BlankSpacer(height = 16.dp)
        articles.forEach {
            ArticleItem(
                onClick = onArticleClick,
                article = it,
                modifier = Modifier.fillMaxWidth()
            )
            BlankSpacer(height = 16.dp)
        }
    }
}

@Composable
private fun HrAnrInfo(
    hrStatus: HrStatus,
    isUnlockAnalysis: Boolean,
    onUnlockAnalysisInfo: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = AppTheme.Color.White,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(text = stringResource(R.string.text_your_heart_rate_is))

            BlankSpacer(height = 10.dp)

            HrStatusInfoGraph(
                currentHrStatus = hrStatus,
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 22.dp)

            if (isUnlockAnalysis) {
                Text(
                    text = stringResource(id = hrStatus.analysisStringId),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Row(
                    modifier = Modifier.bodyWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_ad_trumpet),
                        contentDescription = null,
                        modifier = Modifier.size(26.dp),
                        tint = AppTheme.Color.Red
                    )
                    BlankSpacer(width = 4.dp)

                    Text(
                        text = stringResource(R.string.text_view_ad_to_analyze_your_heart_rate),
                        style = MaterialTheme.typography.titleSmall
                    )
                }

                BlankSpacer(height = 16.dp)

                CardButton(
                    text = stringResource(R.string.text_watch_now),
                    onClick = onUnlockAnalysisInfo,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 40.dp)
                )
            }

            BlankSpacer(height = 8.dp)
        }

    }
}

@Composable
private fun HrStatusInfoGraph(
    currentHrStatus: HrStatus,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        ConstraintLayout {
            val cbWidth = ((LocalConfiguration.current.screenWidthDp - 32 * 2) / 3f).dp

            val (
                slowCb,
                normalCb,
                fastCb,
                cursor,
            ) = createRefs()

            StatusCb(
                statusColor = HrStatus.Slow.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(slowCb) {
                    start.linkTo(parent.start)
                    end.linkTo(normalCb.start)
                    top.linkTo(cursor.bottom)
                },
                position = StatusCbPosition.Start,
            )
            StatusCb(
                statusColor = HrStatus.Normal.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(normalCb) {
                    start.linkTo(slowCb.end)
                    end.linkTo(fastCb.start)
                    top.linkTo(cursor.bottom)
                }
            )
            StatusCb(
                statusColor = HrStatus.Fast.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(fastCb) {
                    start.linkTo(normalCb.end)
                    end.linkTo(parent.end)
                    top.linkTo(cursor.bottom)
                },
                position = StatusCbPosition.End,
            )

            val attachTo = when (currentHrStatus) {
                HrStatus.Slow -> slowCb
                HrStatus.Normal -> normalCb
                HrStatus.Fast -> fastCb
            }

            HrStatusCursor(
                hrStatus = currentHrStatus,
                modifier = Modifier
                    .constrainAs(cursor) {
                        top.linkTo(parent.top)
                        bottom.linkTo(attachTo.top)
                        start.linkTo(attachTo.start)
                        end.linkTo(attachTo.end)
                    }
                    .padding(bottom = 6.dp)
            )
        }
    }
}

@Composable
private fun BpAnrInfo(
    bpStatus: BpStatus,
    isUnlockAnalysis: Boolean,
    onUnlockAnalysisInfo: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = AppTheme.Color.White,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
        ) {
            Text(
                text = stringResource(R.string.text_your_blood_pressure_is),
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 10.dp)

            BpStatusInfoGraph(
                currentBpStatus = bpStatus,
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 22.dp)

            if (isUnlockAnalysis) {
                Text(
                    text = stringResource(id = bpStatus.analysisStringId),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Row(
                    modifier = Modifier.bodyWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_ad_trumpet),
                        contentDescription = null,
                        modifier = Modifier.size(26.dp),
                        tint = AppTheme.Color.Red
                    )
                    BlankSpacer(width = 4.dp)

                    Text(
                        text = stringResource(R.string.text_view_ad_to_analyze_your_blood_pressure),
                        style = MaterialTheme.typography.titleSmall
                    )
                }

                BlankSpacer(height = 16.dp)

                CardButton(
                    text = stringResource(R.string.text_watch_now),
                    onClick = onUnlockAnalysisInfo,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 40.dp)
                )
            }

            BlankSpacer(height = 8.dp)
        }

    }
}

@Composable
private fun BpStatusInfoGraph(
    currentBpStatus: BpStatus,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        ConstraintLayout {
            val cbWidth = ((LocalConfiguration.current.screenWidthDp - 32 * 2) / 6f).dp

            val (
                hypotensionCb,
                normalCb,
                elevatedCb,
                hypertensionS1Cb,
                hypertensionS2Cb,
                hypertensiveCb,
                cursor,
            ) = createRefs()

            StatusCb(
                statusColor = BpStatus.Hypotension.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(hypotensionCb) {
                    start.linkTo(parent.start)
                    end.linkTo(normalCb.start)
                    top.linkTo(cursor.bottom)
                },
                position = StatusCbPosition.Start,
            )
            StatusCb(
                statusColor = BpStatus.Normal.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(normalCb) {
                    start.linkTo(hypotensionCb.end)
                    end.linkTo(elevatedCb.start)
                    top.linkTo(cursor.bottom)
                }
            )
            StatusCb(
                statusColor = BpStatus.Elevated.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(elevatedCb) {
                    start.linkTo(normalCb.end)
                    end.linkTo(hypertensionS1Cb.start)
                    top.linkTo(cursor.bottom)
                }
            )
            StatusCb(
                statusColor = BpStatus.HypertensionS1.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(hypertensionS1Cb) {
                    start.linkTo(elevatedCb.end)
                    end.linkTo(hypertensionS2Cb.start)
                    top.linkTo(cursor.bottom)
                }
            )
            StatusCb(
                statusColor = BpStatus.HypertensionS2.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(hypertensionS2Cb) {
                    start.linkTo(hypertensionS1Cb.end)
                    end.linkTo(hypertensiveCb.start)
                    top.linkTo(cursor.bottom)
                }
            )
            StatusCb(
                statusColor = BpStatus.Hypertensive.color,
                width = cbWidth,
                modifier = Modifier.constrainAs(hypertensiveCb) {
                    start.linkTo(hypertensionS2Cb.end)
                    end.linkTo(parent.end)
                    top.linkTo(cursor.bottom)
                },
                position = StatusCbPosition.End,
            )

            val attachTo = when (currentBpStatus) {
                BpStatus.Hypotension -> hypotensionCb
                BpStatus.Normal -> normalCb
                BpStatus.Elevated -> elevatedCb
                BpStatus.HypertensionS1 -> hypertensionS1Cb
                BpStatus.HypertensionS2 -> hypertensionS2Cb
                BpStatus.Hypertensive -> hypertensiveCb
            }

            BpStatusCursor(
                bpStatus = currentBpStatus,
                modifier = Modifier
                    .constrainAs(cursor) {
                        top.linkTo(parent.top)
                        bottom.linkTo(attachTo.top)

                        when (currentBpStatus) {
                            is BpStatus.Hypotension -> {
                                start.linkTo(attachTo.start)
                            }

                            is BpStatus.Hypertensive -> {
                                end.linkTo(attachTo.end)
                            }

                            else -> {
                                start.linkTo(attachTo.start)
                                end.linkTo(attachTo.end)
                            }
                        }
                    }
                    .padding(bottom = 6.dp)
            )
        }
    }
}

sealed interface StatusCbPosition {
    object Start : StatusCbPosition
    object Middle : StatusCbPosition
    object End : StatusCbPosition
}

@Composable
private fun StatusCb(
    statusColor: Color,
    width: Dp,
    modifier: Modifier = Modifier,
    position: StatusCbPosition = StatusCbPosition.Middle,
) {

    val shape = when (position) {
        StatusCbPosition.Start -> RoundedCornerShape(topStartPercent = 50, bottomStartPercent = 50)
        StatusCbPosition.Middle -> RoundedCornerShape(0)
        StatusCbPosition.End -> RoundedCornerShape(topEndPercent = 50, bottomEndPercent = 50)
    }

    Surface(
        modifier = modifier
            .height(14.dp)
            .width(width),
        shape = shape,
        content = {},
        color = statusColor
    )
}

@Composable
private fun BpStatusCursor(
    bpStatus: BpStatus,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = bpStatus.titleStringId),
            style = MaterialTheme.typography.titleSmall
        )
        BlankSpacer(height = 6.dp)
        Icon(
            imageVector = Icons.Rounded.LocationOn,
            contentDescription = null,
            tint = bpStatus.color
        )
    }
}

@Composable
private fun HrStatusCursor(
    hrStatus: HrStatus,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = hrStatus.titleStringId),
            style = MaterialTheme.typography.titleSmall
        )
        BlankSpacer(height = 6.dp)
        Icon(
            imageVector = Icons.Rounded.LocationOn,
            contentDescription = null,
            tint = hrStatus.color
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AnrAppBar(
    navUp: () -> Unit,
    onHistoryClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_result)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
        actions = {
            IconButton(onClick = onHistoryClick) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_history),
                    contentDescription = "history",
                    modifier = Modifier.size(AppBarActionButtonDpSize)
                )
            }
        }
    )
}

@Preview
@Composable
private fun BpAnrInfoGraphPreview() {
    BpStatusInfoGraph(
        currentBpStatus = BpStatus.Normal,
        modifier = Modifier.bodyWidth()
    )
}

@Preview
@Composable
private fun HrAnrInfoGraphPreview() {
    HrStatusInfoGraph(
        currentHrStatus = HrStatus.Slow,
        modifier = Modifier.bodyWidth()
    )
}
