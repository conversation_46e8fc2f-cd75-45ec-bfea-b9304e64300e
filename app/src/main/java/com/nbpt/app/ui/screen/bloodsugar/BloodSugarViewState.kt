package com.nbpt.app.ui.screen.bloodsugar

import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.db.model.BsRecordEntity
import com.nbpt.app.data.healtharticles.HealthArticle

data class BloodSugarViewState(
    val bsRecordState: BsRecordState = BsRecordState.Default,
    val bsUnit: BsUnit = BsUnit.SI,
    val avgBsMmolL: Float? = null,
    val avgBsMgDl: Float? = null,
    val latestBsMmolL: Float? = null,
    val latestBsMgDl: Float? = null,
    val bsRecords: List<BsRecordEntity> = emptyList(),
    val bsArticles: List<HealthArticle>? = null,
    val bsStatMap: Map<BsStatus, Int> = emptyMap()
)
