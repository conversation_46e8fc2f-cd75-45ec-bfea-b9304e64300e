package com.nbpt.app.ui.screen.bp

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Alarm
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BpRecordItem
import com.nbpt.app.ui.common.BpRecordStat
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.anr.RecommendedReading
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.HistoryDestination
import com.nbpt.app.ui.screen.destinations.BpRecordEditorDestination
import com.nbpt.app.ui.screen.bprecordeditor.BpRecordEditorMode
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bp.BpTheme
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun Bp(
    navigator: DestinationsNavigator,
) {
    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    BpTheme {
        TrackerScreen(
            navigator = navigator,
            viewModel = koinViewModel(),
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
        )
    }
}

@Composable
fun TrackerScreen(
    navigator: DestinationsNavigator,
    viewModel: TrackerViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
) {
    val activity = LocalContext.current.findActivity()
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
//    val scope = rememberCoroutineScope()

//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//        viewModel.registerInterRewardedAdEventFlow(this)
//    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }

    val viewState by viewModel.collectAsState()

    viewModel.collectSideEffect {
        when (it) {
            is TrackerSideEffect.NavTo -> {
                navigator.navigate(it.destination)
            }
        }
    }

//    if (viewState.showAdLoadingDialog) {
//        AdLoadingDialog()
//    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd("bp_back_home", onAdLoadingAfter = {
                navigator.navigateUp()
            })
        } else {
            admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                activity = activity,
                adPlaceName = "bp_back_home"
            )
        }
        Unit
    }

    BackHandler(onBack = onBack)

    Scaffold(
        topBar = {
            TrackerAppBar(
                navUp = onBack,
                openAlarmGroup = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_alarm", onAdLoadingAfter = {
                            navigator.navigate(AlarmGroupsDestination(showInterAdWhenBack = true))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = {
                                navigate(AlarmGroupsDestination(showInterAdWhenBack = true))
                            },
                            adPlaceName = "enter_alarm"
                        )
                    }

                    logEventRecord("click_alarm")
                },
                openHistory = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_bp_list", onAdLoadingAfter = {
                            navigator.navigate(HistoryDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = {
                                navigate(HistoryDestination)
                            },
                            adPlaceName = "enter_bp_list"
                        )
                    }

                    logEventRecord("click_record_history")
                },
                modifier = Modifier.fillMaxWidth()
            )
        },
        bottomBar = {
            SmartRectAd(
                pageType = SmartAdPageType.HOME_BOTTOM_BAR,
                bannerAdPlace = BannerAdPlace.BP,
                nativeAdPlace = NativeAdPlace.Bp,
                modifier = Modifier.navigationBarsPadding(),
            )
        },
        floatingActionButton = {
            val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp

            TrackerFab(
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd(from = "add_record", onAdLoadingAfter = {
                            navigator.navigate(BpRecordEditorDestination(editorMode = BpRecordEditorMode.Add))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = {
                                navigate(BpRecordEditorDestination(editorMode = BpRecordEditorMode.Add))
                            },
                            adPlaceName = "add_record"
                        )
                    }

                    logEventRecord("click_record_add")
                },
                modifier = Modifier.padding(bottom = (screenHeightDp / 2) - 72.dp)
            )
        },
        containerColor = AppTheme.Color.BpBackground
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {

            TrackerStatistics(
                selectMode = viewState.statDataMode, statData = when (viewState.statDataMode) {
                    TrackerRoughStatModeAdt.Avg -> viewState.avgStatData
                    TrackerRoughStatModeAdt.Latest -> viewState.latestStatData
                    TrackerRoughStatModeAdt.Max -> viewState.maxStatData
                    TrackerRoughStatModeAdt.Min -> viewState.minStatData
                },
                onModeSelect = viewModel::onStatModeChange,
                modifier = Modifier.bodyWidth().padding(horizontal = 16.dp)
            ) {
                BlankSpacer(height = 20.dp)
                BpRecordStat(
//                isUnlock = viewState.isUnlockBpRecordStat,
                    isUnlock = true,
                    onWatchAd = {
//                    viewModel.onUnlockBpRecordStat(
//                        activity = activity,
//                        adPlaceName = "bp_access_trend_data"
//                    )
                    },
                    records = viewState.bpRecords.reversed(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp)
                        .height(180.dp)
                )
            }

//            NativeAd(place = NativeAdPlace.Tracker)
//
//            BlankSpacer(height = bodyMargin)


            BlankSpacer(height = 16.dp)

            repeat(3) { index ->
                val bpRecord = viewState.bpRecords.getOrNull(index)
                if (bpRecord != null) {
                    BpRecordItem(bpRecord = bpRecord, modifier = Modifier.padding(horizontal = 16.dp))
                    BlankSpacer(height = 16.dp)
                }
            }

            CardButton(
                text = stringResource(R.string.text_history),
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_bp_list", onAdLoadingAfter = {
                            navigator.navigate(HistoryDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = { navigate(HistoryDestination) },
                            adPlaceName = "enter_bp_list"
                        )
                    }

                    logEventRecord("click_record_history")
                },
                containerBrush = AppTheme.Color.BpBrush,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(horizontal = 56.dp)
            )

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.HOME_CONTENT,
                bannerAdPlace = BannerAdPlace.BP,
                nativeAdPlace = NativeAdPlace.Bp,
            )

            BlankSpacer(height = 16.dp)

            RecommendedReading(
                title = stringResource(R.string.text_recommended_reading),
                onArticleClick = { article ->
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_article", onAdLoadingAfter = {
                            navigator.navigate(ArticleDestination(article))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = { navigate(ArticleDestination(article)) },
                            adPlaceName = "enter_article"
                        )
                    }
                },
                articles = viewState.bpArticles,
                modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp)
            )
        }
    }

    /*    if (viewState.showAddRecordGuideLay) {
            AddRecordGuideLay(
                onClickAddRecord = {
                    navigator.navigate(
                        BpRecordEditorDestination(
                            editorMode = BpRecordEditorMode.Add,
                        )
                    )

    //                viewModel.onTryToShowInterAdAndNavTo(
    //                    activity = activity,
    //                    destination = BpRecordEditorDestination(
    //                        editorMode = BpRecordEditorMode.Add,
    //                    ),
    //                    adPlaceName = "add_record",
    //                )
    //                logEventRecord("click_record_add")
                },
                modifier = Modifier.fillMaxSize()
            )
        }*/
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TrackerAppBar(
    navUp: () -> Unit,
    openAlarmGroup: () -> Unit,
    openHistory: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = {
                Text(text = stringResource(id = R.string.bp_title))
            },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                IconButton(onClick = openAlarmGroup) {
                    Icon(
                        imageVector = Icons.Rounded.Alarm,
                        contentDescription = "alarm",
                    )
                }

                IconButton(onClick = openHistory) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_history),
                        contentDescription = "history",
                        modifier = Modifier.size(AppBarActionButtonDpSize)
                    )
                }
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.HOME_TOP_BAR,
            bannerAdPlace = BannerAdPlace.BP,
            nativeAdPlace = NativeAdPlace.Bp,
        )
    }
}

@Composable
private fun TrackerFab(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Dp = 72.dp,
) {
    Box(modifier = modifier) {
        Image(
            painter = painterResource(id = R.drawable.img_fab_bp_add),
            contentDescription = "add record",
            modifier = Modifier
                .size(size)
                .noRippleClickable(onClick = onClick),
        )
    }
}

@Composable
private fun AddRecordGuideLay(
    onClickAddRecord: () -> Unit,
    modifier: Modifier = Modifier,
    layColor: Color = Color(0xB3080931),
) {
    val scope = rememberCoroutineScope()
    val userBehaviorDataStore: UserBehaviorDataStore = koinInject()

    Box(
        modifier = modifier
            .background(color = layColor)
            .noRippleClickable { }
    ) {
        Text(
            text = stringResource(R.string.text_tips_unlock_bp_tracker_stats),
            style = MaterialTheme.typography.headlineMedium.copy(color = AppTheme.Color.White),
            modifier = Modifier
                .align(Alignment.Center)
                .padding(horizontal = 56.dp),
            textAlign = TextAlign.Center
        )

        CardButton(
            text = stringResource(R.string.text_add),
            onClick = {
                scope.launch {
                    userBehaviorDataStore.setHasShowAddRecordGuideLay(true)
                    onClickAddRecord()
                }
            },
            modifier = Modifier
                .requiredWidthIn(min = 320.dp, max = 320.dp)
                .align(Alignment.BottomCenter)
                .padding(bottom = 120.dp)
                .padding(horizontal = 56.dp)
        )
    }
}
