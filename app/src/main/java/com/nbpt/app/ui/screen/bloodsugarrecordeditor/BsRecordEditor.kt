package com.nbpt.app.ui.screen.bloodsugarrecordeditor

import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.DeleteForever
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material.icons.rounded.Info
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.alarmguide.AddRemindAlarmGuideManager
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BsTypeDialog
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.common.DateTimePicker
import com.nbpt.app.ui.common.DiscardThisXTipsDialog
import com.nbpt.app.ui.common.textfield.UnderlineTextField
import com.nbpt.app.ui.screen.bloodsugar.BsRecordStateSwitch
import com.nbpt.app.ui.screen.bloodsugarnotemanager.bsRecordNotesManagerOnBackEvent
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.screen.destinations.BsRecordNotesDestination
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bs.BsTheme
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.result.NavResult
import com.ramcosta.composedestinations.result.ResultRecipient
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.math.RoundingMode

sealed interface BsRecordEditorRecipientEvent : Parcelable {

    @Parcelize
    data class NotesSelectionFinish(
        val notesSelection: ArrayList<String>
    ) : BsRecordEditorRecipientEvent

    @Parcelize
    data object NotesManagerNavUp : BsRecordEditorRecipientEvent
}

enum class BsRecordEditorMode(@StringRes val titleStringId: Int) {
    Add(R.string.text_new_record),
    Edit(R.string.text_edit)
}

data class BsRecordEditorNavArgs(
    val editorMode: BsRecordEditorMode? = null,
    val recordUUID: String? = null,
    val instant: Instant = nowInstant(),
    val mmolL: Float = 5.0f,
    val state: BsRecordState = BsRecordState.Default,
    val notesSelection: ArrayList<String>? = null,
)

@Destination(
    navArgsDelegate = BsRecordEditorNavArgs::class
)
@Composable
fun BsRecordEditor(
    navigator: DestinationsNavigator,
    resultRecipient: ResultRecipient<BsRecordNotesDestination, BsRecordEditorRecipientEvent>,
) {
    val context = LocalContext.current
    val viewModel: BsRecordEditorViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    val ratingManager: RatingManager = koinInject()
    val alarmGuideManager: AddRemindAlarmGuideManager = koinInject()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    viewModel.collectSideEffect {
        when (it) {
            BsRecordEditorSideEffect.NavUp -> navigator.navigateUp()
            BsRecordEditorSideEffect.SaveAndNavUp -> {
                if (viewModel.navArgs.editorMode == BsRecordEditorMode.Add) {
                    navigator.navigateUp()

                    GlobalScope.launch {
                        val hasOpenRatingSheet =
                            ratingManager.tryToOpenRatingSheet(context.findActivity())

                        debugLog { "hasOpenRatingSheet: $hasOpenRatingSheet" }
                        if (!hasOpenRatingSheet) {
                            alarmGuideManager.tryToOpenGuideDialog()
                        }
                    }
                } else {
                    navigator.navigateUp()
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        //        viewModel.registerInterAdEventFlow(this)
        //        viewModel.registerInterRewardedAdEventFlow(this)

        bsRecordNotesManagerOnBackEvent.onEach {
            val deletedSelection = viewModel.deletedSelection(it)

            viewModel.onNotesDelete(deletedSelection)

            navigator.navigate(
                BsRecordNotesDestination(
                    notesSelection = ArrayList(
                        viewState.notesSelection - deletedSelection
                    )
                )
            )
        }.launchIn(this)
    }

    resultRecipient.onNavResult { result ->
        when (result) {
            NavResult.Canceled -> {}
            is NavResult.Value -> {
                when (result.value) {
                    is BsRecordEditorRecipientEvent.NotesSelectionFinish -> viewModel.onNotesSelectionUpdate(
                        (result.value as BsRecordEditorRecipientEvent.NotesSelectionFinish).notesSelection
                    )

                    BsRecordEditorRecipientEvent.NotesManagerNavUp -> navigator.navigate(
                        BsRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                }
            }
        }
    }

    BackHandler {
        viewModel.onBack()
    }

    BsTheme {
        BsRecordEditor(
            navigator = navigator,
            viewModel = viewModel,
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState,
        )
    }
}

@Composable
private fun BsRecordEditor(
    navigator: DestinationsNavigator,
    viewModel: BsRecordEditorViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: BsRecordEditorViewState,
) {
    val context = LocalContext.current

    if (viewState.showBsTypeDialog) {
        BsTypeDialog(bsUnit = viewState.bsUnit, onDismiss = viewModel::onDismissBpTypeDialog)
    }

    if (viewState.showDeleteRecordDialog) {
        AlertDialog(onDismissRequest = viewModel::onDismissDeleteRecordDialog,
            confirmButton = {
                TextButton(onClick = {
                    viewModel.onDelete()
                    logEventRecord("click_blood_sugar_record_delete")
                }) {
                    Text(text = stringResource(id = R.string.text_delete))
                }
            },
            dismissButton = {
                TextButton(onClick = viewModel::onDismissDeleteRecordDialog) {
                    Text(text = stringResource(id = R.string.text_cancel))
                }
            },
            title = {
                Text(
                    text = stringResource(R.string.text_delete_record),
                    style = MaterialTheme.typography.titleLarge,
                )
            },
            text = {
                Text(text = stringResource(R.string.text_tips_delete_record))
            })
    }

    if (viewState.showExitEditorTipsDialog) {
        if (viewState.editorMode == BsRecordEditorMode.Add) {
            DiscardThisXTipsDialog(
                onDismiss = viewModel::onDismissExitEditorTipsDialog,
                onDiscard = {
                    viewModel.onDismissExitEditorTipsDialog()
                    viewModel.onBack(true)
                },
                title = stringResource(id = R.string.text_title_discard_record),
            )
        }
    }

    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
    Scaffold(
        topBar = {
            BsRecordEditorAppBar(
                editorMode = viewState.editorMode,
                navUp = navigator::navigateUp,
                onQuestionClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_article", onAdLoadingAfter = {
                            viewState.bsArticle?.let { navigator.navigate(ArticleDestination(it)) }
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = {
                                viewState.bsArticle?.let { navigate(ArticleDestination(it)) }
                            },
                            adPlaceName = "enter_article"
                        )
                    }
                },
                onDeleteClick = if (viewState.editorMode == BsRecordEditorMode.Edit) {
                    { viewModel.onShowDeleteRecordDialog() }
                } else null
            )
        },
        bottomBar = {
            Column(
                Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
                    .padding(horizontal = 16.dp)
            ) {
                CardButton(
                    text = stringResource(id = R.string.text_save),
                    onClick = viewModel::onSave,
                    enabled = (viewState.mmolL != null) || (viewState.mgDl != null),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 84.dp),
                    containerBrush = AppTheme.Color.BsBrush
                )

                BlankSpacer(height = 12.dp)

                SmartRectAd(
                    pageType = SmartAdPageType.FEAT_BOTTOM_BAR,
                    bannerAdPlace = BannerAdPlace.BS_EDITOR,
                    nativeAdPlace = NativeAdPlace.BsEditor,
                )
            }
        },
        containerColor = AppTheme.Color.BsBackground,
    ) {
        val inputFocusRequester = FocusRequester()

        val imeController = LocalSoftwareKeyboardController.current

        Column(
            modifier = Modifier
                .noRippleClickable {
                    inputFocusRequester.freeFocus()
                    imeController?.hide()
                }
                .fillMaxSize()
                .padding(it)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)
            BsEditor(
                viewState = viewState,
                inputFocusRequester = inputFocusRequester,
                onStatusInfoTypeClick = viewModel::onShowBpTypeDialog,
                onMmolLChange = viewModel::onMmolLChange,
                onMgDlChange = viewModel::onMgDlChange,
                onBsUnitChange = viewModel::onBsUnitChange,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            BsDateTimePick(
                viewState, viewModel,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            BsEditorNotesItem(
                onNotesEdit = {
                    navigator.navigate(
                        BsRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                },
                notesSelection = viewState.notesSelection,
                modifier = Modifier.padding(horizontal = 16.dp).defShadow()
            )

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.FEAT_CONTENT,
                bannerAdPlace = BannerAdPlace.BS_EDITOR,
                nativeAdPlace = NativeAdPlace.BsEditor,
            )
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Suppress("LocalVariableName")
@Composable
private fun BsEditor(
    viewState: BsRecordEditorViewState,
    inputFocusRequester: FocusRequester,
    onStatusInfoTypeClick: () -> Unit,
    onMmolLChange: (Float?) -> Unit,
    onMgDlChange: (Float?) -> Unit,
    onBsUnitChange: (BsUnit) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Surface(
            modifier = Modifier.defShadow(),
            shape = RoundedCornerShape12Dp,
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                BlankSpacer(height = 16.dp)

                BsRecordStateSwitch(bsRecordState = viewState.bsRecordState)

                BlankSpacer(height = 10.dp)

                Row(
                    modifier = Modifier.bodyWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    BlankSpacer(width = 24.dp)

                    val style = MaterialTheme.typography.titleMedium.copy(
                        AppTheme.Color.textPrimary.copy(.9f)
                    )

                    Text(
                        text = (viewState.bsStatus?.titleStringId?.let { stringResource(it) }
                            ?: "- -") + " : ",
                        style = style
                    )

                    val des = when (viewState.bsUnit) {
                        BsUnit.SI -> viewState.bsStatus?.descriptionStringIdForSi?.let {
                            stringResource(id = it)
                        }

                        BsUnit.NonSI -> viewState.bsStatus?.descriptionStringIdForNonSi?.let {
                            stringResource(id = it)
                        }
                    }

                    Text(text = des ?: "- -", style = style)

                    BlankSpacer(width = 12.dp)

                    Icon(
                        modifier = Modifier.noRippleClickable(onStatusInfoTypeClick),
                        imageVector = Icons.Rounded.Info,
                        contentDescription = null,
                        tint = Color(0xFFAA8ED9)
                    )
                }

                val (value, onValueChange) = when (viewState.bsUnit) {
                    BsUnit.SI -> viewState.mmolL to onMmolLChange
                    BsUnit.NonSI -> viewState.mgDl to onMgDlChange
                }

                var textFieldValue by remember(viewState.bsUnit, viewState.stateHandleFinished) {
                    val _value = value?.scale(1, RoundingMode.DOWN)?.toString() ?: ""

                    mutableStateOf(
                        TextFieldValue(
                            _value,
                            selection = TextRange(_value.lastIndex + 1)
                        )
                    )
                }

                UnderlineTextField(
                    value = textFieldValue,
                    onValueChange = {
                        onValueChange(it.text.toFloatOrNull())
                        textFieldValue = it
                    },
                    textStyle = MaterialTheme.typography.headlineMedium.copy(
                        textAlign = TextAlign.Center,
                        color = AppTheme.Color.textPrimary,
                        fontSize = 29.sp,
                    ),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions.Default.copy(
                        keyboardType = KeyboardType.Number
                    ),
                    modifier = Modifier
                        .width(98.dp)
                        .focusRequester(inputFocusRequester)
                        .scale(.98f),
                    underlineColor = AppTheme.Color.BsPrimary
                )

                val imeController = LocalSoftwareKeyboardController.current
                LaunchedEffect(Unit) {
                    inputFocusRequester.requestFocus()
                    imeController?.hide()
                }

                BlankSpacer(height = 10.dp)

                BsUnitSwitch(
                    currentUnit = viewState.bsUnit,
                    onSwitch = onBsUnitChange,
                    modifier = Modifier.scale(.92f)
                )

                BlankSpacer(height = 16.dp)
            }
        }
    }
}

@Composable
private fun BsDateTimePick(
    viewState: BsRecordEditorViewState,
    viewModel: BsRecordEditorViewModel,
    modifier: Modifier = Modifier
) {

    Surface(
        shape = RoundedCornerShape12Dp,
        modifier = modifier.defShadow()
    ) {
        Column(Modifier.fillMaxWidth()) {
            Text(
                text = stringResource(R.string.text_date_n_time),
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(top = 14.dp)
            )

            DateTimePicker(
                dateTime = viewState.dateTime,
                onDateTimeChange = viewModel::onDateTimeChange,
                modifier = Modifier
                    .bodyWidth()
                    .scale(1.12f)
                    .padding(vertical = 4.dp)
            )
        }
    }
}

@Composable
private fun BsUnitSwitch(
    currentUnit: BsUnit,
    onSwitch: (BsUnit) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Surface(
            border = BorderStroke(2.dp, AppTheme.Color.BsPrimary),
            shape = RoundedCornerShape12Dp
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                BsUnit.entries.forEach {
                    val (textColor, backgroundColor) = if (currentUnit == it) {
                        Color.White to AppTheme.Color.BsPrimary
                    } else {
                        AppTheme.Color.BsPrimary.copy(.7f) to Color.White
                    }

                    Box(
                        modifier = Modifier
                            .noRippleClickable {
                                onSwitch(it)
                            }
                            .background(backgroundColor)
                    ) {
                        Text(
                            text = it.text,
                            modifier = Modifier.padding(vertical = 10.dp, horizontal = 24.dp),
                            style = MaterialTheme.typography.bodyMedium.copy(color = textColor)
                        )
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BsRecordEditorAppBar(
    editorMode: BsRecordEditorMode?,
    navUp: () -> Unit,
    onQuestionClick: () -> Unit,
    modifier: Modifier = Modifier,
    onDeleteClick: (() -> Unit)? = null,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = { Text(text = if (editorMode == null) "" else stringResource(id = editorMode.titleStringId)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                IconButton(onClick = onQuestionClick) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_question),
                        contentDescription = "",
                        modifier = Modifier.size(AppBarActionButtonDpSize),
                        tint = Color(0xFF926CBF)
                    )
                }

                if (onDeleteClick != null) {
                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            imageVector = Icons.Rounded.DeleteForever,
                            contentDescription = "delete",
                        )
                    }
                }
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.FEAT_TOP_BAR,
            bannerAdPlace = BannerAdPlace.BS_EDITOR,
            nativeAdPlace = NativeAdPlace.BsEditor,
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun BsEditorNotesItem(
    onNotesEdit: () -> Unit,
    notesSelection: List<String>,
    modifier: Modifier = Modifier,
) {
    Surface(onClick = onNotesEdit, modifier = modifier, shape = RoundedCornerShape12Dp) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.text_note),
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    imageVector = Icons.Rounded.Edit,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )

            }

            if (notesSelection.isNotEmpty()) {
                FlowRow {
                    notesSelection.forEach { note ->
                        Text(
                            text = "#$note",
                            modifier = Modifier.padding(horizontal = 4.dp),
                            style = MaterialTheme.typography.labelLarge.copy(color = AppTheme.Color.textSecondary)
                        )
                    }
                }

                BlankSpacer(height = 16.dp)
            }
        }
    }
}