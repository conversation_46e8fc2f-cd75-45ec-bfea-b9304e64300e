package com.nbpt.app.ui.screen.heartrate

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Alarm
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.compose.HrTheme
import com.nbpt.app.R
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.heartrate.HeartRateMonitor
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.HeartRateHistoryDestination
import com.nbpt.app.ui.screen.destinations.HeartRateMeasureDestination
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.noRippleClickable
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun Hr(
    navigator: DestinationsNavigator,
) {
    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    HrTheme {
        HeartRateScreen(
            navigator = navigator,
            viewModel = koinViewModel(),
            admobInterstitialAdViewModel = admobInterstitialAdViewModel
        )
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun HeartRateScreen(
    navigator: DestinationsNavigator,
    viewModel: HeartRateViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel
) {
//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//    }
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    viewModel.collectSideEffect {
        when (it) {
            is HeartRateSideEffect.NavTo -> navigator.navigate(it.destination)
        }
    }

    val viewState by viewModel.collectAsState()

    val heartRateMonitor: HeartRateMonitor = koinInject()

    var hasClickMeasure by remember { mutableStateOf(false) }
    val cameraPermissions = heartRateMonitor.checkCameraPermissions()

    val activity = LocalContext.current.findActivity()
    LaunchedEffect(cameraPermissions.status.isGranted, hasClickMeasure) {
        if (cameraPermissions.status.isGranted && hasClickMeasure) {
            navigator.navigate(HeartRateMeasureDestination)
        }
    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd(from = "hr_back_home", onAdLoadingAfter = {
                navigator.navigateUp()
            })
        } else {
            admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                activity = activity,
                adPlaceName = "hr_back_home"
            )
        }

        Unit
    }

    BackHandler(onBack = onBack)

    Scaffold(
        topBar = {
            HeartRateAppBar(
                navUp = onBack,
                openAlarmGroup = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_alarm", onAdLoadingAfter = {
                            navigator.navigate(AlarmGroupsDestination(showInterAdWhenBack = true))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = {
                                navigate(AlarmGroupsDestination(showInterAdWhenBack = true))
                            },
                            adPlaceName = "enter_alarm"
                        )
                    }

                    logEventRecord("click_alarm")
                },
                openHistory = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_hr_list", onAdLoadingAfter = {
                            navigator.navigate(HeartRateHistoryDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = {
                                navigate(HeartRateHistoryDestination)
                            },
                            adPlaceName = "enter_hr_list"
                        )
                    }
                    logEventRecord("click_heart_rate_history")
                })
        },
        bottomBar = {
            SmartRectAd(
                pageType = SmartAdPageType.HOME_BOTTOM_BAR,
                bannerAdPlace = BannerAdPlace.HR,
                nativeAdPlace = NativeAdPlace.Hr,
                modifier = Modifier.navigationBarsPadding(),
            )
        },
        containerColor = AppTheme.Color.HrBackground
    ) {

        val tapToMeasure = {
            hasClickMeasure = true
            if (!cameraPermissions.status.isGranted) {
                cameraPermissions.launchPermissionRequest()
            }
        }

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxWidth()

                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 12.dp)

            HeartRateTapToStart(
                onClick = tapToMeasure,
                modifier = Modifier
                    .padding(horizontal = Layout.bodyMargin)
                    .bodyWidth()
            )

            BlankSpacer(height = 4.dp)

            HeartRateInstruction(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Layout.bodyMargin)
            )

            SmartRectAd(
                pageType = SmartAdPageType.HOME_CONTENT,
                bannerAdPlace = BannerAdPlace.HR,
                nativeAdPlace = NativeAdPlace.Hr,
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeartRateAppBar(
    navUp: () -> Unit,
    openAlarmGroup: () -> Unit,
    openHistory: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = {
                Text(text = stringResource(id = R.string.heart_rate_title))
            },
            actions = {
                IconButton(onClick = openAlarmGroup) {
                    Icon(
                        imageVector = Icons.Rounded.Alarm,
                        contentDescription = "alarm",
                        modifier = Modifier.size(AppBarActionButtonDpSize)
                    )
                }
                IconButton(onClick = openHistory) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_history),
                        contentDescription = "history",
                        modifier = Modifier.size(AppBarActionButtonDpSize)
                    )
                }
            },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
        )


        SmartRectAd(
            pageType = SmartAdPageType.HOME_TOP_BAR,
            bannerAdPlace = BannerAdPlace.HR,
            nativeAdPlace = NativeAdPlace.Hr,
        )
    }
}

@Composable
private fun HeartRateTapToStart(
    onClick: () -> Unit, modifier: Modifier = Modifier
) {
    Box(modifier = modifier.noRippleClickable { onClick() }) {
        Image(
            painter = painterResource(id = R.drawable.img_hr_tap_to_start),
            contentDescription = null,
            modifier = Modifier
                .width(240.dp)
                .aspectRatio(
                    ratio = 460 / 420f, matchHeightConstraintsFirst = true
                )
        )

        Column(
            horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 58.dp)
        ) {
            Text(
                text = stringResource(R.string.text_measure_now),
                style = MaterialTheme.typography.bodyLarge.copy(
                    lineHeight = 15.sp,
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Bold,
                    color = AppTheme.Color.White
                ),
                modifier = Modifier.width(122.dp),
                textAlign = TextAlign.Center,
                minLines = 2,
                maxLines = 2,
            )
        }

    }
}

@Composable
private fun HeartRateInstruction(
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Text(
            text = stringResource(R.string.text_instruction),
            style = MaterialTheme.typography.titleMedium,
        )

        Column(
            modifier = Modifier.bodyWidth()
        ) {

            BlankSpacer(height = 12.dp)

            HeartRateInstructionItem(
                index = "1",
                content = stringResource(R.string.text_tips_hr_instruction_1),
                modifier = Modifier.fillMaxWidth(),
            )
            BlankSpacer(height = 14.dp)
            HeartRateInstructionItem(
                index = "2",
                content = stringResource(R.string.text_tips_hr_instruction_2),
                modifier = Modifier.fillMaxWidth(),
            )
            BlankSpacer(height = 14.dp)
            HeartRateInstructionItem(
                index = "3",
                content = stringResource(R.string.text_tips_hr_instruction_3),
                modifier = Modifier.fillMaxWidth(),
            )
            BlankSpacer(height = 22.dp)
        }
    }
}

@Composable
private fun HeartRateInstructionItem(
    index: String, content: String, modifier: Modifier = Modifier
) {
    Surface(
        shape = RoundedCornerShape12Dp,
        modifier = modifier.defShadow(),
    ) {
        Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Surface(
                modifier = Modifier.size(64.dp),
                shape = RoundedCornerShape12Dp,
                color = AppTheme.Color.HrPrimary.copy(.5f)
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Text(
                        text = index,
                        style = MaterialTheme.typography.headlineMedium.copy(color = AppTheme.Color.textPrimary),
                    )
                }
            }
            Text(
                text = content,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }
    }
}

@Preview
@Composable
private fun HeartRateTapToStartPreview() {
    HeartRateTapToStart(onClick = {})
}

@Preview
@Composable
private fun HeartRateScreenPreview() {
    HeartRateScreen(
        navigator = EmptyDestinationsNavigator,
        viewModel = koinViewModel(),
        admobInterstitialAdViewModel = koinViewModel()
    )
}
