package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth

@Composable
fun BpTypeDialog(
    onDismiss: () -> Unit,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            CardButton(
                text = stringResource(R.string.text_ok), onClick = onDismiss, modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 22.dp),
                containerBrush = AppTheme.Color.BpBrush
            )
        },
        title = {
            Text(
                text = stringResource(R.string.text_blood_pressure_type),
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.bodyWidth(),
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
            ) {
                BpStatus.entries.forEachIndexed { index, bpStatus ->
                    BpTypeItem(bpStatus = bpStatus)

                    if (index != BpStatus.entries.lastIndex) {
                        BlankSpacer(height = Layout.bodyMargin)
                    }
                }
            }
        },
        containerColor = AppTheme.Color.BpBackground
    )
}

@Composable
fun BpTypeItem(
    bpStatus: BpStatus,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        BlankWidthIn(min = Layout.gutter, max = Layout.bodyMargin)

        Surface(
            shape = CircleShape,
            color = bpStatus.color,
            modifier = Modifier.size(size = 24.dp)
        ) {}

        BlankSpacer(width = Layout.bodyMargin)

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = stringResource(id = bpStatus.titleStringId),
                style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary)
            )
            BlankSpacer(height = Layout.gutter / 2)
            Text(
                text = stringResource(id = bpStatus.descriptionStringId),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = AppTheme.Color.textSecondary,
                    fontSize = 13.sp
                )
            )
        }
    }
}

@Preview
@Composable
private fun BpTypeDialogPreview() {
    AppMd3Theme {
        var showDialog by remember {
            mutableStateOf(true)
        }

        BpTypeDialog(onDismiss = { showDialog })
    }
}
