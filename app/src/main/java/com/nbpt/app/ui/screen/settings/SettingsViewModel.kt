package com.nbpt.app.ui.screen.settings

import android.content.Context
import androidx.lifecycle.ViewModel
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmType
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
//import com.nbpt.app.biz.admanager.rewardedInterstitial.RewardedInterstitialAdManager
import com.nbpt.app.common.android.PendingSharedFile
import com.nbpt.app.common.android.shareFile
import com.nbpt.app.common.datatransform.RecordsToCvsTransformer
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.db.dao.BsRecordDao
import com.nbpt.app.data.db.dao.HrRecordDao
import com.nbpt.app.ui.common.ExportDataType
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class SettingsViewModel(
    private val bpRecordDao: BpRecordDao,
    private val hrRecordDao: HrRecordDao,
    private val bsRecordDao: BsRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore,
    private val recordsToCvsTransformer: RecordsToCvsTransformer,
) : ViewModel(), ContainerHost<SettingsViewState, SettingsSideEffect> {

    override val container: Container<SettingsViewState, SettingsSideEffect> =
        container(SettingsViewState.Empty)

//    @SuppressLint("StaticFieldLeak")
//    private var showAdContainer: Activity? = null
//    private var showInterAdAfterDestination: Direction? = null
//
//    private sealed interface ExecuteInterAd {
//        object OnExport : ExecuteInterAd
//        object Navigate : ExecuteInterAd
//    }
//
//    private val executeInterAdFlow = MutableStateFlow<ExecuteInterAd?>(null)
//
//
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent{
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.Navigate }
//
//        showAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun onShowAdAndExport(context: Context) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.OnExport }
//
//        val activity = context.findActivity()
//        showAdContainer = activity
//
//        interstitialAdManager.tryToShowAd(activity, "export_file")
//    }

    fun onExport(context: Context, exportDataType: ExportDataType) = intent {
//        val activity = context.findActivity()
//        showAdContainer = activity
//
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        rewardedInterstitialAdManager.tryToShowAd(activity, "on_export_bp_data")
//
//        val earnedRewardSuccessful =
//            rewardedInterstitialAdManager.adEarnedRewardEventFlow.firstOrNull()
//
//        debugLog(tag = "SettingsViewModel") { "earnedRewardSuccessful: $earnedRewardSuccessful" }
//
//        showAdContainer = null
//        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//        if (earnedRewardSuccessful != true) return@intent

        if (state.dataExporting) return@intent

        reduce { state.copy(dataExporting = true) }

        val dataFile = when (exportDataType) {
            RemindToRecordAlarmType.BloodPressure -> {
                val records = bpRecordDao.fetchAllRecordsFlow().first()
                recordsToCvsTransformer.transformBp(records, context, "My_Blood_Pressure_Data")
            }

            RemindToRecordAlarmType.HeartRate -> {
                val records = hrRecordDao.fetchAllRecordsFlow().first()
                recordsToCvsTransformer.transformHr(records, context, "My_Heart_Rate_Data")
            }

            RemindToRecordAlarmType.BloodSugar -> {
                val records = bsRecordDao.fetchAllRecordsFlow().first()
                val bsUnit = userBehaviorDataStore.bsUnit
                recordsToCvsTransformer.transformBs(bsUnit, records, context, "My_Blood_Sugar_Data")
            }
        }

        reduce { state.copy(dataExporting = false) }

        val subject = "Blood Pressure Data (${nowInstant().toLocalDatetime().date.isoFormat()})"

        context.shareFile(
            PendingSharedFile(
                file = dataFile,
                fileMimeType = "text/csv",
                shareSubject = subject,
                shareTitle = context.getString(R.string.text_data_export)
            )
        )
    }

    fun switchShowExportDataDialog(show: Boolean) = intent {
        reduce { state.copy(showExportDataDialog = show) }
    }

////    fun registerInterRewardedAdEventFlow(lifecycleScope: CoroutineScope) {
////        rewardedInterstitialAdManager.adLoadingStateEventFlow.onEach {
////            when (it) {
////                RewardedInterstitialAdManager.AdLoadingStateEvent.FailedToLoad,
////                RewardedInterstitialAdManager.AdLoadingStateEvent.TimeOut -> intent {
////                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
////                    showAdContainer = null
////                    rewardedInterstitialAdManager.adEarnedRewardEventFlow.send(false)
////                }
////
////                RewardedInterstitialAdManager.AdLoadingStateEvent.Loaded -> {
////                    showAdContainer?.let { containerActivity ->
////                        rewardedInterstitialAdManager.tryToShowAd(containerActivity, null)
////                    }
////                }
////            }
////        }.launchIn(lifecycleScope)
////    }
//
//    fun registerInterAdEventFlow(
//        context: Context,
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    handleInterAdFinishToDo(context)
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    handleInterAdFinishToDo(context)
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
//
//    private fun handleInterAdFinishToDo(
//        context: Context
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//        showAdContainer = null
//
//        when (executeInterAdFlow.first()) {
//            ExecuteInterAd.OnExport -> onExport(context)
//            ExecuteInterAd.Navigate -> intent {
//                showInterAdAfterDestination?.let { destination ->
//                    postSideEffect(SettingsSideEffect.NavTo(destination))
//                }
//                showInterAdAfterDestination = null
//            }
//
//            null -> {}
//        }
//
//        executeInterAdFlow.update { null }
//    }
}
