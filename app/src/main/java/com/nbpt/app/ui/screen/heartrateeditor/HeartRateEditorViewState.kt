package com.nbpt.app.ui.screen.heartrateeditor

import com.nbpt.app.data.healtharticles.HealthArticle
import kotlinx.datetime.Instant


data class HeartRateEditorViewState(
    val editorMode: HeartRateEditorMode? = null,
    val instant: Instant? = null,
    val gender: Gender? = null,
    val age: Int = 25,
    val heartRateBpm: Int = 75,
    val notesSelection: List<String> = emptyList(),
    val showGenderDialog: Boolean = false,
    val showAgeDialog: Boolean = false,
    val showHeartRateDeleteDialog: Boolean = false,
    val showHeartRateStatusDialog: Boolean = false,
    val showExitEditorTipsDialog: <PERSON>olean = false,
    val showAdLoadingDialog: Boolean = false,
    val isUnlockAnalysis: Boolean = false,

    val hrArticles: List<HealthArticle> = emptyList(),
) {
    companion object {
        val Empty = HeartRateEditorViewState()
    }
}
