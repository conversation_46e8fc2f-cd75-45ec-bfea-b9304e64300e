package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.common.calculate.mmolLToMgDL
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.db.model.BsRecordEntity
import com.nbpt.app.data.db.model.notesText
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bs.BsTheme
import com.nbpt.app.ui.theme.defShadow
import kotlinx.datetime.Clock
import java.math.RoundingMode
import java.time.format.FormatStyle

@Composable
fun BsRecordItem(
    bsUnit: BsUnit,
    bsRecord: BsRecordEntity,
    modifier: Modifier = Modifier,
    onEditClick: (() -> Unit)? = null
) {
    val (status, bsValue) = when (bsUnit) {
        BsUnit.SI -> Pair(BsStatus.fromMmolL(bsRecord.mmolL), bsRecord.mmolL)
        BsUnit.NonSI -> {
            val mgDl = mmolLToMgDL(bsRecord.mmolL)
            Pair(BsStatus.fromMgDl(mgDl), mgDl)
        }
    }

    Surface(
        onClick = { onEditClick?.invoke() },
        modifier = modifier.defShadow(),
        shape = RoundedCornerShape12Dp,
    ) {

        Row(
            Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Max)
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 16.dp)

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                Text(
                    text = bsValue.scale(1, RoundingMode.DOWN).toString(),
                    style = MaterialTheme.typography.headlineSmall.copy(fontWeight = FontWeight.Bold),
                )

                BlankSpacer(height = 2.dp)

                Text(
                    text = bsUnit.text,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = AppTheme.Color.textSecondaryDark
                    )
                )

            }

            BlankSpacer(width = 16.dp)

            Surface(
                color = status.color,
                modifier = Modifier
                    .fillMaxHeight()
                    .width(4.dp),
                shape = RoundedCornerShape12Dp,
                content = {}
            )

            BlankSpacer(width = 16.dp)

            Box(
                Modifier.weight(1f)
            ) {
                Column {

                    val dateTime = bsRecord.instant.toLocalDatetime()
                    val dateText = dateTime.date.isoFormat(FormatStyle.MEDIUM)
                    val timeText = dateTime.time.isoFormat()
                    val dateTimeText = "$timeText  $dateText"
                    val hasNotes = bsRecord.notesText.isNotEmpty()

                    Text(
                        text = stringResource(id = status.titleStringId),
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.ExtraBold,
                            fontSize = 18.sp
                        )
                    )

                    if (hasNotes) {
                        BlankSpacer(height = 2.dp)
                    } else {
                        BlankSpacer(height = 8.dp)
                    }

                    Text(
                        text = dateTimeText,
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = AppTheme.Color.textSecondary,
                            fontWeight = FontWeight.Bold
                        )
                    )


                    if (hasNotes) {
                        BlankSpacer(height = 2.dp)

                        Text(
                            text = bsRecord.notesText,
                            style = MaterialTheme.typography.bodySmall.copy(color = AppTheme.Color.textSecondary)
                        )
                    }


                    BlankSpacer(height = 2.dp)
                }
            }


            if (onEditClick != null) {
                IconButton(
                    onClick = onEditClick
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Edit,
                        contentDescription = "edit",
                    )
                }

                BlankSpacer(width = 2.dp)
            }

        }
    }
}

@Preview
@Composable
fun BsRecordItemPreview() {
    BsTheme {
        BsRecordItem(
            bsUnit = BsUnit.SI,
            bsRecord = BsRecordEntity(
                uuid = "",
                instant = Clock.System.now(),
                mmolL = 2.1f,
                state = BsRecordState.Default,
                notes = listOf("qwerty", "abc xyz", "lalala")
            ),
            onEditClick = {}
        )
    }
}
