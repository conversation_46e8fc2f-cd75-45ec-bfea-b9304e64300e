package com.nbpt.app.ui.theme

import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color

object AppTheme {
    object Color {
        val Primary = Color(0xFF7A4CC6)
        val PrimaryMedium = Color(0xFF8E61D2)
        val PrimaryLight = Color(0xFFAE80E3)
        val PrimaryBackground = Color(0xFFFBF8FF)
        val PrimaryBrush = Brush.verticalGradient(
            listOf(PrimaryLight, Primary)
        )
        val PrimaryBrushHorizontal = Brush.horizontalGradient(
            listOf(PrimaryLight, Primary)
        )

        val Red = Color(0xFFFF6565)
        val White = androidx.compose.ui.graphics.Color.White

        val textPrimary = Color(0xFF43435E)
        val textSecondary = Color(0xFF9FA2A5)
        val textSecondaryDark = Color(0xFF8C8B9E)

        val background = Color(0xFFFBF8FF)

        val cardContainer = White
        val cardGray = Color(0xFFFBF8FF)

        val BpPrimary = Color(0xFFFC6C78)
        val BpPrimaryLight = Color(0xFFFD8785)
        val BpPrimaryL2 = Color(0xFFC57272)
        val BpPrimaryL3 = Color(0xFFD98E8E)
        val BpBackground = Color(0xFFFBF5F5)
        val BpBrush = Brush.verticalGradient(
            listOf(Color(0xFFFEA996), BpPrimary)
        )

        val HrPrimary = Color(0xFFFAA0C1)
        val HrBackground = Color(0xFFFFF6FB)
        val HrBrush = Brush.verticalGradient(
            listOf(Color(0xFFFFB2DD), HrPrimary)
        )

        val AlarmPrimary = Color(0xE64482FF)
        val AlarmPrimaryLight = Color(0xFF5A9AFD)
        val AlarmBackground = Color(0xFFF2F8FF)
        val AlarmBrush = Brush.verticalGradient(
            listOf(Color(0xFFA8DBFD), AlarmPrimaryLight)
        )

        val BsPrimary = Color(0xFFA581F5)
        val BsBackground = Color(0xFFFAF6FF)
        val BsBrush = Brush.verticalGradient(
            listOf(Color(0xFFC28DFE), BsPrimary)
        )

        val BmiPrimary = Color(0xFF30CB86)
        val BmiBackground = Color(0xFFF2F9F6)
        val BmiBrush = Brush.verticalGradient(
            listOf(Color(0xFF92E3BF), BmiPrimary)
        )
    }

    object BpStatusColor {
        val hypotension = Color(0xFF795EFC)
        val normal = Color(0xFF618EFF)
        val elevated = Color(0xFF3FCD7A)
        val hypertensionS1 = Color(0xFFFFAB3A)
        val hypertensionS2 = Color(0xFFFF5858)
        val hypertensive = Color(0xFFDC3232)
    }

    object HrStatusColor {
        val slow = Color(0xFF4A7EF7)
        val normal = Color(0xFF21C77E)
        val fast = Color(0xFFDC3232)
    }

    object BmiStatusColor {
        val verySeverelyUnderweight = Color(0xFF7334D7)
        val severelyUnderweight = Color(0xFF3036D6)
        val underweight = Color(0xFF4A7EF7)
        val normal = Color(0xFF21C77E)
        val overweight = Color(0xFFF8D379)
        val obeseClassI = Color(0xFFF3B380)
        val obeseClassII = Color(0xFFED7759)
        val obeseClassIII = Color(0xFFDC3232)
    }

    object BsStatusColor {
        val low = Color(0xFFFDE3B5)
        val normal = Color(0xFFE0C1FF)
        val preDiabetes = Color(0xFFFFBAD2)
        val diabetes = Color(0xFFFFC6B4)
    }
}
