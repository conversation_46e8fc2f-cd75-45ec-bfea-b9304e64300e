package com.nbpt.app.ui.common

import android.content.Context
import android.graphics.Typeface
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.SmartDisplay
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.nbpt.app.R
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.db.model.BpRecordEntity
import com.nbpt.app.ui.common.chart.RoundedBarChart
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.toPx
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.formatter.LargeValueFormatter
import com.github.mikephil.charting.formatter.ValueFormatter
import kotlinx.coroutines.flow.first
import org.koin.compose.koinInject
import java.time.format.FormatStyle

@Suppress("ObjectPropertyName")
private var _statTypeface: Typeface? = null

fun statTypeface(context: Context): Typeface? {
    if (_statTypeface != null) return _statTypeface

    val tf = ResourcesCompat.getFont(context, R.font.figtree_bold)
    _statTypeface = tf
    return _statTypeface
}

@Composable
fun BpRecordStat(
    isUnlock: Boolean,
    onWatchAd: () -> Unit,
    records: List<BpRecordEntity>,
    modifier: Modifier = Modifier,
    xRangeMax: Int = 5
) {
    val context = LocalContext.current

    val systolicBarEntry = arrayListOf<BarEntry>()
    val diastolicBarEntry = arrayListOf<BarEntry>()

    val xAxisValues = mutableListOf<String>()

    records.forEachIndexed { index, bpRecordEntity ->
//        val instantFloat = index.toFloat()
        xAxisValues.add(bpRecordEntity.instant.toLocalDatetime().date.isoFormat(FormatStyle.SHORT))

        systolicBarEntry.add(BarEntry(index.toFloat(), bpRecordEntity.systolic.toFloat()))
        diastolicBarEntry.add(BarEntry(index.toFloat(), bpRecordEntity.diastolic.toFloat()))
    }

    val systolicDataSet = BarDataSet(
        systolicBarEntry,
        stringResource(id = R.string.text_systolic).lowercase()
    ).apply {
        color = AppTheme.Color.Primary.toArgb()
    }
    val diastolicDataSet = BarDataSet(
        diastolicBarEntry,
        stringResource(id = R.string.text_diastolic).lowercase()
    ).apply {
        color = AppTheme.Color.Red.toArgb()
    }

    val textColor = AppTheme.Color.textPrimary.toArgb()
    val textTypeface = statTypeface(context)

    val barData = BarData(
        systolicDataSet,
        diastolicDataSet
    ).apply {
        setValueFormatter(LargeValueFormatter())
        setValueTextSize(11.6f)
        textTypeface?.let {
            setValueTypeface(textTypeface)
        }
        setValueTextColor(textColor)
    }

    val barRoundedCornerSize = 20.dp.toPx()

    Surface(shape = RoundedCornerShape12Dp, modifier = modifier) {
        AndroidView(
            factory = {
                RoundedBarChart(it).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )

                    setRadius(barRoundedCornerSize.toInt())

                    data = barData

                    axisRight.isEnabled = false

                    axisLeft.apply {
                        textTypeface?.let {
                            typeface = textTypeface
                        }
                        setTextColor(textColor)
                        axisMinimum = 0f
                        axisMaximum = 300f
                        setDrawGridLines(false)
                        setScaleEnabled(false)
                        setPinchZoom(false)
                        textSize = 12f
                    }

                    xAxis.apply {
                        textTypeface?.let {
                            typeface = textTypeface
                        }
                        setTextColor(textColor)
                        position = XAxis.XAxisPosition.BOTTOM
                        textSize = 11f
                    }

                    legend.apply {
                        textTypeface?.let {
                            typeface = textTypeface
                        }
                        setTextColor(textColor)
                        verticalAlignment = Legend.LegendVerticalAlignment.TOP
                        horizontalAlignment = Legend.LegendHorizontalAlignment.RIGHT
                        setDrawInside(true)
                        textSize = 11f
                    }

                    setDrawBarShadow(false)
                    setDrawGridBackground(false)

                    description.isEnabled = false

                    isDragEnabled = true
                    isDoubleTapToZoomEnabled = false
                    setTouchEnabled(true)
                    setScaleEnabled(true)

                    invalidate()
                }
            }, modifier = modifier
                .padding(top = 4.dp, bottom = 10.dp)
                .padding(start = 4.dp, end = 2.dp)
        ) {
            it.apply {
                val groupSpace = .2f
                val barSpace = .1f
                val barWidth = .3f

                data = barData

                getBarData().barWidth = barWidth

                xAxis.apply {
                    valueFormatter = XAxisStringsFormatter(xAxisValues)
                    axisMinimum = 0f
                    axisMaximum = getBarData().getGroupWidth(groupSpace, barSpace) * records.size
                    granularity = 1f
                    setCenterAxisLabels(true)
                }

                groupBars(0f, groupSpace, barSpace);

                setVisibleXRangeMaximum(xRangeMax.toFloat())
                moveViewToX(Long.MAX_VALUE.toFloat())
                notifyDataSetChanged()
                invalidate()
            }
        }

        if (!isUnlock) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .clickable { }
                    .background(color = Color(0xB3080931))
                    .fillMaxWidth()
            ) {
                IconButton(onClick = onWatchAd) {
                    Icon(
                        imageVector = Icons.Rounded.SmartDisplay,
                        contentDescription = null,
                        modifier = Modifier.size(44.dp),
                        tint = AppTheme.Color.White
                    )
                }

                BlankSpacer(height = 10.dp)

                Text(
                    text = stringResource(R.string.text_view_ad_to_access_trend_data),
                    style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.White)
                )

                BlankSpacer(height = 6.dp)
            }
        }
    }
}

class XAxisStringsFormatter(private val values: List<String>) : ValueFormatter() {

    override fun getFormattedValue(value: Float): String {
        return values.getOrElse(value.toInt()) { "" }
    }
}

object YAxisIntFormatter : ValueFormatter() {
    override fun getFormattedValue(value: Float): String {
        return value.toInt().toString()
    }
}

@Preview
@Composable
private fun BpRecordStatPreview() {

    val recordDao: BpRecordDao = koinInject()

    var records by remember {
        mutableStateOf(emptyList<BpRecordEntity>())
    }

    LaunchedEffect(Unit) {
        records = recordDao.fetchAllRecordsInstantAscendFlow().first()
    }

    BpRecordStat(
        isUnlock = false,
        onWatchAd = {},
        records = records,
        modifier = Modifier
            .fillMaxWidth()
            .height(220.dp)
    )
}
