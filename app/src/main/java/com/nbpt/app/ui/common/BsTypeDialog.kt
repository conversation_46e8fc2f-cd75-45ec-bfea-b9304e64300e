package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.adt.entries
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bs.BsTheme

@Composable
fun BsTypeDialog(
    bsUnit: BsUnit,
    onDismiss: () -> Unit,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            CardButton(
                text = stringResource(R.string.text_ok), onClick = onDismiss, modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 22.dp),
                containerBrush = AppTheme.Color.BsBrush
            )
        },
        title = {
            Text(
                text = stringResource(R.string.text_blood_sugar_type),
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.bodyWidth(),
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .bodyWidth()
                    .padding(end = 12.dp)
            ) {
                BsStatus.entries.forEachIndexed { index, bsStatus ->
                    BsTypeItem(bsUnit = bsUnit, bsStatus = bsStatus)

                    if (index != BsStatus.entries.lastIndex) {
                        BlankSpacer(height = Layout.bodyMargin)
                    }
                }
            }
        },
        containerColor = AppTheme.Color.BsBackground
    )
}

@Composable
fun BsTypeItem(
    bsUnit: BsUnit,
    bsStatus: BsStatus,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        BlankWidthIn(min = Layout.gutter, max = Layout.bodyMargin)

        Surface(
            shape = CircleShape,
            color = bsStatus.color,
            modifier = Modifier.size(size = 24.dp)
        ) {}

        BlankSpacer(width = Layout.bodyMargin)

        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = bsStatus.titleStringId),
                style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary, fontSize = 18.sp)
            )

            BlankSpacer(width = 6.dp)

            val des = when (bsUnit) {
                BsUnit.SI -> stringResource(id = bsStatus.descriptionStringIdForSi)
                BsUnit.NonSI -> stringResource(id = bsStatus.descriptionStringIdForNonSi)
            }

            Text(
                text = ": $des",
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = AppTheme.Color.textSecondary,
                    fontSize = 16.sp
                ),
                modifier = Modifier.padding(top = 1.dp)
            )
        }
    }
}

@Preview
@Composable
private fun BsTypeDialogPreview() {
    AppMd3Theme {
        BsTheme {
            BsTypeDialog(bsUnit = BsUnit.SI, onDismiss = { })
        }
    }
}
