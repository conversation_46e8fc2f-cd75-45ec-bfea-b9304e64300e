package com.nbpt.app.ui.screen.alarmgroups

import android.annotation.SuppressLint
import androidx.activity.compose.BackHandler
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.zIndex
import androidx.lifecycle.Lifecycle
import com.nbpt.app.ui.theme.alarm.AlarmTheme
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.alarm.remindtorecord.AlarmGroup
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmType
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.alarmeditor.AlarmEditorDayOfWeekMultiPicker
import com.nbpt.app.ui.screen.alarmeditor.AlarmEditorMode
import com.nbpt.app.ui.screen.destinations.AlarmEditorDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.autoMirror
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import kotlinx.datetime.LocalTime
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun AlarmGroups(
    navigator: DestinationsNavigator,
    showInterAdWhenBack: Boolean = false
) {
    val context = LocalContext.current

    AlarmTheme {
        val viewModel: AlarmGroupsViewModel = koinViewModel()
        val viewState: AlarmGroupsViewState by viewModel.collectAsState()

        val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
        val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

        LaunchedEffect(Unit) {
            admobInterstitialAdViewModel.registerInterAdEventFlow(this)
        }

        admobInterstitialAdViewModel.RegisterHandleSideEffect(
            navigator = navigator
        )

        if (admobInterstitialAdViewState.adLoading) {
            AdLoadingDialog()
        }

        AlarmGroups(
            navigator = navigator,
            viewModel = viewModel,
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState,
            showInterAdWhenBack = showInterAdWhenBack
        )

        val interstitialAdManager: MaxInterstitialAdManager = koinInject()
        BackHandler {
            if (viewState.showRemindTypeChooseLay) {
                viewModel.onDismissRemindTypeLay()
            } else {
                if (showInterAdWhenBack) {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd(from = "exit_alarm", onAdLoadingAfter = {
                            navigator.navigateUp()
                        })
                    } else {
                        admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                            activity = context.findActivity(),
                            adPlaceName = "exit_alarm"
                        )
                    }
                } else {
                    navigator.navigateUp()
                }
            }
        }

        OnLifecycleEvent { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> {
                    viewModel.onRefresh()
                }

                else -> {}
            }
        }

//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//    }

        viewModel.collectSideEffect {
            when (it) {
                is AlarmGroupsSideEffect.NavTo -> navigator.navigate(it.destination)
                is AlarmGroupsSideEffect.NavUp -> navigator.navigateUp()
            }
        }

//        if (viewState.showInterstitialAdLoadingDialog) {
//            AdLoadingDialog()
//        }

//    BackHandler {
//        viewModel.onBack(
//            activity = context.findActivity(),
//            adPlaceName = "back_alarm_list"
//        )
//    }
    }
}

@Composable
private fun AlarmGroups(
    navigator: DestinationsNavigator,
    showInterAdWhenBack: Boolean,
    viewModel: AlarmGroupsViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: AlarmGroupsViewState,
) {
    val activity = LocalContext.current.findActivity()
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    if (viewState.showRemindTypeChooseLay) {
        RemindToRecordChooseLay(
            onDismiss = viewModel::onDismissRemindTypeLay,
            onChoose = {
                viewModel.onDismissRemindTypeLay()

                navigator.navigate(
                    AlarmEditorDestination(
                        alarmGroup = AlarmGroup.Default.copy(
                            typeSid = it.sid
                        )
                    )
                )

                logEventRecord("click_add_alarm_${it.typeName.replace(" ", "_").lowercase()}")
            }
        )
    }

    Scaffold(
        topBar = {
            AlarmGroupsAppBar(navUp = {
                if (showInterAdWhenBack) {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd(from = "exit_alarm", onAdLoadingAfter = {
                            navigator.navigateUp()
                        })
                    } else {
                        admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                            activity = activity,
                            adPlaceName = "exit_alarm"
                        )
                    }
                } else {
                    navigator.navigateUp()
                }
            })
        },
        bottomBar = {
            ListAdaptiveRectangleAd(
                bannerAdPlace = BannerAdPlaceholder.ALARM_LIST,
                nativeAdPlace = NativeAdPlaceholder.Alarm,
                modifier = Modifier.navigationBarsPadding()
            )
        },
        floatingActionButton = {
            AlarmGroupsFabAdd(
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd(from = "add_alarm", onAdLoadingAfter = {
                            viewModel.onShowRemindTypeLay()
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = activity,
                            navAction = { viewModel.onShowRemindTypeLay() },
                            adPlaceName = "add_alarm"
                        )
                    }
                },
                isAlarmGroupEmpty = viewState.showAlarmGroupEmpty
            )
        },
        containerColor = AppTheme.Color.AlarmBackground
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .padding(horizontal = Layout.bodyMargin)
                .fillMaxSize()
        ) {
//                Box(
//                    contentAlignment = Alignment.Center,
//                    modifier = Modifier.bodyWidth()
//                ) {
//                    NativeAd(
//                        place = NativeAdPlace.Alarm,
//                        modifier = Modifier.padding(vertical = Layout.bodyMargin)
//                    )
//                }

            if (viewState.showAlarmGroupEmpty != null) {
                Crossfade(
                    targetState = viewState.showAlarmGroupEmpty,
                    modifier = Modifier.weight(1f),
                    label = ""
                ) { alarmGroupsEmpty ->
                    if (alarmGroupsEmpty) {
                        AlarmGroupEmpty(modifier = Modifier.fillMaxSize())
                    } else {
                        LazyColumn(
                            modifier = Modifier.fillMaxSize()
                        ) {
                            item {
                                Spacer(modifier = Modifier.height(16.dp))
                            }

                            item {
                                Text(text = stringResource(R.string.text_remind_me_to_record))
                            }

                            viewState.alarmGroups.forEachIndexed { index, alarmGroup ->
                                if (index == 0) {
                                    item {
                                        Spacer(modifier = Modifier.height(Layout.gutter))
                                    }
                                }

                                item {
                                    AlarmGroupItem(
                                        alarmGroup = alarmGroup,
                                        onClick = { clickedAlarmGroup ->
                                            navigator.navigate(
                                                AlarmEditorDestination(
                                                    editorMode = AlarmEditorMode.Edit,
                                                    alarmGroup = clickedAlarmGroup,
                                                )
                                            )
                                        },
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = Layout.gutter)
                                    )
                                }
                            }
                        }
                    }
                }
            }

        }
    }
}

@Composable
private fun AlarmGroupItem(
    alarmGroup: AlarmGroup,
    onClick: (AlarmGroup) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Surface(
            onClick = { onClick(alarmGroup) },
            color = AppTheme.Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .defShadow(),
            shape = RoundedCornerShape12Dp
        ) {
            Column(modifier = Modifier.padding(top = 10.dp, bottom = 2.dp)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 12.dp)
                ) {
                    val alarmIconResId = alarmGroup.type.iconResId

                    Image(
                        painter = painterResource(id = alarmIconResId),
                        contentDescription = null,
                        modifier = Modifier.size(30.dp)
                    )


                    BlankSpacer(width = 5.dp)

                    Text(
                        text = stringResource(id = alarmGroup.type.typeStringId),
                        style = MaterialTheme.typography.titleSmall
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    val alarmTime =
                        if (alarmGroup.hour != null && alarmGroup.minute != null)
                            LocalTime(
                                alarmGroup.hour,
                                alarmGroup.minute
                            )
                        else null

                    Text(
                        text = alarmTime?.isoFormat() ?: "",
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                BlankSpacer(height = 4.dp)

                AlarmEditorDayOfWeekMultiPicker(
                    selections = alarmGroup.dayOfWeek.toSet(),
                    onSelect = { _, _ ->
                        onClick(alarmGroup)
                    },
                    modifier = Modifier.fillMaxWidth()
                )

            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AlarmGroupsAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_alarm)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
    )
}

@Composable
private fun AlarmGroupsFab(
    onClick: () -> Unit,
    painter: Painter,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    size: Dp = 72.dp,
) {
    Box(modifier = modifier) {
        Image(
            painter = painter,
            contentDescription = contentDescription,
            modifier = Modifier
                .size(size)
                .noRippleClickable(onClick = onClick),
        )
    }
}

@Composable
private fun AlarmGroupsFabAdd(
    onClick: () -> Unit,
    isAlarmGroupEmpty: Boolean?,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.End) {
        val fabEndOffset = 2.dp

        if (isAlarmGroupEmpty == true) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxWidth()
            ) {
                Image(
                    painter = painterResource(id = R.drawable.bg_alarm_tips_bubble),
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(90.dp)
                        .padding(start = 24.dp, end = 36.dp)
                        .autoMirror(),
                    contentScale = ContentScale.FillBounds
                )
                Text(
                    text = stringResource(R.string.text_content_set_alarm_now_tips),
                    maxLines = 1,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge.copy(color = AppTheme.Color.White),
                    modifier = Modifier.padding(bottom = 14.dp)
                )
            }
        }

        Row {
            AlarmGroupsFab(
                onClick = onClick,
                painter = painterResource(id = R.drawable.img_fab_add_alarm),
                contentDescription = null,
                modifier = Modifier.padding(bottom = 40.dp)
            )
            BlankSpacer(width = fabEndOffset)
        }
    }
}

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@Composable
fun RemindToRecordChooseLay(
    onDismiss: () -> Unit,
    onChoose: (RemindToRecordAlarmType) -> Unit,
) {
    AddAlarmChooser(
        onDismiss = onDismiss,
        onChoose = onChoose,
    )
}

@Composable
private fun AddAlarmChooser(
    onDismiss: () -> Unit,
    onChoose: (RemindToRecordAlarmType) -> Unit,
) {
    Dialog(
        onDismissRequest = onDismiss
    ) {
        Surface(
            shape = RoundedCornerShape12Dp,
            color = AppTheme.Color.AlarmBackground
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = Layout.bodyMargin)
                    .padding(bottom = Layout.bodyMargin)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BlankSpacer(height = 10.dp)

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = null,
                        tint = AppTheme.Color.textSecondary.copy(.8f),
                        modifier = Modifier.clickable(onClick = onDismiss)
                    )
                }

                Text(
                    text = stringResource(id = R.string.text_remind_me_to_record),
                    style = MaterialTheme.typography.titleLarge
                )

                BlankSpacer(height = 16.dp)

                ChooseAlarmItem(
                    alarmType = RemindToRecordAlarmType.BloodPressure,
                    onChoose = onChoose
                )
                BlankSpacer(height = 12.dp)

                ChooseAlarmItem(
                    alarmType = RemindToRecordAlarmType.HeartRate,
                    onChoose = onChoose
                )

                BlankSpacer(height = 12.dp)

                ChooseAlarmItem(
                    alarmType = RemindToRecordAlarmType.BloodSugar,
                    onChoose = onChoose
                )

                BlankSpacer(height = 8.dp)
            }
        }
    }
}

@Composable
private fun ChooseAlarmItem(
    alarmType: RemindToRecordAlarmType,
    onChoose: (RemindToRecordAlarmType) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = { onChoose(alarmType) }, color = Color.White,
        shape = RoundedCornerShape12Dp,
        modifier = modifier.defShadow(),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .padding(horizontal = 12.dp, vertical = 16.dp)
                .fillMaxWidth()
        ) {
            BlankSpacer(width = 6.dp)
            Image(
                painter = painterResource(id = alarmType.iconResId),
                contentDescription = null,
                modifier = Modifier.size(36.dp)
            )
            BlankSpacer(width = 12.dp)
            Text(
                text = stringResource(id = alarmType.typeStringId),
                fontSize = 15.sp
            )
        }
    }
}

@Composable
private fun AlarmGroupEmpty(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        BlankSpacer(height = 28.dp)


        Image(
            painter = painterResource(id = R.drawable.img_alarm),
            contentDescription = null,
            modifier = Modifier
                .width(260.dp)
                .rotate(15f)
        )

        BlankSpacer(height = 36.dp)

        Text(
            text = stringResource(R.string.text_content_set_alarm_for_health_monitoring_tips),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 40.dp)
        )

        Spacer(modifier = Modifier.weight(1f))
    }
}

@Preview
@Composable
private fun AlarmGroupsPreview() {
    AlarmGroups(navigator = EmptyDestinationsNavigator)
}
