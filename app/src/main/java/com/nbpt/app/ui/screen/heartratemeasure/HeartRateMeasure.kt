package com.nbpt.app.ui.screen.heartratemeasure

import androidx.activity.compose.BackHandler
import androidx.camera.view.PreviewView
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.Favorite
import androidx.compose.material.icons.rounded.FlashlightOff
import androidx.compose.material.icons.rounded.FlashlightOn
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.example.compose.HrTheme
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionState
import com.google.accompanist.permissions.isGranted
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel

import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.heartrate.HeartRateMeasureState
import com.nbpt.app.biz.heartrate.HeartRateMonitor
import com.nbpt.app.biz.heartrate.HeartRateMonitorViewfinder

import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.destinations.HeartRateEditorDestination
import com.nbpt.app.ui.screen.heartrateeditor.HeartRateEditorMode
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@OptIn(ExperimentalPermissionsApi::class)
@Destination
@Composable
fun HeartRateMeasure(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val viewModel: HeartRateMeasureViewModel = koinViewModel()
    val heartRateMonitor: HeartRateMonitor = koinInject()

    val cameraPermissions = heartRateMonitor.checkCameraPermissions()



    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
        if (!cameraPermissions.status.isGranted) {
            cameraPermissions.launchPermissionRequest()
        }
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    viewModel.collectSideEffect {
        when (it) {
            is HeartRateMeasureSideEffect.MeasureFinish -> {
                val destination = HeartRateEditorDestination(
                    editorMode = HeartRateEditorMode.Add,
                    gender = it.genderNumber,
                    age = it.age,
                    heartRateBpm = it.bpm,
                )

                admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                    activity = context.findActivity(),
                    navAction = {
                        popBackStack()
                        navigate(destination)
                    },
                    adPlaceName = "done_hr"
                )
            }

            is HeartRateMeasureSideEffect.NavUp -> navigator.navigateUp()

            is HeartRateMeasureSideEffect.NavTo -> {
                navigator.popBackStack()
                navigator.navigate(it.destination)
            }
        }
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_STOP -> viewModel.onInterruptCamera()
            else -> {}
        }
    }

    val viewState by viewModel.collectAsState()

    HrTheme {
        HeartRateMeasure(
            navigator = navigator,
            viewModel = viewModel,
            viewState = viewState,
            cameraPermissions = cameraPermissions,
        )
    }

    BackHandler {
        viewModel.onBack(context.findActivity())
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
private fun HeartRateMeasure(
    navigator: DestinationsNavigator,
    viewModel: HeartRateMeasureViewModel,
    viewState: HeartRateMeasureViewState,
    cameraPermissions: PermissionState
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

//    if (viewState.showInterstitialAdLoadingDialog) {
//        AdLoadingDialog()
//    }

    Scaffold(
        topBar = {
            HeartRateMeasureAppBar(
                navUp = {
                    viewModel.onBack(context.findActivity())
                },
                useCameraFlash = viewState.useCameraFlash,
                onCameraFlashSwitch = viewModel::onFlashSwitch
            )
        },
        bottomBar = {
            ListAdaptiveRectangleAd(
                bannerAdPlace = BannerAdPlaceholder.HR_MEASURE,
                nativeAdPlace = NativeAdPlaceholder.HrMeasure,
                modifier = Modifier.navigationBarsPadding(),
            )
        },
        containerColor = AppTheme.Color.HrBackground
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 42.dp)

            HeartRateMeasureDashBoard(
                heartRateMeasureState = viewState.measureState,
                modifier = Modifier.bodyWidth()
            )

            BlankSpacer(height = 12.dp)

            val measureFinish: Boolean = viewState.measureState is HeartRateMeasureState.Finish

            HeartRateFakeBpmWave(
                viewState.measureState,
                modifier = Modifier
                    .height(44.dp)
                    .bodyWidth()
            )

            BlankSpacer(height = 14.dp)

            HeartRateMeasureStateText(
                heartRateMeasureState = viewState.measureState,
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 20.dp)
            )

            BlankSpacer(height = 20.dp)


            Box(
                modifier = Modifier.bodyWidth()
            ) {
                if (cameraPermissions.status.isGranted) {
                    HeartRateMonitorViewfinder(
                        onViewfinderCreate = { previewView ->
                            if (!measureFinish) {
                                viewModel.onHRMVFLayout(
                                    context = context,
                                    lifecycleOwner = lifecycleOwner,
                                    previewView = previewView
                                )
                            }
                        },
                        modifier = Modifier.size(64.dp, 108.dp)
                    )

                    LaunchedEffect(Unit) {
                        delay(250)
                        viewModel.onFlashSwitch(true)
                    }
                } else {
                    Box(
                        modifier = Modifier
                            .size(64.dp, 108.dp)
                            .background(
                                color = Color.Black,
                                shape = RoundedCornerShape(percent = 50)
                            )
                            .noRippleClickable(onClick = { cameraPermissions.launchPermissionRequest() })
                    )

                }
            }

            BlankSpacer(height = 16.dp)

            HeartRateMeasureTipsText(
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 24.dp)
            )

            BlankSpacer(height = 20.dp)

//            Text(
//                text = stringResource(R.string.text_content_hr_measure_turn_on_flashlight_tips),
//                style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.Red),
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(horizontal = 28.dp),
//                textAlign = TextAlign.Center
//            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeartRateMeasureAppBar(
    navUp: () -> Unit,
    useCameraFlash: Boolean,
    onCameraFlashSwitch: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_measure)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
        actions = {
            IconButton(onClick = { onCameraFlashSwitch(!useCameraFlash) }) {
                Icon(
                    imageVector = if (useCameraFlash) Icons.Rounded.FlashlightOff else Icons.Rounded.FlashlightOn,
                    contentDescription = "back",
                )
            }
        },
    )
}

@Composable
private fun HeartRateMeasureDashBoard(
    heartRateMeasureState: HeartRateMeasureState?,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {

        Surface(
            shape = CircleShape,
            color = Color.White,
            border = BorderStroke(10.dp, AppTheme.Color.HrPrimary.copy(.3f)),
            modifier = Modifier.size(210.dp),
            content = {}
        )

        val (progress, heartRateBpm) = when (heartRateMeasureState) {
            is HeartRateMeasureState.Finish -> {
                1f + HeartRateMonitor.HEART_RATE_PROGRESS_PER_STEP to (heartRateMeasureState.heartRateBpm
                    ?: 0)
            }

            is HeartRateMeasureState.Measuring -> {
                heartRateMeasureState.progress + HeartRateMonitor.HEART_RATE_PROGRESS_PER_STEP to (heartRateMeasureState.heartRateBpm
                    ?: 0)
            }

            null, HeartRateMeasureState.Stop, HeartRateMeasureState.TryingToMeasure -> 0f to 0
        }

        val animatedProgress = animateFloatAsState(
            targetValue = progress,
            animationSpec = tween(durationMillis = 1000, easing = LinearEasing)
        ).value

        // 添加数字动画
        val animatedHeartRateBpm = animateIntAsState(
            targetValue = heartRateBpm,
            animationSpec = tween(durationMillis = 1000, easing = LinearEasing)
        ).value

        val heartRateBpmText = if (heartRateBpm == 0) "00" else animatedHeartRateBpm.toString()

        CircularProgressIndicator(
            progress = { animatedProgress },
            modifier = Modifier.size(182.dp),
            color = AppTheme.Color.HrPrimary,
            strokeWidth = 9.dp,
            strokeCap = StrokeCap.Round,
        )

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = heartRateBpmText,
                style = MaterialTheme.typography.displayMedium.copy(
                    fontFamily = FontFamily.Monospace,
                    fontWeight = FontWeight.ExtraBold,
                    fontSize = 54.sp
                )
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Rounded.Favorite,
                    contentDescription = null,
                    tint = AppTheme.Color.Red,
                    modifier = Modifier.size(18.dp)
                )
                BlankSpacer(width = 1.dp)
                Text(
                    text = stringResource(R.string.text_bpm),
                    style = MaterialTheme.typography.labelMedium.copy(
                        fontSize = 22.sp
                    )
                )
            }
        }
    }
}

@Composable
private fun HeartRateFakeBpmWave(
    measureState: HeartRateMeasureState?,
//    isPlaying: Boolean,
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(
        spec = LottieCompositionSpec.Asset("hr_wave.json")
    )

    when (measureState) {
        is HeartRateMeasureState.Finish, is HeartRateMeasureState.Stop, null -> {
            Box(modifier = modifier)
        }

        is HeartRateMeasureState.Measuring -> {
            LottieAnimation(
                composition = composition,
                modifier = modifier,
                isPlaying = true,
                iterations = Int.MAX_VALUE,
            )
        }

        HeartRateMeasureState.TryingToMeasure -> {
            Box(modifier = modifier, contentAlignment = Alignment.Center) {
                LinearProgressIndicator(
                    color = AppTheme.Color.HrPrimary,
                    strokeCap = StrokeCap.Round
                )
            }
        }
    }
}

@Composable
private fun HeartRateMeasureStateText(
    heartRateMeasureState: HeartRateMeasureState?,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        val (title, content) = when (heartRateMeasureState) {
            is HeartRateMeasureState.Finish -> stringResource(R.string.text_finish) to ""

            is HeartRateMeasureState.Stop -> stringResource(R.string.text_stop) to ""

            is HeartRateMeasureState.TryingToMeasure ->
                stringResource(R.string.text_measure) to stringResource(R.string.text_place_your_finger_on_camera)

            is HeartRateMeasureState.Measuring -> {
                val animatedProgress = animateFloatAsState(
                    targetValue = heartRateMeasureState.progress + HeartRateMonitor.HEART_RATE_PROGRESS_PER_STEP,
                    animationSpec = tween(durationMillis = 1000, easing = LinearEasing)
                ).value

                stringResource(
                    R.string.text_measuring,
                    (animatedProgress * 100).toInt()
                ) to stringResource(R.string.text_measuring_your_heart_rate_keep_it_steady)
            }

            null -> "" to ""
        }

        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.ExtraBold)
        )
        BlankSpacer(height = 2.dp)

        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            modifier = Modifier,
        )
    }
}

@Composable
private fun HeartRateMeasureTipsText(
    modifier: Modifier = Modifier
) {
    Text(
        text = buildAnnotatedString {
            withStyle(
                MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold)
                    .toSpanStyle()
            ) {
                append(stringResource(R.string.text_hr_measure_tips_pt1))
            }
            withStyle(
                MaterialTheme.typography.bodyMedium.toSpanStyle()
                    .copy(fontWeight = FontWeight.ExtraBold, color = AppTheme.Color.Red)
            ) {
                append(stringResource(R.string.text_hr_measure_tips_pt2))
            }
            withStyle(
                MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.SemiBold)
                    .toSpanStyle()
            ) {
                append(stringResource(R.string.text_hr_measure_tips_pt3))
            }
        },
        modifier = modifier
    )
}

@Composable
private fun HeartRateMeasureBottomBar(
    modifier: Modifier = Modifier,
) {
//    NativeAd(
//        place = NativeAdPlace.HeartRateMeasure,
//        modifier = modifier
//    )
}

private fun Int?.formatBpmDisplay(): String {
    return if (this == null) {
        " 00"
    } else if (this >= 100) {
        this.toString()
    } else {
        " $this"
    }
}