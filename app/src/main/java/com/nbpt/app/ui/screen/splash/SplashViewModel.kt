package com.nbpt.app.ui.screen.splash

import android.app.Activity
import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.biz.admanager.appopen.AdmobAppOpenAdManager
import com.nbpt.app.biz.admanager.appopen.MaxAppOpenAdHelper
import com.nbpt.app.biz.admanager.appopen.TryToShowExecuteResult
import com.nbpt.app.biz.admanager.appopen.closeSplashEventFlow
import com.nbpt.app.biz.admanager.banner.AdmobBannerAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdLoadingStateEvent
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdShowEvent
import com.nbpt.app.biz.remoteconfig.RealRemoteConfig
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.biz.ump.UserMessagingPlatformManager
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import com.nbpt.app.configureMaxAdIfNeeded
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class SplashViewModel(
    savedStateHandle: SavedStateHandle,
    private val maxAppOpenAdHelper: MaxAppOpenAdHelper,
    private val admobAppOpenAdManager: AdmobAppOpenAdManager,
    private val userMessagingPlatformManager: UserMessagingPlatformManager,
    private val remoteConfig: RealRemoteConfig,
    private val maxInterstitialAdHelper: MaxInterstitialAdManager,
    private val admobInterstitialAdManager: AdmobInterstitialAdManager,
    private val admobBannerAdManager: AdmobBannerAdManager,
) : ViewModel(), ContainerHost<SplashViewState, SplashSideEffect> {

    override val container: Container<SplashViewState, SplashSideEffect> =
        container(SplashViewState())

    val args = savedStateHandle.navArgs<SplashNavArgs>()

    private var hasRequestConsentAndConfigureAds = false

    fun onNavUp() = intent {
        val afterLaunchDestinationRoute = args.afterLaunchDestinationRoute

        if (afterLaunchDestinationRoute.isNotEmpty()) {
            postSideEffect(SplashSideEffect.PopupSelfAndNavAction(route = afterLaunchDestinationRoute))
        } else if (args.isColdLaunch) {
            postSideEffect(SplashSideEffect.PopupSelfAndNavToNextScreen)
        } else {
            postSideEffect(SplashSideEffect.NavUp)
        }
    }

    private fun onShowAdmobAppOpenAd() = intent {
        postSideEffect(SplashSideEffect.ShowAdmobAppOpenAd)
    }

    private fun registerAdFlow(
        useLegacyAdConfig: Boolean,
        scope: CoroutineScope
    ) {
        if (useLegacyAdConfig) {
            launchedEffectMaxAppOpenAdHandler(scope)
        } else {
            configureAdmobAppOpenAd()
        }
    }

    private fun onLoadAds(
        useLegacyAdConfig: Boolean,
        context: Context,
    ) {
        viewModelScope.launch(Dispatchers.Main.immediate) {
            if (useLegacyAdConfig) {
                maxAppOpenAdHelper.tryToLoadAd()
                maxInterstitialAdHelper.tryToLoadAd()
            } else {
                if (GlobalNavigator.previousDestination() is HomeDestination) {
                    admobBannerAdManager.destroyAll()
                }
                admobInterstitialAdManager.tryToLoadAd(context.findActivity())

                admobAppOpenAdManager.tryToShowAd(context.findActivity())
            }
        }
    }

    private fun configureAdmobAppOpenAd() {
        admobAppOpenAdManager.adLoadingStateEventFlow.onEach {
            when (it) {
                AdmobAppOpenAdManager.AdLoadingStateEvent.FailedToLoad -> {
                    delay(1000)
                    onNavUp()
                }

                AdmobAppOpenAdManager.AdLoadingStateEvent.TimeOut -> {
                    onNavUp()
                }

                AdmobAppOpenAdManager.AdLoadingStateEvent.Loaded -> {
                    onShowAdmobAppOpenAd()
                }
            }
        }.launchIn(viewModelScope)


        admobAppOpenAdManager.adShowStateEventFlow.onEach {
            when (it) {
                AdmobAppOpenAdManager.AdShowStateEvent.FailedToShow,
                AdmobAppOpenAdManager.AdShowStateEvent.Finish -> {
                    onNavUp()
                }

                AdmobAppOpenAdManager.AdShowStateEvent.SkipToShow -> {
                    delay(1000)
                    onNavUp()
                }

                AdmobAppOpenAdManager.AdShowStateEvent.Showing -> {
                    intent {
                        delay(500)
                        reduce { state.copy(appOpenAdShowing = true) }
                    }
                }

            }
        }.launchIn(viewModelScope)
    }

    fun requestConsentAndConfigureAds(
        activity: Activity,
        scope: CoroutineScope,
        onConfigure: () -> Unit
    ) {
        if (hasRequestConsentAndConfigureAds) return
        debugLog(tag = "SplashViewModel") { "requestConsentAndConfigureAds" }

        userMessagingPlatformManager.requestConsentInfoUpdate(
            activity = activity,
            onUpdate = {
                debugLog(tag = "SplashViewModel") { "Consent info update successful" }
                val useLegacyAdConfig = remoteConfig.useLegacyAd

                registerAdFlow(useLegacyAdConfig, scope)
                onLoadAds(useLegacyAdConfig, activity)

                onConfigure()

                GlobalScope.launch {
                    configureMaxAdIfNeeded(activity.applicationContext)
                }
            })

        hasRequestConsentAndConfigureAds = true
    }

    private val loadingSeconds = MutableStateFlow(0f)
    fun configureLoadingSeconds(scope: CoroutineScope) {
        scope.launch {
            repeat(Int.MAX_VALUE) { _ ->
                delay(500)
                loadingSeconds.update { it + 0.5f }
            }
        }
    }

    private fun registerMaxAppOpenAdHandler(coroutineScope: CoroutineScope) {
        val timeoutJob = coroutineScope.launch {
            maxAppOpenAdHelper.instantLoadTimeoutDelay()
            maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.send(null)
            onNavUp()
        }

        maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.onEach {
            when (it) {
                TryToShowExecuteResult.DoNotShow,
                TryToShowExecuteResult.Error -> {
                    delay(1500)
                    onNavUp()
                }

                TryToShowExecuteResult.ShowFinish -> {
                    onNavUp()
                }

                TryToShowExecuteResult.ReadyToShow -> {
                    timeoutJob.cancel()
                }

                else -> {}
            }
        }.launchIn(coroutineScope)
    }

    fun launchedEffectMaxAppOpenAdHandler(coroutineScope: CoroutineScope) {
        registerMaxAppOpenAdHandler(coroutineScope)
        closeSplashEventFlow.onEach {
            maxAppOpenAdHelper.tryToShowAd()
        }.launchIn(coroutineScope)
    }

    override fun onCleared() {
        maxAppOpenAdHelper.tryToShowExecuteResultEventFlow.send(null)
        super.onCleared()
    }
}
