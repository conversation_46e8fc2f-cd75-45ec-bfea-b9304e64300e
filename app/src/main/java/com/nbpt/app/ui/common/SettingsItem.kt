package com.nbpt.app.ui.common

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow

@Composable
fun SettingsItem(
    onItemClick: () -> Unit,
    iconPainter: Painter,
    title: String,
    modifier: Modifier = Modifier,
    iconTint: Color? = null
) {
    Surface(
        onClick = onItemClick,
        modifier = modifier.defShadow(),
        shape = RoundedCornerShape12Dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Layout.bodyMargin),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = iconPainter,
                contentDescription = null,
                modifier = Modifier.size(34.dp),
                colorFilter = if (iconTint != null) ColorFilter.tint(iconTint) else null
            )

            BlankSpacer(width = Layout.gutter)

            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f)
            )
        }
    }
}
