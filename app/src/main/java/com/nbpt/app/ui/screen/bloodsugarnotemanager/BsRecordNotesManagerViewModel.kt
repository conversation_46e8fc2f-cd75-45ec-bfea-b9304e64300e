package com.nbpt.app.ui.screen.bloodsugarnotemanager

import androidx.lifecycle.ViewModel
import com.nbpt.app.data.db.dao.BsRecordsNoteDao
import com.nbpt.app.data.db.model.BsRecordsNoteEntity
import com.nbpt.app.data.pojo.BsRecordsNote
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BsRecordNotesManagerViewModel(
    private val noteDao: BsRecordsNoteDao
) : ViewModel(),
    ContainerHost<BsRecordNotesManagerViewState, BsRecordNotesManagerSideEffect> {

    override val container: Container<BsRecordNotesManagerViewState, BsRecordNotesManagerSideEffect> =
        container(BsRecordNotesManagerViewState.Empty)

    init {
        intent {
            val notes = noteDao.fetchAllNotesFlow().first()

            reduce { state.copy(notes = notes) }
        }
    }

    private val pendingAddNotes = mutableListOf<BsRecordsNoteEntity>()
    val pendingDeleteNotes = mutableListOf<BsRecordsNoteEntity>()

    fun onAdd(
        noteText: String
    ) = intent {
        val noteSingle = BsRecordsNote(content = noteText.trim()).toDbEntity()

        val newNotes = listOf(noteSingle) + state.notes

        pendingAddNotes.add(noteSingle)

        reduce { state.copy(notes = newNotes) }

        onDismissAddNoteDialog()
    }

    fun onDelete(note: BsRecordsNoteEntity) = intent {
        val newNotes = state.notes.toMutableList().apply {
            remove(note)
        }

        pendingDeleteNotes.add(note)
        pendingAddNotes.remove(note)

        reduce { state.copy(notes = newNotes) }
    }

    fun onSave() = intent {
        reduce { state.copy(notesSaving = true) }

        if (pendingDeleteNotes.isNotEmpty()) {
            noteDao.delete(pendingDeleteNotes)
        }

        if (pendingAddNotes.isNotEmpty()) {
            noteDao.add(pendingAddNotes)
        }

        reduce { state.copy(notesSaving = false) }

        postSideEffect(BsRecordNotesManagerSideEffect.NavUp)
    }

    fun onShowAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = true) }
    }

    fun onDismissAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = false) }
    }
}
