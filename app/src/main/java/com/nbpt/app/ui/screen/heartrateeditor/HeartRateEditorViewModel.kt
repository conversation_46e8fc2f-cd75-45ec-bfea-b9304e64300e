package com.nbpt.app.ui.screen.heartrateeditor

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.data.db.dao.HrRecordDao
import com.nbpt.app.data.db.model.HrRecordEntity
import com.nbpt.app.data.db.model.HrRecordsNoteEntity
import com.nbpt.app.data.healtharticles.HealthArticleType
import com.nbpt.app.data.healtharticles.HealthArticles
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.util.UUID

class HeartRateEditorViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val hrRecordDao: HrRecordDao,
    private val fixedNotificationHelper: FixedNotificationHelper
) : ViewModel(), ContainerHost<HeartRateEditorViewState, HeartRateEditorSideEffect> {

    override val container: Container<HeartRateEditorViewState, HeartRateEditorSideEffect> =
        container(HeartRateEditorViewState.Empty)

    val navArgs = savedStateHandle.navArgs<HeartRateEditorNavArgs>()

    init {
        intent {

            val hrArticles = HealthArticles.fetch()[HealthArticleType.HR]

            reduce {
                state.copy(
                    editorMode = navArgs.editorMode,
                    heartRateBpm = navArgs.heartRateBpm,
                    instant = navArgs.instant,
                    gender = navArgs.gender.let { Gender.numberOf(it) },
                    age = navArgs.age,
                    notesSelection = navArgs.notesSelection ?: emptyList(),
                    hrArticles = hrArticles ?: emptyList()
                )
            }
        }
    }

    private var recordUUID: String = navArgs.recordUUID ?: UUID.randomUUID().toString()

    fun onGenderChange(gender: Gender) = intent {
        reduce { state.copy(gender = gender) }
    }

    fun onAgeChange(age: Int) = intent {
        reduce { state.copy(age = age) }
    }

    fun onShowGenderDialog() = intent {
        reduce { state.copy(showGenderDialog = true) }
    }

    fun onDismissGenderDialog() = intent {
        reduce { state.copy(showGenderDialog = false) }
    }

    fun onShowAgeDialog() = intent {
        reduce { state.copy(showAgeDialog = true) }
    }

    fun onDismissAgeDialog() = intent {
        reduce { state.copy(showAgeDialog = false) }
    }

    fun onShowHeartRateDeleteDialog() = intent {
        reduce { state.copy(showHeartRateDeleteDialog = true) }
    }

    fun onDismissHeartRateDeleteDialog() = intent {
        reduce { state.copy(showHeartRateDeleteDialog = false) }
    }

    fun onShowHeartRateStatusDialog() = intent {
        reduce { state.copy(showHeartRateStatusDialog = true) }
    }

    fun onDismissHeartRateStatusDialog() = intent {
        reduce { state.copy(showHeartRateStatusDialog = false) }
    }

    fun onSave() = intent {
        hrRecordDao.upsert(
            HrRecordEntity(
                uuid = recordUUID,
                instant = state.instant ?: nowInstant(),
                gender = (state.gender ?: Gender.Others).number,
                age = state.age,
                heartRateBpm = state.heartRateBpm,
                notes = state.notesSelection
            )
        )

        postSideEffect(HeartRateEditorSideEffect.SaveAndNavUp)
        fixedNotificationHelper.updateNoti()
    }

    fun onDelete() = intent {
        hrRecordDao.delete(recordUUID)

        postSideEffect(HeartRateEditorSideEffect.NavUp)
        fixedNotificationHelper.updateNoti()
    }

    fun onNotesDelete(deletedSelection: Set<String>) = intent {
        reduce { state.copy(notesSelection = state.notesSelection - deletedSelection) }
    }

    suspend fun deletedSelection(deletedNotes: List<HrRecordsNoteEntity>): Set<String> {
        val state = container.stateFlow.first()

        val deletedSelection = mutableSetOf<String>()

        deletedNotes.forEach { hrRecordsNoteEntity ->
            if (hrRecordsNoteEntity.content in state.notesSelection) {
                deletedSelection.add(hrRecordsNoteEntity.content)
            }
        }

        return deletedSelection
    }

    fun onNotesSelectionUpdate(notesSelection: List<String>) = intent {
        reduce { state.copy(notesSelection = notesSelection) }
    }

    fun onBack(force: Boolean = false) = intent {
        if (force) {
            postSideEffect(HeartRateEditorSideEffect.NavUp)
            return@intent
        }

        if (navArgs.editorMode == HeartRateEditorMode.Add) {
            reduce { state.copy(showExitEditorTipsDialog = true) }
        } else {
            postSideEffect(HeartRateEditorSideEffect.NavUp)
        }
    }

    fun onDismissExitEditorTipsDialog() = intent {
        reduce { state.copy(showExitEditorTipsDialog = false) }
    }

}
