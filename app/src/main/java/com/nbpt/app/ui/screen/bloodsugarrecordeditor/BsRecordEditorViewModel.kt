package com.nbpt.app.ui.screen.bloodsugarrecordeditor

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.common.calculate.mgDlToMmol
import com.nbpt.app.common.calculate.mmolLToMgDL
import com.nbpt.app.common.datetime.toInstant
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BsRecordDao
import com.nbpt.app.data.db.model.BsRecordEntity
import com.nbpt.app.data.db.model.BsRecordsNoteEntity
import com.nbpt.app.data.healtharticles.HealthArticleType
import com.nbpt.app.data.healtharticles.HealthArticles
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import kotlinx.datetime.LocalDateTime
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.util.UUID

class BsRecordEditorViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val bsRecordDao: BsRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore,
    private val fixedNotificationHelper: FixedNotificationHelper
) : ViewModel(), ContainerHost<BsRecordEditorViewState, BsRecordEditorSideEffect> {

    override val container: Container<BsRecordEditorViewState, BsRecordEditorSideEffect> =
        container(BsRecordEditorViewState())

    val navArgs = savedStateHandle.navArgs<BsRecordEditorNavArgs>()

    init {
        handleArgs()
    }

    private var recordUUID: String = navArgs.recordUUID ?: UUID.randomUUID().toString()

    private fun handleArgs() = intent {
        val bsArticle = HealthArticles.fetch()[HealthArticleType.BS]

        reduce {
            state.copy(
                stateHandleFinished = true,
                editorMode = navArgs.editorMode,
                dateTime = navArgs.instant.toLocalDatetime(),
                mmolL = navArgs.mmolL,
                mgDl = mmolLToMgDL(navArgs.mmolL),
                bsStatus = BsStatus.fromMmolL(navArgs.mmolL),
                bsUnit = userBehaviorDataStore.bsUnit,
                bsRecordState = navArgs.state,
                notesSelection = navArgs.notesSelection ?: emptyList(),
                bsArticle = bsArticle?.getOrNull(1)
            )
        }
    }

    fun onDismissDeleteRecordDialog() = intent {
        reduce { state.copy(showDeleteRecordDialog = false) }
    }

    fun onShowDeleteRecordDialog() = intent {
        reduce { state.copy(showDeleteRecordDialog = true) }
    }

    fun onDismissBpTypeDialog() = intent {
        reduce { state.copy(showBsTypeDialog = false) }
    }

    fun onShowBpTypeDialog() = intent {
        reduce { state.copy(showBsTypeDialog = true) }
    }

    fun onDateTimeChange(dateTime: LocalDateTime) = intent {
        reduce { state.copy(dateTime = dateTime) }
    }

    fun onMmolLChange(mmolL: Float?) = intent {
        val bsStatus = mmolL?.let { BsStatus.fromMmolL(it) }
        val mgDl = mmolL?.let { mmolLToMgDL(it) }
        reduce { state.copy(bsStatus = bsStatus, mmolL = mmolL, mgDl = mgDl) }
    }

    fun onMgDlChange(mgDl: Float?) = intent {
        val bsStatus = mgDl?.let { BsStatus.fromMgDl(it) }
        val mmolL = mgDl?.let { mgDlToMmol(it) }
        reduce { state.copy(bsStatus = bsStatus, mmolL = mmolL, mgDl = mgDl) }
    }

    fun onBsUnitChange(bsUnit: BsUnit) = intent {
        userBehaviorDataStore.bsUnit = bsUnit

        reduce { state.copy(bsUnit = bsUnit) }
    }

    fun onSave() = intent {
        val mmolL = state.mmolL ?: return@intent
        val instant = state.dateTime?.toInstant() ?: return@intent

        bsRecordDao.upsert(
            BsRecordEntity(
                uuid = recordUUID,
                instant = instant,
                mmolL = mmolL,
                state = state.bsRecordState,
                notes = state.notesSelection
            )
        )

        postSideEffect(BsRecordEditorSideEffect.SaveAndNavUp)
        fixedNotificationHelper.updateNoti()
    }

    fun onDelete() = intent {
        bsRecordDao.delete(uuid = recordUUID)
        onDismissDeleteRecordDialog()
        postSideEffect(BsRecordEditorSideEffect.NavUp)
        fixedNotificationHelper.updateNoti()
    }

    fun onDismissExitEditorTipsDialog() = intent {
        reduce { state.copy(showExitEditorTipsDialog = false) }
    }

    fun onBack(force: Boolean = false) = intent {
        if (force) {
            postSideEffect(BsRecordEditorSideEffect.NavUp)
            return@intent
        }

        if (navArgs.editorMode == BsRecordEditorMode.Add) {
            reduce { state.copy(showExitEditorTipsDialog = true) }
        } else {
            postSideEffect(BsRecordEditorSideEffect.NavUp)
        }
    }

    fun onNotesDelete(deletedSelection: Set<String>) = intent {
        reduce { state.copy(notesSelection = state.notesSelection - deletedSelection) }
    }

    suspend fun deletedSelection(deletedNotes: List<BsRecordsNoteEntity>): Set<String> {
        val state = container.stateFlow.first()

        val deletedSelection = mutableSetOf<String>()

        deletedNotes.forEach { bsRecordsNoteEntity ->
            if (bsRecordsNoteEntity.content in state.notesSelection) {
                deletedSelection.add(bsRecordsNoteEntity.content)
            }
        }

        return deletedSelection
    }

    fun onNotesSelectionUpdate(notesSelection: List<String>) = intent {
        reduce { state.copy(notesSelection = notesSelection) }
    }
}