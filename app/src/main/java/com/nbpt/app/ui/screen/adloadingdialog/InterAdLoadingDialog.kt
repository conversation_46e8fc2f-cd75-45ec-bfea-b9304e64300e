package com.nbpt.app.ui.screen.adloadingdialog

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdLoadingStateEvent
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdShowEvent
import com.nbpt.app.ui.common.CircularProgressLoadingDialog
import com.nbpt.app.ui.screen.destinations.InterAdLoadingDialogDestination
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.ui.common.AdLoadingDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState

val interAdLoadingAfterEvent = MutableStateFlow<Function0<Unit>?>(null)

@Destination(
    style = AdaptiveHeightDialog::class,
)
@Composable
fun InterAdLoadingDialog(
    navigator: DestinationsNavigator,
    whenShowingExecuteAction: Boolean = true
) {
    val viewModel: InterAdLoadingDialogViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    BackHandler {

    }

    val afterAction = suspend {
        debugLog(tag = "MaxInterstitialAdHelper") { "finishAction" }
        val afterAction = interAdLoadingAfterEvent.first()
        interAdLoadingAfterEvent.update { null }

        if (GlobalNavigator.currentDestination() is InterAdLoadingDialogDestination) {
            navigator.navigateUp()
        }
        debugLog(tag = "MaxInterstitialAdHelper") { "afterAction: $afterAction" }
        afterAction?.invoke()
        Unit
    }

    if (viewState.displayLoading) {
        debugLog(tag = "MaxInterstitialAdHelper") { "displayDialog ture" }
        AdLoadingDialog()
    } else {
        debugLog(tag = "MaxInterstitialAdHelper") { "displayDialog false" }
    }

    LaunchedEffect(Unit) {
        interstitialAdManager.adLoadingStateEvent.onEach {
            when (it) {
                MaxInterstitialAdLoadingStateEvent.FailedToLoad -> afterAction()
                MaxInterstitialAdLoadingStateEvent.Loaded -> interstitialAdManager.tryToShowAd()
            }
        }.launchIn(this)

        interstitialAdManager.interstitialAdEventFlow.onEach {
            debugLog(tag = "MaxInterstitialAdHelper") { "interstitialAdEventFlow.onEach: $it" }

            when (it) {
                MaxInterstitialAdShowEvent.Skip -> afterAction()
                MaxInterstitialAdShowEvent.Showing -> if (whenShowingExecuteAction) {
                    afterAction()
                } else {
                    viewModel.hideDialog()
                    debugLog(tag = "MaxInterstitialAdHelper") { "displayDialog = false" }
                }

                MaxInterstitialAdShowEvent.Invalid -> {}
                MaxInterstitialAdShowEvent.Hidden -> if (!whenShowingExecuteAction) afterAction()
                MaxInterstitialAdShowEvent.TryToShowing -> {}
            }
        }.launchIn(this)
    }

    // timeout stuff
    LaunchedEffect(Unit) {
        delay(interstitialAdManager.adLoadingTimeoutDuration())

        afterAction()
    }
}