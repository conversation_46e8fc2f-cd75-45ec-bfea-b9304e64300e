package com.nbpt.app.ui.screen.bp

import androidx.lifecycle.ViewModel
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
//import com.nbpt.app.biz.admanager.rewardedInterstitial.RewardedInterstitialAdManager
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.healtharticles.HealthArticleType
import com.nbpt.app.data.healtharticles.HealthArticles
import kotlinx.coroutines.flow.first
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class TrackerViewModel(
    private val bpRecordDao: BpRecordDao,
//    private val interstitialAdManager: InterstitialAdManager,
//    private val rewardedInterstitialAdManager: RewardedInterstitialAdManager,
    private val userBehaviorDataStore: UserBehaviorDataStore,
) : ViewModel(), ContainerHost<TrackerViewState, TrackerSideEffect>, KoinComponent {

    override val container: Container<TrackerViewState, TrackerSideEffect> =
        container(TrackerViewState.Empty)

    init {
        onRefresh()
    }

    fun onRefresh() = intent {
        val records = bpRecordDao.fetchAllRecordsFlow().first()
        val isUnlockBpRecordStat = userBehaviorDataStore.isUnlockBpRecordStat

        val maxSys = records.maxByOrNull { it.systolic }?.systolic
        val maxDia = records.maxByOrNull { it.diastolic }?.diastolic
        val maxPulse = records.maxByOrNull { it.pulse }?.pulse

        val minSys = records.minByOrNull { it.systolic }?.systolic
        val minDia = records.minByOrNull { it.diastolic }?.diastolic
        val minPulse = records.minByOrNull { it.pulse }?.pulse

        val avgSys = records.map { it.systolic }.takeIf { it.isNotEmpty() }?.average()?.toInt()
        val avgDia = records.map { it.diastolic }.takeIf { it.isNotEmpty() }?.average()?.toInt()
        val avgPulse = records.map { it.pulse }.takeIf { it.isNotEmpty() }?.average()?.toInt()

        val latestRecord = records.maxByOrNull { it.instant.epochSeconds }
        val latestSys = latestRecord?.systolic
        val latestDia = latestRecord?.diastolic
        val latestPulse = latestRecord?.pulse

//        val showAddRecordGuideLay = !userBehaviorDataStore.hasShowAddRecordGuideLay()

        val bpArticles = HealthArticles.fetch()[HealthArticleType.BP]

        reduce {
            state.copy(
                maxStatData = TrackerRoughStatData(maxSys, maxDia, maxPulse),
                minStatData = TrackerRoughStatData(minSys, minDia, minPulse),
                avgStatData = TrackerRoughStatData(avgSys, avgDia, avgPulse),
                latestStatData = TrackerRoughStatData(latestSys, latestDia, latestPulse),
                showAddRecordGuideLay = false,
                bpRecords = records,
                isUnlockBpRecordStat = isUnlockBpRecordStat,
                bpArticles = bpArticles ?: emptyList()
            )
        }
    }

    fun onStatModeChange(statDataMode: TrackerRoughStatModeAdt) = intent {
        reduce { state.copy(statDataMode = statDataMode) }
    }

//    fun onUnlockBpRecordStat(
//        activity: Activity,
//        adPlaceName: String,
//    ) = intent {
//        showRewardedAdContainer = activity
//
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        rewardedInterstitialAdManager.tryToShowAd(activity, adPlaceName)
//
//        val earnedRewardSuccessful =
//            rewardedInterstitialAdManager.adEarnedRewardEventFlow.firstOrNull()
//
//        showRewardedAdContainer = null
//        reduce { state.copy(showAdLoadingDialog = false) }
//
//        if (earnedRewardSuccessful == true) {
//            userBehaviorDataStore.isUnlockBpRecordStat = true
//            reduce { state.copy(isUnlockBpRecordStat = true) }
//        }
//    }
//
//    @SuppressLint("StaticFieldLeak")
//    private var showRewardedAdContainer: Activity? = null
//
//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//
//    private var showInterAdAfterDestination: Direction? = null
//
//    @Volatile
//    private var hasInterAdShowing = false
//
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showAdLoadingDialog = false) }
//
//                    showInterAdAfterDestination?.let { destination ->
//                        showInterAdContainer = null
//                        showInterAdAfterDestination = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(TrackerSideEffect.NavTo(destination))
//                    }
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        if (!hasInterAdShowing) {
//                            interstitialAdManager.tryToShowAd(containerActivity, null)
//                        }
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showAdLoadingDialog = false) }
//
//                    showInterAdAfterDestination?.let { destination ->
//                        showInterAdContainer = null
//                        showInterAdAfterDestination = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(TrackerSideEffect.NavTo(destination))
//                    }
//
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {
//                    if (showInterAdContainer != null) {
//
//                        // if current page has multiple inter ad manager or page has multiple inter ad trigger then should be use `hasInterAdShowing`
//                        hasInterAdShowing = true
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//    }
//
//    fun registerInterRewardedAdEventFlow(lifecycleScope: CoroutineScope) {
//        rewardedInterstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                RewardedInterstitialAdManager.AdLoadingStateEvent.FailedToLoad,
//                RewardedInterstitialAdManager.AdLoadingStateEvent.TimeOut -> intent {
//                    reduce { state.copy(showAdLoadingDialog = false) }
//                    showRewardedAdContainer = null
//                    rewardedInterstitialAdManager.adEarnedRewardEventFlow.send(false)
//                }
//
//                RewardedInterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showRewardedAdContainer?.let { containerActivity ->
//                        rewardedInterstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//    }
}
