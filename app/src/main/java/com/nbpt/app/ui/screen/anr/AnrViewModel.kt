package com.nbpt.app.ui.screen.anr

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.healtharticles.HealthArticle
import com.nbpt.app.data.healtharticles.HealthArticles
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class AnrViewModel(
    savedStateHandle: SavedStateHandle,
    private val bpRecordDao: BpRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore,
) : ViewModel(), ContainerHost<AnrViewState, AnrSideEffect> {

    override val container: Container<AnrViewState, AnrSideEffect> =
        container(AnrViewState.Empty)

//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//
//    @SuppressLint("StaticFieldLeak")
//    private var showRewardedAdContainer: Activity? = null
//
//    private var showInterAdAfterDestination: Direction? = null

    val navArgs: AnrNavArgs = savedStateHandle.navArgs()

    init {
        intent {
            reduce { state.copy(anrMode = navArgs.anrMode) }

            doReduceRandomRecommendedArticles()
            doReduceBpRecordStat()
        }
    }

    private fun doReduceRandomRecommendedArticles() = intent {
        val randomPickupArticles = mutableListOf<HealthArticle>()

        val articleMap = HealthArticles.fetch()
        articleMap.forEach { (_, articles) ->
            randomPickupArticles.add(articles.random())
        }

        reduce { state.copy(recommendedArticles = randomPickupArticles) }
    }

    private fun doReduceBpRecordStat() = intent {
        if (navArgs.anrMode is AnrMode.BP) {
            val bpRecords = bpRecordDao.fetchAllRecordsFlow().first()
            val isUnlockBpRecordStat = userBehaviorDataStore.isUnlockBpRecordStat
            reduce {
                state.copy(
                    bpRecords = bpRecords,
                    isUnlockBpRecordStat = isUnlockBpRecordStat
                )
            }
        }
    }

//    fun onUnlockBpRecordStat(
//        activity: Activity,
//        adPlaceName: String,
//    ) = intent {
//        showRewardedAdContainer = activity
//
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        rewardedInterstitialAdManager.tryToShowAd(activity, adPlaceName)
//
//        val earnedRewardSuccessful =
//            rewardedInterstitialAdManager.adEarnedRewardEventFlow.firstOrNull()
//
//        showRewardedAdContainer = null
//        reduce { state.copy(showAdLoadingDialog = false) }
//
//        if (earnedRewardSuccessful == true) {
//            userBehaviorDataStore.isUnlockBpRecordStat = true
//            reduce { state.copy(isUnlockBpRecordStat = true) }
//        }
//    }

//    fun onUnlockAnalysisInfo(
//        activity: Activity,
//        adPlaceName: String,
//    ) = intent {
//        showRewardedAdContainer = activity
//
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        rewardedInterstitialAdManager.tryToShowAd(activity, adPlaceName)
//
//        val earnedRewardSuccessful =
//            rewardedInterstitialAdManager.adEarnedRewardEventFlow.firstOrNull()
//
//        showRewardedAdContainer = null
//        reduce { state.copy(showAdLoadingDialog = false) }
//
//        if (earnedRewardSuccessful == true) {
//            reduce { state.copy(isUnlockAnalysis = true) }
//        }
//    }

//    sealed interface ExecuteInterAd {
//        object Navigate : ExecuteInterAd
//        object OnBack : ExecuteInterAd
//    }
//
//    private val executeInterAdFlow = MutableStateFlow<ExecuteInterAd?>(null)
//
//    fun onBack(
//        activity: Activity,
//    ) = intent {
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.OnBack }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = null
//
//        val adPlaceName = when (state.anrMode) {
//            is AnrMode.BP -> "back_result_bp"
//            is AnrMode.HR -> "back_result_hr"
//            null -> "back_result"
//        }
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.Navigate }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    handleInterAdFinishToDo()
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    handleInterAdFinishToDo()
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
//
//    private fun handleInterAdFinishToDo() = intent {
//        reduce { state.copy(showAdLoadingDialog = false) }
//        showInterAdContainer = null
//
//        when (executeInterAdFlow.first()) {
//            ExecuteInterAd.OnBack -> postSideEffect(AnrSideEffect.NavUp)
//            ExecuteInterAd.Navigate -> intent {
//                showInterAdAfterDestination?.let { destination ->
//                    postSideEffect(AnrSideEffect.NavTo(destination))
//                }
//                showInterAdAfterDestination = null
//            }
//
//            null -> {}
//        }
//
//        executeInterAdFlow.update { null }
//    }
//
//    fun registerInterRewardedAdEventFlow(lifecycleScope: CoroutineScope) {
//        rewardedInterstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                RewardedInterstitialAdManager.AdLoadingStateEvent.FailedToLoad,
//                RewardedInterstitialAdManager.AdLoadingStateEvent.TimeOut -> intent {
//                    reduce { state.copy(showAdLoadingDialog = false) }
//                    showRewardedAdContainer = null
//                    rewardedInterstitialAdManager.adEarnedRewardEventFlow.send(false)
//                }
//
//                RewardedInterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showRewardedAdContainer?.let { containerActivity ->
//                        rewardedInterstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//    }
}
