package com.nbpt.app.ui.screen.bloodsugarhistory

import androidx.lifecycle.ViewModel
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BsRecordDao
import com.nbpt.app.data.db.dao.mockData
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BsHistoryViewModel(
    private val bsRecordDao: BsRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore,
) : ViewModel(), ContainerHost<BsHistoryViewState, Unit> {

    override val container: Container<BsHistoryViewState, Unit> = container(BsHistoryViewState())

    fun onRefresh() = intent {
//        val records = bsRecordDao.mockData()
        val records = bsRecordDao.fetchAllRecordsFlow().first()
        val bsUnit = userBehaviorDataStore.bsUnit

        reduce { state.copy(
            records = records,
            showNoRecordsContent = records.isEmpty(),
            bsUnit = bsUnit
        ) }
    }
}
