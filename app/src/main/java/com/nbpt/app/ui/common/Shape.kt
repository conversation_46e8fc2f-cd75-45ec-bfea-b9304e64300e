package com.nbpt.app.ui.common

import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection

val Triangle =
    object : Shape {
        override fun createOutline(
            size: Size,
            layoutDirection: LayoutDirection,
            density: Density
        ): Outline {
            val path = Path()
            path.moveTo(size.width / 2, 0f)
            path.lineTo(0f, size.height)
            path.lineTo(size.width, size.height)
            path.close()
            return Outline.Generic(path)
        }
    }
