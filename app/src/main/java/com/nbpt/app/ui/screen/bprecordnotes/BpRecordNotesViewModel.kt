package com.nbpt.app.ui.screen.bprecordnotes

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.data.db.dao.BpRecordsNoteDao
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.nbpt.app.data.pojo.BpRecordsNote
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BpRecordNotesViewModel(
    savedStateHandle: SavedStateHandle,
    private val noteDao: BpRecordsNoteDao
) : ViewModel(), ContainerHost<BpRecordNotesViewState, Unit> {

    override val container: Container<BpRecordNotesViewState, Unit> =
        container(BpRecordNotesViewState.Empty)

    private val navArgs = savedStateHandle.navArgs<RecordNotesNavArgs>()

    init {
        intent {
            reduce { state.copy(noteSelection = navArgs.notesSelection ?: emptyList()) }
        }

        loadNotes()
    }

    fun loadNotes() = intent {
        val notes = noteDao.fetchAllNotesFlow().first()

        reduce { state.copy(notes = notes) }
    }

    fun onSelect(note: BpRecordsNoteEntity, selected: Boolean) = intent {
        val newSelection = if (selected) {
            state.noteSelection.toMutableList().apply { add(note.content) }
        } else {
            state.noteSelection.toMutableSet().apply { remove(note.content) }.toList()
        }

        reduce { state.copy(noteSelection = newSelection) }
    }

    fun onShowAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = true) }
    }

    fun onDismissAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = false) }
    }

    fun onAddNote(noteText: String) = intent {
        val newNoteSingle = listOf(BpRecordsNote(content = noteText).toDbEntity())

        val addFailed = noteDao.add(newNoteSingle).contains(-1)

        if (!addFailed) {
            reduce { state.copy(notes = newNoteSingle + state.notes) }
        }

        onDismissAddNoteDialog()
    }
}
