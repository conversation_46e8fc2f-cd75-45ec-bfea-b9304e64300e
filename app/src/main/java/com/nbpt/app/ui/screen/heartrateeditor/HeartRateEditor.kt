package com.nbpt.app.ui.screen.heartrateeditor

import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.Cake
import androidx.compose.material.icons.rounded.DeleteForever
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material.icons.rounded.Favorite
import androidx.compose.material.icons.rounded.Person
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.compose.HrTheme
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.alarmguide.AddRemindAlarmGuideManager
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.biz.remoteconfig.useLegacyAd
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.adt.HrStatus
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.common.DiscardThisXTipsDialog
import com.nbpt.app.ui.common.HrStatusTypeDialog
import com.nbpt.app.ui.screen.anr.RecommendedReading
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.screen.destinations.HeartRateRecordNotesDestination
import com.nbpt.app.ui.screen.heartraterecordnotesmanager.hrRecordNotesManagerOnBackEvent
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.result.NavResult
import com.ramcosta.composedestinations.result.ResultRecipient
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.time.format.FormatStyle

sealed interface HeartRateEditorRecipientEvent : Parcelable {
    @Parcelize
    data class NotesSelectionFinish(
        val notesSelection: ArrayList<String>
    ) : HeartRateEditorRecipientEvent

    @Parcelize
    data object NotesManagerNavUp : HeartRateEditorRecipientEvent
}

enum class HeartRateEditorMode(@StringRes val titleStringId: Int) {
    Add(R.string.text_measurement),
    Edit(R.string.text_edit)
}

data class HeartRateEditorNavArgs(
    val editorMode: HeartRateEditorMode? = null,
    val recordUUID: String? = null,
    val instant: Instant = nowInstant(),
    val gender: Int = 0,
    val age: Int = 25,
    val heartRateBpm: Int = 75,
    val notesSelection: ArrayList<String>? = null,
)

@Destination(
    navArgsDelegate = HeartRateEditorNavArgs::class
)
@Composable
fun HeartRateEditor(
    navigator: DestinationsNavigator,
    resultRecipient: ResultRecipient<HeartRateRecordNotesDestination, HeartRateEditorRecipientEvent>,
) {
    val context = LocalContext.current
    val viewModel: HeartRateEditorViewModel = koinViewModel()
    val ratingManager: RatingManager = koinInject()
    val alarmGuideManager: AddRemindAlarmGuideManager = koinInject()

    viewModel.collectSideEffect {
        when (it) {
            HeartRateEditorSideEffect.NavUp -> navigator.navigateUp()
            HeartRateEditorSideEffect.SaveAndNavUp -> {
                if (viewModel.navArgs.editorMode == HeartRateEditorMode.Add) {
                    navigator.navigateUp()
                    GlobalScope.launch {
                        val hasOpenRatingSheet =
                            ratingManager.tryToOpenRatingSheet(context.findActivity())

                        debugLog { "hasOpenRatingSheet: $hasOpenRatingSheet" }
                        if (!hasOpenRatingSheet) {
                            alarmGuideManager.tryToOpenGuideDialog()
                        }
                    }
                } else {
                    navigator.navigateUp()
                }
            }
        }
    }

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    val viewState by viewModel.collectAsState()

    HrTheme {
        HeartRateEditor(
            navigator = navigator,
            viewModel = viewModel,
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState
        )
    }

    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//        viewModel.registerInterRewardedAdEventFlow(this)

        hrRecordNotesManagerOnBackEvent.onEach {
            val deletedSelection = viewModel.deletedSelection(it)

            viewModel.onNotesDelete(deletedSelection)

            navigator.navigate(
                HeartRateRecordNotesDestination(
                    notesSelection = ArrayList(
                        viewState.notesSelection - deletedSelection
                    )
                )
            )
        }.launchIn(this)
    }

    resultRecipient.onNavResult { result ->
        when (result) {
            NavResult.Canceled -> {}
            is NavResult.Value -> {
                when (result.value) {
                    is HeartRateEditorRecipientEvent.NotesSelectionFinish -> viewModel.onNotesSelectionUpdate(
                        (result.value as HeartRateEditorRecipientEvent.NotesSelectionFinish).notesSelection
                    )

                    HeartRateEditorRecipientEvent.NotesManagerNavUp -> navigator.navigate(
                        HeartRateRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                }
            }
        }
    }

    BackHandler {
        viewModel.onBack()
    }
}

@Composable
private fun HeartRateEditor(
    navigator: DestinationsNavigator,
    viewModel: HeartRateEditorViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: HeartRateEditorViewState
) {
    val context = LocalContext.current

    if (viewState.showGenderDialog) {
        GenderDialog(
            gender = viewState.gender,
            onSelect = {
                viewModel.onGenderChange(it)
                viewModel.onDismissGenderDialog()
            },
            onDismiss = viewModel::onDismissGenderDialog
        )
    }

    if (viewState.showAgeDialog) {
        AgeDialog(
            age = viewState.age,
            onAgeSelect = {
                viewModel.onAgeChange(it)
                viewModel.onDismissAgeDialog()
            },
            onDismiss = viewModel::onDismissAgeDialog
        )
    }

    if (viewState.showHeartRateStatusDialog) {
        HrStatusTypeDialog(onDismiss = viewModel::onDismissHeartRateStatusDialog)
    }

    if (viewState.showHeartRateDeleteDialog) {
        AlertDialog(
            onDismissRequest = viewModel::onDismissHeartRateDeleteDialog,
            confirmButton = {
                TextButton(onClick = {
                    viewModel.onDismissHeartRateDeleteDialog()
                    viewModel.onDelete()
                    logEventRecord("click_heart_rate_record_delete")
                }) {
                    Text(text = stringResource(id = R.string.text_delete))
                }
            },
            dismissButton = {
                TextButton(onClick = viewModel::onDismissHeartRateDeleteDialog) {
                    Text(text = stringResource(id = R.string.text_cancel))
                }
            },
            title = {
                Text(
                    text = stringResource(R.string.text_delete_record),
                    style = MaterialTheme.typography.titleLarge,
                )
            },
            text = {
                Text(text = stringResource(R.string.text_tips_delete_record))
            }
        )
    }

    if (viewState.showExitEditorTipsDialog) {
        if (viewState.editorMode == HeartRateEditorMode.Add) {
            DiscardThisXTipsDialog(
                onDismiss = viewModel::onDismissExitEditorTipsDialog,
                onDiscard = {
                    viewModel.onDismissExitEditorTipsDialog()
                    viewModel.onBack(true)
                },
                title = stringResource(R.string.text_title_discard_record),
            )
        }
    }

    Scaffold(
        topBar = {
            HeartRateEditorAppBar(
                editorMode = viewState.editorMode,
                navUp = viewModel::onBack,
                onDelete = if (viewState.editorMode == HeartRateEditorMode.Edit) {
                    { viewModel.onShowHeartRateDeleteDialog() }
                } else null
            )
        },
        bottomBar = {
            Column(modifier = Modifier.navigationBarsPadding()) {
                CardButton(
                    text = stringResource(R.string.text_save),
                    onClick = { viewModel.onSave() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 64.dp),
                    containerBrush = AppTheme.Color.HrBrush
                )

                BlankSpacer(height = 16.dp)

                SmartRectAd(
                    pageType = SmartAdPageType.RESULT_BOTTOM_BAR,
                    bannerAdPlace = BannerAdPlaceholder.HR_EDITOR,
                    nativeAdPlace = NativeAdPlace.HrEditor,
                )
            }
        },
        containerColor = AppTheme.Color.HrBackground
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)

            HeartRateEditorBpmOverviewItem(
                bpm = viewState.heartRateBpm,
                instant = viewState.instant ?: Instant.fromEpochMilliseconds(0),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .defShadow()
            )

            BlankSpacer(height = 16.dp)

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal =16.dp)
            ) {
                HeartRateEditorGenderItem(
                    onClick = viewModel::onShowGenderDialog,
                    gender = viewState.gender,
                    modifier = Modifier
                        .weight(1f)
                        .defShadow()
                )

                BlankSpacer(width = 10.dp)

                HeartRateEditorAgeItem(
                    onClick = viewModel::onShowAgeDialog,
                    age = viewState.age,
                    modifier = Modifier
                        .weight(1f)
                        .defShadow()
                )
            }

            BlankSpacer(height = 16.dp)

            HeartRateEditorBpmStatusItem(
                onStatusInfoClick = viewModel::onShowHeartRateStatusDialog,
                onUnlockAnalysisInfo = {
//                        viewModel.onUnlockAnalysisInfo(
//                            activity = context.findActivity(),
//                            adPlaceName = "hr_analyze"
//                        )
                },
                bpm = viewState.heartRateBpm,
//                isUnlockAnalysis = viewState.isUnlockAnalysis
                isUnlockAnalysis = true,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .defShadow()
            )

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.RESULT_CONTENT,
                bannerAdPlace = BannerAdPlaceholder.HR_EDITOR,
                nativeAdPlace = NativeAdPlace.HrEditor,
            )

            BlankSpacer(height = 16.dp)

            HeartRateEditorNotesItem(
                onNotesEdit = {
                    navigator.navigate(
                        HeartRateRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                },
                notesSelection = viewState.notesSelection,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .defShadow()
            )

            BlankSpacer(height = 20.dp)

            val interstitialAdManager: MaxInterstitialAdManager = koinInject()
            RecommendedReading(
                title = stringResource(R.string.text_recommended_reading),
                onArticleClick = { ha ->
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_article", onAdLoadingAfter = {
                            navigator.navigate(ArticleDestination(ha))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = {
                                navigate(ArticleDestination(ha))
                            },
                            adPlaceName = "enter_article"
                        )
                    }
                },
                articles = viewState.hrArticles,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@Composable
private fun HeartRateEditorBpmOverviewItem(
    bpm: Int?,
    instant: Instant,
    modifier: Modifier = Modifier
) {
    Surface(
        shape = RoundedCornerShape12Dp,
        modifier = modifier,
        color = AppTheme.Color.cardContainer
    ) {
        Box {
            Image(
                painter = painterResource(id = R.drawable.bg_hr_wave_item),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .height(72.dp)
                    .width(280.dp)
                    .alpha(.3f)
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp, horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = bpm.toString(),
                    style = MaterialTheme.typography.displayMedium
                )

                BlankSpacer(width = 6.dp)

                Column {
                    Icon(
                        imageVector = Icons.Rounded.Favorite,
                        contentDescription = null,
                        tint = AppTheme.Color.Red,
                        modifier = Modifier.size(18.dp)
                    )
                    Text(
                        text = stringResource(R.string.text_bpm),
                        style = MaterialTheme.typography.labelMedium.copy(fontSize = 13.sp)
                    )
                }

                Spacer(modifier = Modifier.weight(1f))

                val dateTime = instant.toLocalDatetime()
                val dateText = dateTime.date.isoFormat(FormatStyle.MEDIUM)
                val timeText = dateTime.time.isoFormat()
                val dateTimeText = "$timeText\n$dateText"

                Text(
                    text = dateTimeText,
                    style = MaterialTheme.typography.bodySmall.copy(lineHeight = 22.sp),
                    textAlign = TextAlign.End,
                )
            }
        }

    }
}

@Composable
private fun HeartRateEditorGenderItem(
    onClick: () -> Unit,
    gender: Gender?,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = AppTheme.Color.cardContainer,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Rounded.Person,
                contentDescription = null,
                modifier = Modifier.size(28.dp)
            )
            BlankSpacer(width = 8.dp)

            Text(
                text = if (gender == null) "" else stringResource(id = gender.textStringId),
                fontSize = 14.sp
            )
        }
    }
}

@Composable
private fun HeartRateEditorAgeItem(
    onClick: () -> Unit,
    age: Int,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = AppTheme.Color.cardContainer,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Rounded.Cake,
                contentDescription = null,
                modifier = Modifier.size(28.dp)
            )
            BlankSpacer(width = 8.dp)

            Text(
                text = stringResource(R.string.text_age, age),
                fontSize = 14.sp
            )
        }
    }
}

@Composable
private fun HeartRateEditorBpmStatusItem(
    onStatusInfoClick: () -> Unit,
    onUnlockAnalysisInfo: () -> Unit,
    bpm: Int,
    isUnlockAnalysis: Boolean,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = AppTheme.Color.cardContainer,
    ) {
        val status = HrStatus.from(bpm)

        Box(modifier = Modifier.padding(16.dp)) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                Surface(color = status.color, shape = RoundedCornerShape12Dp) {
                    Box(contentAlignment = Alignment.Center, modifier = Modifier.width(180.dp)) {
                        Text(
                            text = stringResource(id = status.titleStringId),
                            modifier = Modifier.padding(8.dp),
                            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.White),
                        )
                    }
                }

                BlankSpacer(height = 8.dp)

                Text(
                    text = stringResource(id = status.descriptionStringId),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(horizontal = 20.dp)
                )

                BlankSpacer(height = 8.dp)

                HeartRateStatusBar(hrStatus = status)

                BlankSpacer(height = 8.dp)

                if (isUnlockAnalysis) {
                    Text(
                        text = stringResource(id = status.analysisStringId),
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        modifier = Modifier.fillMaxWidth()
                    )
                } else {
                    Row(
                        modifier = Modifier.bodyWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_ad_trumpet),
                            contentDescription = null,
                            modifier = Modifier.size(26.dp),
                            tint = AppTheme.Color.Red
                        )
                        BlankSpacer(width = 4.dp)

                        Text(
                            text = stringResource(R.string.text_view_ad_to_analyze_your_heart_rate),
                            style = MaterialTheme.typography.titleMedium
                        )
                    }

                    BlankSpacer(height = 16.dp)

                    CardButton(
                        text = stringResource(R.string.text_watch_now),
                        onClick = onUnlockAnalysisInfo,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 40.dp)
                    )
                }
            }



            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 4.dp),
            ) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "info",
                    modifier = Modifier
                        .size(28.dp)
                        .clickable(onClick = onStatusInfoClick)
                )
            }
        }
    }
}

@Composable
private fun HeartRateStatusBar(
    hrStatus: HrStatus,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.Bottom) {
            Surface(
                modifier = Modifier
                    .weight(0.4f)
                    .height(if (hrStatus is HrStatus.Slow) 18.dp else 12.dp),
                shape = RoundedCornerShape(
                    topStartPercent = 50,
                    bottomStartPercent = 50,
                    topEndPercent = 15,
                    bottomEndPercent = 15
                ),
                color = HrStatus.Slow.color
            ) {}

            BlankSpacer(width = 4.dp)

            Surface(
                modifier = Modifier
                    .weight(0.6f)
                    .height(if (hrStatus is HrStatus.Normal) 18.dp else 12.dp),
                shape = RoundedCornerShape(percent = 15),
                color = HrStatus.Normal.color
            ) {}

            BlankSpacer(width = 4.dp)

            Surface(
                modifier = Modifier
                    .weight(1f)
                    .height(if (hrStatus is HrStatus.Fast) 18.dp else 12.dp),
                shape = RoundedCornerShape(
                    topEndPercent = 50,
                    bottomEndPercent = 50,
                    topStartPercent = 15,
                    bottomStartPercent = 15
                ),
                color = HrStatus.Fast.color
            ) {}
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeartRateEditorAppBar(
    editorMode: HeartRateEditorMode?,
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
    onDelete: (() -> Unit)? = null,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = { Text(text = if (editorMode == null) "" else stringResource(id = editorMode.titleStringId)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                if (onDelete != null) {
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Rounded.DeleteForever,
                            contentDescription = "delete",
                        )
                    }
                }
            }
        )
        SmartRectAd(
            pageType = SmartAdPageType.RESULT_TOP_BAR,
            bannerAdPlace = BannerAdPlaceholder.HR_EDITOR,
            nativeAdPlace = NativeAdPlace.HrEditor,
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun HeartRateEditorNotesItem(
    onNotesEdit: () -> Unit,
    notesSelection: List<String>,
    modifier: Modifier = Modifier,
) {
    Surface(onClick = onNotesEdit, modifier = modifier, shape = RoundedCornerShape12Dp) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.text_note),
                    style = MaterialTheme.typography.bodyLarge
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    imageVector = Icons.Rounded.Edit,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp)
                )

            }

            if (notesSelection.isNotEmpty()) {
                FlowRow {
                    notesSelection.forEach { note ->
                        Text(
                            text = "#$note",
                            modifier = Modifier.padding(horizontal = 4.dp),
                            style = MaterialTheme.typography.labelLarge.copy(color = AppTheme.Color.textSecondary)
                        )
                    }
                }

                BlankSpacer(height = 16.dp)
            }
        }
    }
}

@Preview
@Composable
private fun HeartRateEditorBpmOverviewItemPreview() {

    HeartRateEditorBpmOverviewItem(
        bpm = 100, instant = nowInstant(),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    )
}

@Preview
@Composable
private fun HeartRateEditor2ndRowItemPreview() {

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        HeartRateEditorAgeItem(onClick = { /*TODO*/ }, age = 100, modifier = Modifier.weight(1f))

        BlankSpacer(width = 10.dp)

        HeartRateEditorGenderItem(
            onClick = { /*TODO*/ },
            gender = Gender.Male,
            modifier = Modifier.weight(1f)
        )

    }
}

@Preview
@Composable
private fun HeartRateEditorBpmStatusItemPreview() {
    HeartRateEditorBpmStatusItem(onStatusInfoClick = { /*TODO*/ }, {}, bpm = 70, true)
}

@Preview
@Composable
private fun HeartRateEditorNotesItemPreview() {
    HeartRateEditorNotesItem(
        onNotesEdit = { /*TODO*/ },
        notesSelection = listOf("lalala", "blabla")
    )
}
