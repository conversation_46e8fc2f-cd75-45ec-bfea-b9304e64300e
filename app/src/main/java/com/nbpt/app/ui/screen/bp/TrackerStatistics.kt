package com.nbpt.app.ui.screen.bp

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowLeft
import androidx.compose.material.icons.rounded.ArrowRight
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable

sealed class TrackerRoughStatModeAdt(@StringRes val descriptionStringId: Int) {
    data object Max :
        TrackerRoughStatModeAdt(R.string.text_max)

    data object Min : TrackerRoughStatModeAdt(R.string.text_min)

    data object Avg :
        TrackerRoughStatModeAdt(R.string.text_avg)

    data object Latest : TrackerRoughStatModeAdt(R.string.text_latest)
}

val modes = listOf(
    TrackerRoughStatModeAdt.Max,
    TrackerRoughStatModeAdt.Min,
    TrackerRoughStatModeAdt.Avg,
    TrackerRoughStatModeAdt.Latest,
)

data class TrackerRoughStatData(
    val systolic: Int?,
    val diastolic: Int?,
    val pulse: Int?,
)

@Composable
internal fun TrackerStatistics(
    selectMode: TrackerRoughStatModeAdt,
    statData: TrackerRoughStatData?,
    onModeSelect: (TrackerRoughStatModeAdt) -> Unit,
    modifier: Modifier = Modifier,
    statContent: @Composable ColumnScope.() -> Unit,
) {
    Column(modifier) {
        TrackerRoughStatModeSwitch(
            selectMode = selectMode,
            onSelect = onModeSelect,
            modifier = Modifier.fillMaxWidth().padding(horizontal = 24.dp),
        )

        Surface(shape = RoundedCornerShape12Dp, modifier = Modifier.defShadow()) {
            Column {
                BlankSpacer(height = 22.dp)
                TrackerRoughStatDashBoard(
                    selectMode = selectMode,
                    systolic = statData?.systolic,
                    diastolic = statData?.diastolic,
                    pulse = statData?.pulse,
                    modifier = Modifier.fillMaxWidth(),
                )
                statContent()
                BlankSpacer(height = 20.dp)
            }
        }

    }
}

@Composable
private fun TrackerRoughStatModeSwitch(
    selectMode: TrackerRoughStatModeAdt,
    onSelect: (TrackerRoughStatModeAdt) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {

        Icon(
            imageVector = Icons.Rounded.ArrowLeft,
            contentDescription = null,
            modifier = Modifier
                .size(56.dp)
                .noRippleClickable {
                    val currentIndex = modes.indexOf(selectMode)

                    if (currentIndex == 0) {
                        onSelect(modes.last())
                    } else {
                        onSelect(modes[currentIndex - 1])
                    }
                }
        )

        Text(
            text = stringResource(id = selectMode.descriptionStringId),
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.titleLarge
        )


        Icon(
            imageVector = Icons.Rounded.ArrowRight,
            contentDescription = null,
            modifier = Modifier
                .size(56.dp)
                .noRippleClickable {
                    val currentIndex = modes.indexOf(selectMode)

                    if (currentIndex == modes.lastIndex) {
                        onSelect(modes.first())
                    } else {
                        onSelect(modes[currentIndex + 1])
                    }
                }
        )
    }
}

@Composable
private fun TrackerRoughStatDashBoard(
    selectMode: TrackerRoughStatModeAdt,
    systolic: Int?,
    diastolic: Int?,
    pulse: Int?,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
    ) {
        DashBoardText(
            systolic = systolic,
            diastolic = diastolic,
            pulse = pulse,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun DashBoardText(
    systolic: Int?,
    diastolic: Int?,
    pulse: Int?,
    modifier: Modifier = Modifier
) {
    Row(modifier) {
        DashBoardTextItem(
            type = stringResource(id = R.string.text_systolic),
            value = systolic?.toString(),
            unit = "mmHg",
            modifier = Modifier.weight(1f)
        )
        DashBoardTextItem(
            type = stringResource(id = R.string.text_diastolic),
            value = diastolic?.toString(),
            unit = "mmHg",
            modifier = Modifier.weight(1f)
        )
        DashBoardTextItem(
            type = stringResource(id = R.string.text_systolic),
            value = pulse?.toString(),
            unit = "BPM",
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun DashBoardTextItem(
    type: String,
    value: String?,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = type,
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textSecondaryDark)
        )

        BlankSpacer(height = 6.dp)

        Text(text = value ?: "--", style = MaterialTheme.typography.titleLarge)

        BlankSpacer(height = 4.dp)

        Text(
            text = unit,
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textSecondaryDark)
        )
    }
}

@Preview
@Composable
private fun TrackerRoughStatisticsPreview() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Layout.bodyMargin)
    ) {
        TrackerStatistics(
            selectMode = TrackerRoughStatModeAdt.Max,
            statData = TrackerRoughStatData(
                systolic = 180,
                diastolic = 90,
                pulse = 99,
            ),
            onModeSelect = {}
        ) {}
    }
}
