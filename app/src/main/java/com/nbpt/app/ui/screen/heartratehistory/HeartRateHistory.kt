package com.nbpt.app.ui.screen.heartratehistory

import androidx.activity.compose.BackHandler
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import com.example.compose.HrTheme
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel

import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord

//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankHeightIn
import com.nbpt.app.ui.common.HrRecordItem

import com.nbpt.app.ui.screen.destinations.HeartRateEditorDestination
import com.nbpt.app.ui.screen.heartrateeditor.HeartRateEditorMode
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun HeartRateHistory(
    navigator: DestinationsNavigator
) {
    val viewModel: HeartRateHistoryViewModel = koinViewModel()
    val context = LocalContext.current

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }

//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//    }
//
//    BackHandler {
//        viewModel.onBack(context.findActivity())
//    }

    viewModel.collectSideEffect {
        when (it) {
            HeartRateHistorySideEffect.NavUp -> navigator.navigateUp()
        }
    }

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    val viewState by viewModel.collectAsState()

    HrTheme {
        HeartRateHistory(
            navigator = navigator,
            viewModel = viewModel,
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
            viewState = viewState
        )
    }
}

@Composable
private fun HeartRateHistory(
    navigator: DestinationsNavigator,
    viewModel: HeartRateHistoryViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: HeartRateHistoryViewState,
) {
    val context = LocalContext.current
    val onBack = {
        admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
            activity = context.findActivity(),
            adPlaceName = "exit_bs_list"
        )
        Unit
    }

    BackHandler(onBack = onBack)

    Scaffold(
        topBar = {
            HeartRateHistoryAppBar(navUp = onBack)
        },
        bottomBar = {
            HeartRateHistoryBottomBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Layout.bodyMargin)
                    .padding(vertical = Layout.gutter)
                    .navigationBarsPadding()
            )
        },
        containerColor = AppTheme.Color.HrBackground
    ) {
        Crossfade(
            targetState = viewState.showNoRecordsContent,
            modifier = Modifier
                .padding(it)
                .fillMaxSize(),
            label = ""
        ) { showNoRecordsContent ->
            if (showNoRecordsContent) {
                HeartRateHistoryNoRecordsContent(modifier = Modifier.fillMaxSize())
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    viewState.hrRecords.forEachIndexed { index, hrRecord ->
                        if (index == 0) {
                            item {
                                Spacer(modifier = Modifier.height(Layout.gutter))
                            }
                        }

                        item {
                            HrRecordItem(
                                hrRecord = hrRecord,
                                modifier = Modifier
                                    .padding(
                                        vertical = Layout.gutter,
                                        horizontal = Layout.bodyMargin
                                    )
                                    .defShadow(),
                                onEditClick = {
                                    navigator.navigate(
                                        HeartRateEditorDestination(
                                            editorMode = HeartRateEditorMode.Edit,
                                            recordUUID = hrRecord.uuid,
                                            instant = hrRecord.instant,
                                            gender = hrRecord.gender,
                                            age = hrRecord.age,
                                            heartRateBpm = hrRecord.heartRateBpm,
                                            notesSelection = ArrayList(hrRecord.notes)
                                        )
                                    )
                                    logEventRecord("click_heart_rate_record_edit")
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeartRateHistoryAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_history)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
    )
}

@Composable
private fun HeartRateHistoryBottomBar(
    modifier: Modifier = Modifier,
) {
    ListAdaptiveRectangleAd(
        bannerAdPlace = BannerAdPlace.HR_HISTORY,
        nativeAdPlace = NativeAdPlace.HrHistory,
        modifier = modifier,
    )
}

@Composable
private fun HeartRateHistoryNoRecordsContent(
    modifier: Modifier = Modifier
) {
    Column(modifier, Arrangement.Center, Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.img_no_record_for_hr),
            contentDescription = "no records",
            modifier = Modifier
                .requiredWidthIn(200.dp, 280.dp)
                .aspectRatio(281 / 165f)
                .padding(start = 40.dp)
        )

        BlankHeightIn(min = 32.dp, max = 64.dp)

        Text(
            text = stringResource(R.string.text_content_no_record),
            style = MaterialTheme.typography.titleLarge
        )

        BlankHeightIn(min = 8.dp, max = 16.dp)

        Text(
            text = stringResource(R.string.text_measure_your_heart_rate_now),
            style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.textSecondary)
        )
    }
}
