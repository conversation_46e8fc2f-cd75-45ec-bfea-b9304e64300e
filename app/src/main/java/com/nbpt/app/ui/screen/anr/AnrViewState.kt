package com.nbpt.app.ui.screen.anr

import com.nbpt.app.data.db.model.BpRecordEntity
import com.nbpt.app.data.healtharticles.HealthArticle

data class AnrViewState(
    val anrMode: AnrMode? = null,
    val isUnlockAnalysis: Boolean = false,
    val bpRecords: List<BpRecordEntity> = emptyList(),
    val recommendedArticles: List<HealthArticle> = emptyList(),
    val showAdLoadingDialog: Boolean = false,
    val isUnlockBpRecordStat: Boolean = false,
) {
    companion object {
        val Empty = AnrViewState()
    }
}
