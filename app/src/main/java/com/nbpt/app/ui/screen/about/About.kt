package com.nbpt.app.ui.screen.about

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.BuildConfig
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.logEventRecord
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.android.openBrowser
//import com.nbpt.app.getFirebaseToken
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.SettingsItem

import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.compose.koinInject

@Destination
@Composable
fun About(
    navigator: DestinationsNavigator,
) {
    val context = LocalContext.current
    val splashManager: SplashManager = koinInject()

    var firebaseToken by remember { mutableStateOf<String?>(null) }
//    LaunchedEffect(Unit) {
//        getFirebaseToken {
//            firebaseToken = it
//        }
//    }

    Scaffold(
        topBar = {
            AboutAppBar(navUp = navigator::navigateUp)
        }
    ) {

        Column(
            modifier = Modifier
                .padding(it)
                .padding(horizontal = Layout.bodyMargin)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = Layout.bodyMargin * 3)

            Image(
                painter = painterResource(id = R.drawable.launcher_icon),
                contentDescription = null,
                modifier = Modifier
                    .bodyWidth()
                    .size(64.dp)
            )

            BlankSpacer(height = Layout.textMargin)

            Text(
                text = stringResource(id = R.string.app_name),
                modifier = Modifier.bodyWidth(),
                style = MaterialTheme.typography.titleLarge
            )

            BlankSpacer(height = Layout.gutter)

            Text(
                text = stringResource(R.string.text_version, BuildConfig.VERSION_NAME),
                modifier = Modifier.bodyWidth(),
                style = MaterialTheme.typography.labelSmall.copy(color = AppTheme.Color.textSecondary)
            )

            firebaseToken?.let {
                BlankSpacer(height = Layout.gutter)
                // make text copyable
                SelectionContainer {
                    Text(text = "Firebase Token: $firebaseToken")
                }
            }


            BlankSpacer(height = Layout.bodyMargin * 3)

            SettingsItem(
                onItemClick = {
                    context.openBrowser(BuildConfig.privacyPolicyUrl)
                    splashManager.doSkipSplash(true)
                    logEventRecord("click_privacy")
                },
                iconPainter = painterResource(id = R.drawable.ic_pp),
                title = stringResource(R.string.text_privacy_policy),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = Layout.bodyMargin)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AboutAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_about)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
    )
}
