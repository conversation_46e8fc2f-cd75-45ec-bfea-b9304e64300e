package com.nbpt.app.ui.screen.bmiresult

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.Info
import androidx.compose.material.icons.rounded.LocationOn
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.data.adt.BmiStatus
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BmiTypeDialog
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bmi.BmiTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator

data class BmiResultNavArgs(
    val bmi: Float,
    val bmiStatus: BmiStatus
)

@Destination(
    navArgsDelegate = BmiResultNavArgs::class
)
@Composable
fun BmiResult(
    args: BmiResultNavArgs,
    navigator: DestinationsNavigator
) {
    BmiTheme {
        var showBmiTypeDialog by remember { mutableStateOf(false) }

        if (showBmiTypeDialog) {
            BmiTypeDialog(onDismiss = { showBmiTypeDialog = false })
        }

        BackHandler {
            if (showBmiTypeDialog) {
                showBmiTypeDialog = false
            } else {
                navigator.navigateUp()
            }
        }

        Scaffold(
            topBar = {
                BmiResultAppBar(navUp = navigator::navigateUp)
            },
            containerColor = AppTheme.Color.BmiBackground
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(it)
            ) {
                BlankSpacer(height = 24.dp)

                Surface(
                    shape = RoundedCornerShape12Dp,
                    modifier = Modifier
                        .padding(horizontal = 20.dp)
                        .defShadow()
                ) {
                    Column {
                        BlankSpacer(height = 28.dp)
                        Box(modifier = Modifier.bodyWidth()) {
                            CircularProgressIndicator(
                                progress = 1f,
                                modifier = Modifier.size(180.dp),
                                color = args.bmiStatus.color,
                                strokeWidth = 20.dp
                            )

                            Text(
                                text = args.bmi.scale(2).toString(),
                                modifier = Modifier.align(Alignment.Center),
                                style = MaterialTheme.typography.headlineLarge
                            )
                        }

                        BlankSpacer(height = 12.dp)

                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Spacer(modifier = Modifier.weight(1f))
                                Text(
                                    text = stringResource(id = args.bmiStatus.titleStringId),
                                    style = MaterialTheme.typography.titleLarge
                                )
                                Box(modifier = Modifier.weight(1f)) {
                                    IconButton(onClick = { showBmiTypeDialog = true }) {
                                        Icon(
                                            imageVector = Icons.Rounded.Info,
                                            contentDescription = null,
                                            tint = args.bmiStatus.color
                                        )
                                    }
                                }
                            }

                            Text(
                                text = stringResource(id = args.bmiStatus.descriptionStringId),
                                style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.textSecondary)
                            )

                            BlankSpacer(height = 8.dp)

                            BmiStatusBar(
                                currentStatus = args.bmiStatus,
                                modifier = Modifier.padding(horizontal = 12.dp)
                            )

                            BlankSpacer(height = 28.dp)
                        }
                    }
                }

                BlankSpacer(20.dp)

                SmartRectAd(
                    pageType = SmartAdPageType.RESULT_CONTENT,
                    bannerAdPlace = BannerAdPlace.BMI_RESULT,
                    nativeAdPlace = NativeAdPlace.BmiResult,
                )

                BlankSpacer(20.dp)

                CardButton(
                    text = stringResource(id = R.string.text_ok),
                    onClick = navigator::navigateUp,
                    containerBrush = AppTheme.Color.BmiBrush,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 84.dp)
                )
            }
        }
    }
}

@Composable
private fun BmiStatusBar(
    currentStatus: BmiStatus,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.Bottom) {
            BmiStatus.entries.forEach {
                BmiStatusBarItem(
                    bmiStatus = it,
                    currentStatus = currentStatus,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun BmiStatusBarItem(
    bmiStatus: BmiStatus,
    currentStatus: BmiStatus,
    modifier: Modifier = Modifier
) {

    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        if (bmiStatus == currentStatus) {
            BlankSpacer(height = 4.dp)
            Icon(
                imageVector = Icons.Rounded.LocationOn,
                contentDescription = null,
                tint = Color(0xFFD8326F)
            )
            BlankSpacer(height = 4.dp)
        }

        val barShape = when (bmiStatus) {
            BmiStatus.VerySeverelyUnderweight ->
                RoundedCornerShape(topStartPercent = 50, bottomStartPercent = 50)

            BmiStatus.ObeseClassIII ->
                RoundedCornerShape(topEndPercent = 50, bottomEndPercent = 50)

            else ->
                RectangleShape
        }

        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp),
            shape = barShape,
            color = bmiStatus.color,
            content = {}
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BmiResultAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier) {
        TopAppBar(
            title = { Text(text = stringResource(R.string.text_result)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.RESULT_TOP_BAR,
            bannerAdPlace = BannerAdPlace.BMI_RESULT,
            nativeAdPlace = NativeAdPlace.BmiResult,
        )
    }

}

@Preview
@Composable
private fun BmiStatusBarPreview() {
    BmiStatusBar(currentStatus = BmiStatus.Normal)
//    BmiStatusBarItem(bmiStatus = BmiStatus.Normal, currentStatus = BmiStatus.Normal)
}