@file:Suppress("ObjectPropertyName")

package com.nbpt.app.ui.common

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Scroller
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidViewBinding
import com.nbpt.app.R
import com.nbpt.app.common.calculate.kgToLbs
import com.nbpt.app.common.getFieldValue
import com.nbpt.app.databinding.LayoutWeightSelectorBinding
import com.nbpt.app.ui.theme.AppTheme

private val _maxKg by lazy { 270 }
private val _maxLb by lazy { kgToLbs(_maxKg.toFloat()) }

@Composable
fun BodyWeightSelectorContentImperial(
    lbs: Float,
    onLbsChange: (Float) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier = modifier) {
        BlankSpacer(height = 16.dp)

        Text(
            text = stringResource(id = R.string.text_weight),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary, fontSize = 18.sp)
        )

        BlankSpacer(height = 8.dp)

        AndroidViewBinding(
            factory = { inflater: LayoutInflater, parent: ViewGroup, attachToParent: Boolean ->
                LayoutWeightSelectorBinding.inflate(inflater, parent, attachToParent).apply {
                    rvWeight.setValue(0f, _maxLb, lbs, .1f, 10)

                    rvWeight.setOnValueChangedListener(onLbsChange)

                    tvLb.setTextColor(AppTheme.Color.textPrimary.toArgb())
                    tvLb.text = lbs.toString()

                    tvUnit.text = "lbs"
                }
            },
            update = {
                tvLb.text = lbs.toString()
            },
            modifier = Modifier.padding(horizontal = 10.dp)
        )

        BlankSpacer(height = 8.dp)
    }
}

@Composable
fun BodyWeightSelectorContentMetric(
    kg: Float,
    onKgChange: (Float) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier = modifier) {
        BlankSpacer(height = 16.dp)

        Text(
            text = stringResource(id = R.string.text_weight),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary, fontSize = 18.sp)
        )

        BlankSpacer(height = 8.dp)

        AndroidViewBinding(
            factory = { inflater: LayoutInflater, parent: ViewGroup, attachToParent: Boolean ->
                LayoutWeightSelectorBinding.inflate(inflater, parent, attachToParent).apply {
                    rvWeight.setValue(0f, _maxKg.toFloat(), kg, .1f, 10)

                    rvWeight.setOnValueChangedListener(onKgChange)

                    tvLb.setTextColor(AppTheme.Color.textPrimary.toArgb())
                    tvLb.text = kg.toString()

                    tvUnit.text = "kg"
                }
            },
            update = {
                tvLb.text = kg.toString()
            },
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        BlankSpacer(height = 8.dp)
    }
}
