package com.nbpt.app.ui.screen.history

import androidx.activity.compose.BackHandler
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import com.ramcosta.composedestinations.annotation.Destination
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankHeightIn
import com.nbpt.app.ui.common.BpRecordItem

import com.nbpt.app.ui.screen.destinations.BpRecordEditorDestination
import com.nbpt.app.ui.screen.bprecordeditor.BpRecordEditorMode
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bp.BpTheme
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Destination
@Composable
fun History(
    navigator: DestinationsNavigator,
) {
    BpTheme {
        History(
            navigator = navigator,
            viewModel = koinViewModel()
        )
    }
}

@Composable
private fun History(
    navigator: DestinationsNavigator,
    viewModel: HistoryViewModel
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

//    BackHandler {
//        viewModel.onBack(context.findActivity())
//    }
//
//    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)
//    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }

    viewModel.collectSideEffect {
        when (it) {
            HistorySideEffect.NavUp -> navigator.navigateUp()
        }
    }

    val viewState by viewModel.collectAsState()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd("exit_bp_list", onAdLoadingAfter = {
                navigator.navigateUp()
            })
        } else {
            admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                activity = context.findActivity(),
                adPlaceName = "exit_bp_list"
            )
        }
        Unit
    }

    BackHandler(onBack = onBack)

    Scaffold(
        topBar = {
            HistoryAppBar(navUp = onBack)
        },
        bottomBar = {
            HistoryBottomBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
            )
        },
        containerColor = AppTheme.Color.BpBackground
    ) {

        Crossfade(
            targetState = viewState.showNoRecordsContent,
            modifier = Modifier
                .padding(it)
                .fillMaxSize(), label = ""
        ) { showNoRecordsContent ->
            if (showNoRecordsContent) {
                HistoryNoRecordsContent(modifier = Modifier.fillMaxSize())
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    viewState.bpRecords.forEachIndexed { index, bpRecord ->
                        if (index == 0) {
                            item {
                                Spacer(modifier = Modifier.height(Layout.gutter))
                            }
                        }

                        item {
                            BpRecordItem(
                                bpRecord = bpRecord,
                                modifier = Modifier.padding(
                                    vertical = Layout.gutter,
                                    horizontal = Layout.bodyMargin
                                ),
                                onEditClick = {
                                    navigator.navigate(
                                        BpRecordEditorDestination(
                                            editorMode = BpRecordEditorMode.Edit,
                                            recordUUID = bpRecord.uuid,
                                            instant = bpRecord.instant,
                                            systolic = bpRecord.systolic,
                                            diastolic = bpRecord.diastolic,
                                            pulse = bpRecord.pulse,
                                            notesSelection = ArrayList(bpRecord.notes),
                                        )
                                    )

                                    logEventRecord("click_record_edit")
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun HistoryNoRecordsContent(
    modifier: Modifier = Modifier
) {
    Column(modifier, Arrangement.Center, Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.img_no_record_for_bp),
            contentDescription = "no records",
            modifier = Modifier
                .requiredWidthIn(200.dp, 280.dp)
                .aspectRatio(281 / 165f)
                .padding(start = 40.dp)
        )

        BlankHeightIn(min = 32.dp, max = 64.dp)

        Text(
            text = stringResource(R.string.text_no_record_here),
            style = MaterialTheme.typography.titleLarge
        )

        BlankHeightIn(min = 8.dp, max = 16.dp)

        Text(
            text = stringResource(R.string.text_please_add_a_new_record),
            style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.textSecondary)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HistoryAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_history)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
    )
}

@Composable
private fun HistoryBottomBar(
    modifier: Modifier = Modifier,
) {
    ListAdaptiveRectangleAd(
        bannerAdPlace = BannerAdPlaceholder.BP_HISTORY,
        nativeAdPlace = NativeAdPlaceholder.BpHistory,
        modifier = modifier,
    )
}
