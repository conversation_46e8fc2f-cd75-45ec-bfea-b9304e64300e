package com.nbpt.app.ui.screen.info

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.data.healtharticles.HealthArticle
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun InfoScreen(
    navigator: DestinationsNavigator,
    viewModel: InfoViewModel,
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    val viewState by viewModel.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.onFetch()
    }

    viewModel.collectSideEffect {
        when (it) {
            is InfoSideEffect.NavTo -> navigator.navigate(it.destination)
        }
    }

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    Scaffold(
        topBar = {
            InfoAppBar()
        },
        contentWindowInsets = ScaffoldDefaults.contentWindowInsets.exclude(WindowInsets.navigationBars)
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
        ) {

            BlankSpacer(height = 12.dp)

            viewState.articleMap?.flatMap { (_, articles) -> 
                articles
            }?.forEachIndexed { index, article ->
                ArticleItem(
                    onClick = {
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd(
                                "enter_article",
                                onAdLoadingAfter = {
                                    navigator.navigate(ArticleDestination(article, true))
                                })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = {
                                    navigate(ArticleDestination(article, true))
                                },
                                adPlaceName = "enter_article"
                            )
                        }
                    },
                    article = article,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .defShadow()
                )

                BlankSpacer(height = 12.dp)

                if (index == 4) {
                    SmartRectAd(
                        pageType = SmartAdPageType.HOME_CONTENT,
                        bannerAdPlace = BannerAdPlace.HOME,
                        nativeAdPlace = NativeAdPlace.Info,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun InfoAppBar(
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        TopAppBar(
            title = {
                Text(text = stringResource(R.string.info_title))
            },
        )

        SmartRectAd(
            pageType = SmartAdPageType.HOME_TOP_BAR,
            bannerAdPlace = BannerAdPlace.HOME,
            nativeAdPlace = NativeAdPlace.Info,
        )
    }
}

@Composable
fun ArticleItem(
    onClick: (HealthArticle) -> Unit,
    article: HealthArticle,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = { onClick(article) },
        shape = RoundedCornerShape12Dp,
        modifier = modifier.defShadow()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 6.dp)

            Image(
                painter = painterResource(id = article.imgRes),
                contentDescription = null,
                modifier = Modifier.size(50.dp)
            )

            BlankSpacer(width = 12.dp)

            Text(
                text = article.title,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.weight(1f)
            )

            BlankSpacer(width = 12.dp)

            Surface(
                shape = CircleShape,
                contentColor = Color.White,
                color = AppTheme.Color.Primary,
            ) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowRight,
                    contentDescription = null,
                    modifier = Modifier.size(22.dp)
                )
            }

            BlankSpacer(width = 14.dp)
        }
    }
}
