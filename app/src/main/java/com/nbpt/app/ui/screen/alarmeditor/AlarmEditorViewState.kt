package com.nbpt.app.ui.screen.alarmeditor

import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmType
import com.nbpt.app.common.datetime.dayOfWeeks
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalTime

data class AlarmEditorViewState(
    val isSetNewAlarm: Boolean = true,
    val localTime: LocalTime? = null,
    val dayOfWeekSelections: Set<DayOfWeek> = dayOfWeeks(),
    val soundEnable: Boolean = false,
    val vibrateEnable: Boolean = false,
    val remindType: RemindToRecordAlarmType? = null,
    val showCrudProcessingDialog: <PERSON>olean = false,
    val showDeleteAlarmDialog: Boolean = false,
    val showInterstitialAdLoadingDialog: Boolean = false,
    val showExitEditorTipsDialog: Boolean = false,
) {
    companion object {
        val Empty = AlarmEditorViewState()
    }
}
