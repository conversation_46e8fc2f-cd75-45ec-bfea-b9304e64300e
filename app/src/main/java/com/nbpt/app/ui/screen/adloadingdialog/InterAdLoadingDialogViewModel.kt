package com.nbpt.app.ui.screen.adloadingdialog

import androidx.lifecycle.ViewModel
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class InterAdLoadingDialogViewModel : ViewModel(), ContainerHost<InterAdLoadingViewState, Unit> {
    override val container: Container<InterAdLoadingViewState, Unit> =
        container(InterAdLoadingViewState())

    fun hideDialog() = intent {
        reduce { state.copy(displayLoading = false) }
    }
}