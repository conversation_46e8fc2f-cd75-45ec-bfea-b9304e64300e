package com.nbpt.app.ui.screen.heartraterecordnotes

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.data.db.dao.HrRecordsNoteDao
import com.nbpt.app.data.db.model.HrRecordsNoteEntity
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class HeartRateRecordNotesViewModel(
    savedStateHandle: SavedStateHandle,
    private val noteDao: HrRecordsNoteDao
) : ViewModel(),
    ContainerHost<HeartRateRecordNotesViewState, Unit> {

    override val container: Container<HeartRateRecordNotesViewState, Unit> =
        container(HeartRateRecordNotesViewState.Empty)

    private val navArgs = savedStateHandle.navArgs<HeartRateRecordNotesNavArgs>()

    init {
        intent {
            reduce { state.copy(noteSelection = navArgs.notesSelection ?: emptyList()) }
        }

        loadNotes()
    }

    fun loadNotes() = intent {
        val notes = noteDao.fetchAllNotesFlow().first()

        reduce { state.copy(notes = notes) }
    }

    fun onSelect(note: HrRecordsNoteEntity, selected: Boolean) = intent {
        val newSelection = if (selected) {
            state.noteSelection.toMutableList().apply { add(note.content) }
        } else {
            state.noteSelection.toMutableSet().apply { remove(note.content) }.toList()
        }

        reduce { state.copy(noteSelection = newSelection) }
    }
}
