package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.data.adt.BmiStatus
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bmi.BmiTheme
import com.nbpt.app.ui.theme.bodyWidth

@Composable
fun BmiTypeDialog(
    onDismiss: () -> Unit,
) {
    BmiTheme {
        AlertDialog(
            onDismissRequest = onDismiss,
            confirmButton = {
                CardButton(
                    text = stringResource(R.string.text_ok), onClick = onDismiss, modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 22.dp),
                    containerBrush = AppTheme.Color.BmiBrush
                )
            },
            title = {
                Text(
                    text = "BMI Type",
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.bodyWidth(),
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                ) {
                    BmiStatus.entries.forEachIndexed { index, bmiStatus ->
                        BmiTypeItem(bmiStatus = bmiStatus)

                        if (index != BmiStatus.entries.lastIndex) {
                            BlankSpacer(height = Layout.bodyMargin)
                        }
                    }
                }
            },
            containerColor = AppTheme.Color.BmiBackground
        )
    }
}

@Composable
fun BmiTypeItem(
    bmiStatus: BmiStatus,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        BlankWidthIn(min = Layout.gutter, max = Layout.bodyMargin)

        Surface(
            shape = CircleShape,
            color = bmiStatus.color,
            modifier = Modifier.size(size = 24.dp)
        ) {}

        BlankSpacer(width = Layout.bodyMargin)

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = stringResource(id = bmiStatus.titleStringId),
                style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary)
            )
            BlankSpacer(height = Layout.gutter / 2)
            Text(
                text = stringResource(id = bmiStatus.descriptionStringId),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = AppTheme.Color.textSecondary,
                    fontSize = 13.sp
                )
            )
        }
    }
}

@Preview
@Composable
private fun BmiTypeDialogPreview() {
    BmiTheme {
        BmiTypeDialog({})
    }
}