package com.nbpt.app.ui.screen.bpnotesmanager

import androidx.lifecycle.ViewModel
import com.nbpt.app.data.db.dao.BpRecordsNoteDao
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.nbpt.app.data.pojo.BpRecordsNote
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BpNotesManagerViewModel(
    private val noteDao: BpRecordsNoteDao
) : ViewModel(), ContainerHost<BpNotesManagerViewState, BpNotesManagerSideEffect> {

    override val container: Container<BpNotesManagerViewState, BpNotesManagerSideEffect> =
        container(BpNotesManagerViewState.Empty)

    init {
        intent {
            val notes = noteDao.fetchAllNotesFlow().first()

            reduce { state.copy(notes = notes) }
        }
    }

    private val pendingAddNotes = mutableListOf<BpRecordsNoteEntity>()
    val pendingDeleteNotes = mutableListOf<BpRecordsNoteEntity>()

    fun onAdd(
        noteText: String
    ) = intent {
        val noteSingle = BpRecordsNote(content = noteText.trim()).toDbEntity()

        val newNotes = listOf(noteSingle) + state.notes

        pendingAddNotes.add(noteSingle)

        reduce { state.copy(notes = newNotes) }

        onDismissAddNoteDialog()
    }

    fun onDelete(note: BpRecordsNoteEntity) = intent {
        val newNotes = state.notes.toMutableList().apply {
            remove(note)
        }

        pendingDeleteNotes.add(note)
        pendingAddNotes.remove(note)

        reduce { state.copy(notes = newNotes) }
    }

    fun onSave() = intent {
        reduce { state.copy(notesSaving = true) }

        if (pendingDeleteNotes.isNotEmpty()) {
            noteDao.delete(pendingDeleteNotes)
        }

        if (pendingAddNotes.isNotEmpty()) {
            noteDao.add(pendingAddNotes)
        }

        reduce { state.copy(notesSaving = false) }

        postSideEffect(BpNotesManagerSideEffect.NavUp)
    }

    fun onShowAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = true) }
    }

    fun onDismissAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = false) }
    }
}
