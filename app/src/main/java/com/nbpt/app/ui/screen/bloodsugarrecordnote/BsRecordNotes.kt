package com.nbpt.app.ui.screen.bloodsugarrecordnote

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.EditNote
import androidx.compose.material3.ElevatedFilterChip
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.R
import com.nbpt.app.data.db.model.BsRecordsNoteEntity
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.bloodsugarrecordeditor.BsRecordEditorRecipientEvent
import com.nbpt.app.ui.screen.destinations.BsRecordNotesManagerDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bs.BsTheme
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.result.ResultBackNavigator
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState

data class RecordNotesNavArgs(
    val notesSelection: ArrayList<String>? = null,
)

@OptIn(ExperimentalLayoutApi::class)
@Destination(
    style = AdaptiveHeightDialog::class,
    navArgsDelegate = RecordNotesNavArgs::class
)
@Composable
fun BsRecordNotes(
    navigator: DestinationsNavigator,
    resultNavigator: ResultBackNavigator<BsRecordEditorRecipientEvent>,
    viewModel: BsRecordNotesViewModel = koinViewModel()
) {
    BsTheme {
        val viewState by viewModel.collectAsState()

        Surface(
            modifier = Modifier
                .navigationBarsPadding()
                .bodyWidth()
                .width(440.dp)
                .padding(horizontal = 40.dp),
            shape = RoundedCornerShape12Dp,
            color = AppTheme.Color.BsBackground
        ) {
            Column {
                BlankSpacer(height = 16.dp)

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Layout.bodyMargin),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.text_note) + " : ",
                        modifier = Modifier.weight(1f)
                    )

                    IconButton(
                        onClick = navigator::navigateUp,
                        colors = IconButtonDefaults.iconButtonColors(
                            containerColor = Color.Transparent,
                            contentColor = AppTheme.Color.textPrimary
                        ),
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.Close,
                            contentDescription = "close",
                            modifier = Modifier.padding(2.dp)
                        )
                    }
                }

                BlankSpacer(height = 4.dp)

                FlowRow(
                    modifier = Modifier.padding(horizontal = Layout.bodyMargin),
                    verticalArrangement = Arrangement.Center
                ) {
                    EditNoteChip(
                        onClick = { navigator.navigate(BsRecordNotesManagerDestination) },
                        modifier = Modifier.padding(horizontal = 2.dp)
                    )

                    viewState.notes.forEach { note ->
                        NoteChip(
                            note = note,
                            selected = viewState.noteSelection.contains(note.content),
                            onClick = viewModel::onSelect,
                            modifier = Modifier.padding(horizontal = (2).dp)
                        )
                    }
                }

                CardButton(
                    text = stringResource(R.string.text_save),
                    onClick = {
                        resultNavigator.navigateBack(
                            BsRecordEditorRecipientEvent.NotesSelectionFinish(
                                ArrayList(viewState.noteSelection)
                            )
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 18.dp, vertical = 8.dp),
                    containerBrush = AppTheme.Color.BsBrush
                )

                BlankSpacer(height = 10.dp)
            }

        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun NoteChip(
    note: BsRecordsNoteEntity,
    selected: Boolean,
    onClick: (BsRecordsNoteEntity, Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier, contentAlignment = Alignment.Center) {
        ElevatedFilterChip(
            selected = selected,
            onClick = { onClick(note, !selected) },
            label = { Text(text = note.content) },
            elevation = FilterChipDefaults.filterChipElevation(),
            shape = CircleShape,
            colors = FilterChipDefaults.elevatedFilterChipColors(
                containerColor = AppTheme.Color.White,
                labelColor = AppTheme.Color.textPrimary.copy(alpha = .74f),
                iconColor = AppTheme.Color.textPrimary.copy(alpha = .74f),
                selectedContainerColor = AppTheme.Color.BsPrimary,
                selectedLabelColor = AppTheme.Color.White,
            ),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditNoteChip(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier, contentAlignment = Alignment.Center) {
        FilterChip(
            selected = false,
            onClick = onClick,
            leadingIcon = {
                Icon(
                    imageVector = Icons.Rounded.EditNote,
                    contentDescription = "edit note"
                )
            },
            label = { Text(text = stringResource(R.string.text_add_or_edit)) },
            shape = CircleShape,
            colors = FilterChipDefaults.elevatedFilterChipColors(
                containerColor = AppTheme.Color.White,
                labelColor = AppTheme.Color.textPrimary.copy(alpha = .94f),
                iconColor = AppTheme.Color.textPrimary.copy(alpha = .94f),
            )
        )
    }
}
