package com.nbpt.app.ui.theme.bs
import androidx.compose.ui.graphics.Color
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.seed

val md_theme_light_primary = AppTheme.Color.BsPrimary
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color.White
val md_theme_light_onPrimaryContainer = seed
val md_theme_light_secondary = Color(0xFF6C48B8)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = md_theme_light_primaryContainer
val md_theme_light_onSecondaryContainer = Color(0xFF24005B)
val md_theme_light_tertiary = Color(0xFF6C48B8)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = md_theme_light_primaryContainer
val md_theme_light_onTertiaryContainer = seed
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFFFBFF)
val md_theme_light_onBackground = seed
val md_theme_light_surface = md_theme_light_primaryContainer
val md_theme_light_onSurface = seed
val md_theme_light_surfaceVariant = Color(0xFFE7E0EB)
val md_theme_light_onSurfaceVariant = seed
val md_theme_light_outline = Color(0xFF7A757F)
val md_theme_light_inverseOnSurface = Color(0xFFF1EFFF)
val md_theme_light_inverseSurface = Color(0xFF1E2578)
val md_theme_light_inversePrimary = Color(0xFFD1BCFF)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = md_theme_light_primaryContainer
val md_theme_light_outlineVariant = Color(0xFFCBC4CF)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFFD1BCFF)
val md_theme_dark_onPrimary = Color(0xFF3C0C88)
val md_theme_dark_primaryContainer = Color(0xFF532D9F)
val md_theme_dark_onPrimaryContainer = Color(0xFFEADDFF)
val md_theme_dark_secondary = Color(0xFFD1BCFF)
val md_theme_dark_onSecondary = Color(0xFF3C0C88)
val md_theme_dark_secondaryContainer = Color(0xFF532D9F)
val md_theme_dark_onSecondaryContainer = Color(0xFFEADDFF)
val md_theme_dark_tertiary = Color(0xFFD1BCFF)
val md_theme_dark_onTertiary = Color(0xFF3C0C88)
val md_theme_dark_tertiaryContainer = Color(0xFF532D9F)
val md_theme_dark_onTertiaryContainer = Color(0xFFEADDFF)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF030865)
val md_theme_dark_onBackground = Color(0xFFE0E0FF)
val md_theme_dark_surface = Color(0xFF030865)
val md_theme_dark_onSurface = Color(0xFFE0E0FF)
val md_theme_dark_surfaceVariant = Color(0xFF49454E)
val md_theme_dark_onSurfaceVariant = Color(0xFFCBC4CF)
val md_theme_dark_outline = Color(0xFF948F99)
val md_theme_dark_inverseOnSurface = Color(0xFF030865)
val md_theme_dark_inverseSurface = Color(0xFFE0E0FF)
val md_theme_dark_inversePrimary = Color(0xFF6C48B8)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFFD1BCFF)
val md_theme_dark_outlineVariant = Color(0xFF49454E)
val md_theme_dark_scrim = Color(0xFF000000)
