package com.nbpt.app.ui.screen.bloodsugar

import androidx.lifecycle.ViewModel
import com.nbpt.app.common.calculate.mmolLToMgDL
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.data.db.dao.BsRecordDao
import com.nbpt.app.data.db.dao.mockData
import com.nbpt.app.data.healtharticles.HealthArticleType
import com.nbpt.app.data.healtharticles.HealthArticles
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BloodSugarViewModel(
    private val bsRecordDao: BsRecordDao,
    private val userBehaviorDataStore: UserBehaviorDataStore
) : ViewModel(), ContainerHost<BloodSugarViewState, Unit> {

    override val container: Container<BloodSugarViewState, Unit> = container(BloodSugarViewState())

    fun onRefresh() = intent {
        val bsUnit = userBehaviorDataStore.bsUnit

//        val records = bsRecordDao.mockData()
        val records = bsRecordDao.fetchAllRecordsFlow().first()

        val avgBsMmolL =
            records.map { it.mmolL }.takeIf { it.isNotEmpty() }?.average()?.toFloat()
        val avgMgDl = avgBsMmolL?.let { mmolLToMgDL(avgBsMmolL) }

        val latestMmolL = records.firstOrNull()?.mmolL
        val latestMgDl = latestMmolL?.let { mmolLToMgDL(latestMmolL) }

        val bsArticles = HealthArticles.fetch()[HealthArticleType.BS]

        val bsStatMap: MutableMap<BsStatus, Int> = mutableMapOf()

        when (bsUnit) {
            BsUnit.SI -> {
                records.forEach { bsRecord ->
                    val statue = BsStatus.fromMmolL(bsRecord.mmolL)

                    bsStatMap[statue] = (bsStatMap[statue] ?: 0) + 1
                }
            }

            BsUnit.NonSI -> {
                records.forEach { bsRecord ->
                    val mgDl = mmolLToMgDL(bsRecord.mmolL)
                    val statue = BsStatus.fromMgDl(mgDl)

                    bsStatMap[statue] = (bsStatMap[statue] ?: 0) + 1
                }
            }
        }

        reduce {
            state.copy(
                bsUnit = bsUnit,
                avgBsMmolL = avgBsMmolL,
                avgBsMgDl = avgMgDl,
                latestBsMmolL = latestMmolL,
                latestBsMgDl = latestMgDl,
                bsRecords = records,
                bsArticles = bsArticles,
                bsStatMap = bsStatMap,
            )
        }
    }
}
