package com.nbpt.app.ui.screen.info

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.data.healtharticles.HealthArticles
import com.ramcosta.composedestinations.spec.Direction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class InfoViewModel(
//    private val interstitialAdManager: InterstitialAdManager
) : ViewModel(), ContainerHost<InfoViewState, InfoSideEffect>, KoinComponent {



    override val container: Container<InfoViewState, InfoSideEffect> =
        container(InfoViewState.Empty)

    @Suppress("MemberVisibilityCanBePrivate")
    fun onFetch() = intent {
        val articleMap = HealthArticles.fetch()
        reduce { state.copy(articleMap = articleMap) }
    }

//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    private var showInterAdAfterDestination: Direction? = null
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    showInterAdAfterDestination?.let { destination ->
//                        showInterAdContainer = null
//                        showInterAdAfterDestination = null
//
//                        postSideEffect(InfoSideEffect.NavTo(destination))
//                    }
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    showInterAdAfterDestination?.let { destination ->
//                        showInterAdContainer = null
//                        showInterAdAfterDestination = null
//
//                        postSideEffect(InfoSideEffect.NavTo(destination))
//                    }
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
}
