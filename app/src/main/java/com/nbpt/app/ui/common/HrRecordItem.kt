package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.adt.HrStatus
import com.nbpt.app.data.db.model.HrRecordEntity
import com.nbpt.app.data.db.model.notesText
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import kotlinx.datetime.Clock
import java.time.format.FormatStyle

@Composable
fun HrRecordItem(
    hrRecord: HrRecordEntity,
    modifier: Modifier = Modifier,
    onEditClick: (() -> Unit)? = null
) {
    val status = HrStatus.from(hrRecord.heartRateBpm)

    Surface(
        onClick = { onEditClick?.invoke() },
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
    ) {
        val gutter = Layout.gutter
        val bodyMargin = Layout.bodyMargin

        Row(
            Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
                .height(IntrinsicSize.Max),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 16.dp)

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = hrRecord.heartRateBpm.toString(),
                    style = MaterialTheme.typography.titleLarge
                )

                BlankSpacer(height = 1.dp)

                Text(
                    text = stringResource(id = R.string.text_bpm),
                    style = MaterialTheme.typography.titleSmall.copy(color = AppTheme.Color.textSecondaryDark)
                )
            }

            BlankSpacer(width = 14.dp)

            Surface(
                modifier = Modifier
                    .width(4.dp)
                    .fillMaxHeight(),
                color = status.color,
                shape = CircleShape,
                content = {}
            )

            BlankSpacer(width = 14.dp)

            Column(
                Modifier
                    .weight(1f)
            ) {
                val bodySmall = MaterialTheme.typography.bodySmall.copy(
                    color = AppTheme.Color.textSecondary,
                    fontWeight = FontWeight.Normal
                )
                val bodyMedium = MaterialTheme.typography.bodyMedium

                val dateTime = hrRecord.instant.toLocalDatetime()
                val dateText = dateTime.date.isoFormat(FormatStyle.MEDIUM)
                val timeText = dateTime.time.isoFormat()
                val dateTimeText = "$timeText  $dateText"

                val blankSpacerHeight = if (hrRecord.notes.isEmpty()) 8.dp else 2.dp

                Text(
                    text = stringResource(id = status.titleStringId),
                    style = bodyMedium.copy(
                        fontSize = (bodyMedium.fontSize.value + 3).sp
                    )
                )

                BlankSpacer(height = blankSpacerHeight)

                Text(
                    text = dateTimeText,
                    style = bodySmall
                )

                val notesText = hrRecord.notesText

                if (notesText.isNotEmpty()) {
                    BlankSpacer(height = blankSpacerHeight)

                    Text(
                        text = notesText,
                        style = bodySmall
                    )
                }

            }

            if (onEditClick != null) {
                IconButton(
                    onClick = onEditClick,
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Edit,
                        contentDescription = "edit",
                    )
                }

                BlankSpacer(width = 6.dp)
            }
        }
    }
}

@Preview
@Composable
fun HrRecordItemPreview() {
    HrRecordItem(
        hrRecord = HrRecordEntity(
            uuid = "",
            instant = Clock.System.now(),
            gender = 0,
            age = 50,
            heartRateBpm = 170,
            notes = listOf("a", "b", "c"),
//            notes = emptyList()
        ),
        onEditClick = {}
    )
}
