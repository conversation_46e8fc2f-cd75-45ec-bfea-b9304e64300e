package com.nbpt.app.ui.screen.guide

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class GuideViewModel(
//    private val interstitialAdManager: InterstitialAdManager
) : ViewModel(),
    ContainerHost<GuideViewState, GuideSideEffect>,
    KoinComponent {

    override val container: Container<GuideViewState, GuideSideEffect> =
        container(GuideViewState.Empty)

//    fun onTryToShowInterAdAndNav(
//        activity: Activity,
//        navBlock: DestinationsNavigator.() -> Unit,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        showInterAdContainer = activity
//        showInterAdAfterNavBlock = navBlock
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }

//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    private var showInterAdAfterNavBlock: (DestinationsNavigator.() -> Unit)? = null
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    showInterAdAfterNavBlock?.let { navBlock ->
//                        postSideEffect(GuideSideEffect.NavBlock(navBlock))
//                    }
//
//                    showInterAdContainer = null
//                    showInterAdAfterNavBlock = null
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    showInterAdAfterNavBlock?.let { navBlock ->
//                        postSideEffect(GuideSideEffect.NavBlock(navBlock))
//                    }
//
//                    showInterAdContainer = null
//                    showInterAdAfterNavBlock = null
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
}
