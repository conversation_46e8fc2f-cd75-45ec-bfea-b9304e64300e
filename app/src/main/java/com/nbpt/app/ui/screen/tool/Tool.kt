package com.nbpt.app.ui.screen.tool

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowForwardIos
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.android.showToast
import com.nbpt.app.common.skipSplash
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.camera.TakePictureContainer
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.BmiCalculatorDestination
import com.nbpt.app.ui.screen.destinations.BpDestination
import com.nbpt.app.ui.screen.destinations.BsDestination
import com.nbpt.app.ui.screen.destinations.HrDestination
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.autoMirror
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun Tool(
    navigator: DestinationsNavigator,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    Scaffold(
        topBar = {
            Column {
                TopAppBar(title = {
                    Text(text = stringResource(R.string.text_home))
                })

                SmartRectAd(
                    pageType = SmartAdPageType.HOME_TOP_BAR,
                    bannerAdPlace = BannerAdPlace.HOME,
                    nativeAdPlace = NativeAdPlace.Home,
                )
            }

        },
        contentWindowInsets = ScaffoldDefaults.contentWindowInsets.exclude(WindowInsets.navigationBars)
    ) {
        val halfItemWidth = (LocalConfiguration.current.screenWidthDp - 16 * 2 - 12) / 2

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(it)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)

            HrItem(
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("home", onAdLoadingAfter = {
                            navigator.navigate(HrDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(HrDestination) },
                            adPlaceName = "home"
                        )
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
            )


            BlankSpacer(height = 12.dp)

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                ToolItem(
                    onClick = {
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("home", onAdLoadingAfter = {
                                navigator.navigate(BpDestination)
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(BpDestination) },
                                adPlaceName = "home"
                            )
                        }
                    },
                    title = stringResource(id = R.string.text_blood_pressure) + "\n\n",
                    bgPainter = painterResource(id = R.drawable.bg_item_bp2),
                    modifier = Modifier.width(halfItemWidth.dp)
                )

                BlankSpacer(width = 12.dp)

                ToolItem(
                    onClick = {
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("home", onAdLoadingAfter = {
                                navigator.navigate(BsDestination)
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(BsDestination) },
                                adPlaceName = "home"
                            )
                        }
                    },
                    title = stringResource(id = R.string.text_blood_sugar) + "\n\n",
                    bgPainter = painterResource(id = R.drawable.bg_item_bs2),
                    modifier = Modifier.width(halfItemWidth.dp)
                )
            }

            BlankSpacer(height = 12.dp)

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                ToolItem(
                    onClick = {
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("home", onAdLoadingAfter = {
                                navigator.navigate(BmiCalculatorDestination)
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(BmiCalculatorDestination) },
                                adPlaceName = "home"
                            )
                        }
                    },
                    title = "BMI" + "\n\n",
                    bgPainter = painterResource(id = R.drawable.bg_item_bmi),
                    modifier = Modifier.width(halfItemWidth.dp),
                )

                BlankSpacer(width = 12.dp)

                ToolItem(
                    onClick = {
                        val destination = AlarmGroupsDestination(showInterAdWhenBack = true)
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("enter_alarm", onAdLoadingAfter = {
                                navigator.navigate(destination)
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(destination) },
                                adPlaceName = "enter_alarm"
                            )
                        }
                    },
                    title = stringResource(id = R.string.text_alarm) + "\n\n",
                    bgPainter = painterResource(id = R.drawable.bg_item_alarm),
                    modifier = Modifier.width(halfItemWidth.dp),
                )
            }

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.HOME_CONTENT,
                bannerAdPlace = BannerAdPlace.HOME,
                nativeAdPlace = NativeAdPlace.Home,
            )
        }
    }
}

@Composable
fun ToolItem(
    onClick: () -> Unit,
    title: String,
    bgPainter: Painter,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape12Dp)
            .clickable(onClick = onClick),
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Image(
                painter = bgPainter,
                contentDescription = null,
                modifier = Modifier
                    .autoMirror()
                    .height(120.dp)
                    .fillMaxWidth(),
                contentScale = ContentScale.FillBounds
            )
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = 17.sp,
                    color = Color.White
                ),
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = 14.dp, top = 20.dp)
            )
        }
    }
}

@Composable
private fun HrItem(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape12Dp)
            .clickable(onClick = onClick),
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_item_hr2),
                contentDescription = null,
                modifier = Modifier
                    .autoMirror()
                    .height(126.dp)
                    .fillMaxWidth(),
                contentScale = ContentScale.FillBounds
            )
            Text(
                text = stringResource(id = R.string.text_heart_rate),
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = 20.sp,
                    color = Color.White
                ),
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = 16.dp, top = 20.dp)
            )


            val screenWidth = LocalConfiguration.current.screenWidthDp
            val lottieAnimationOffsetX =
                remember(screenWidth) { calculateOffsetX(screenWidth.toDouble()) }

            val composition by rememberLottieComposition(
                spec = LottieCompositionSpec.Asset("hr_hw.json")
            )
            LottieAnimation(
                composition = composition,
                iterations = Int.MAX_VALUE,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = lottieAnimationOffsetX.dp, y = 4.dp)
                    .autoMirror()
                    .height(88.dp),
            )

            Row(
                modifier = Modifier
                    .padding(start = 16.dp, bottom = 20.dp)
                    .align(Alignment.BottomStart)
                    .clip(CircleShape)
                    .background(Color.White),
                verticalAlignment = Alignment.CenterVertically
            ) {
                BlankSpacer(12.dp)

                Text(
                    text = stringResource(id = R.string.text_measure_now),
                    modifier = Modifier.padding(vertical = 4.dp),
                    fontSize = 13.sp,
                    color = AppTheme.Color.HrPrimary
                )

                BlankSpacer(3.dp)

                Icon(
                    imageVector = Icons.AutoMirrored.Rounded.ArrowForwardIos,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp),
                    tint = AppTheme.Color.HrPrimary,
                )

                BlankSpacer(9.dp)
            }
        }
    }
}

fun calculateOffsetX(width: Double): Double {
    // 二次函数系数: y = a*x^2 + b*x + c
    val a = 0.0009077705f
    val b = -0.9253812636
    val c = 254.49019608

    // 使用二次函数计算结果
    return a * width * width + b * width + c
}

@Preview
@Composable
private fun ToolPreview() {
    AppMd3Theme {
        Tool(
            navigator = EmptyDestinationsNavigator,
            admobInterstitialAdViewModel = koinViewModel()
        )
    }
}