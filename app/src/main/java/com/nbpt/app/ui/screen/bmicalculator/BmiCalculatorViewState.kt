package com.nbpt.app.ui.screen.bmicalculator

import com.nbpt.app.data.adt.BmiCalculateUnit
import com.nbpt.app.data.pojo.BmiCalculatorCache

data class BmiCalculatorViewState(
    val bmiCalculateUnit: BmiCalculateUnit = BmiCalculateUnit.Imperial,

    val ft: Int = 5,
    val `in`: Int = 7,
    val lbs: Float = 150f,

    val cm: Int = 170,
    val kg: Float = 68f,

    val hasCalculate: Boolean = false,
) {
    fun toBmiCalculatorCache() = BmiCalculatorCache(
        bmiCalculateUnit, ft, `in`, lbs, cm, kg
    )

    fun importCache(cache: BmiCalculatorCache) = this.copy(
        bmiCalculateUnit = cache.bmiCalculateUnit,
        ft = cache.ft,
        `in` = cache.`in`,
        lbs = cache.lbs,
        cm = cache.cm,
        kg = cache.kg
    )
}
