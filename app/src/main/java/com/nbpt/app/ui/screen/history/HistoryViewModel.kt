package com.nbpt.app.ui.screen.history

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.data.db.dao.BpRecordDao
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class HistoryViewModel(
    private val bpRecordDao: BpRecordDao,
//    private val interstitialAdManager: InterstitialAdManager
) : ViewModel(),
    ContainerHost<HistoryViewState, HistorySideEffect>,
    KoinComponent {

    override val container: Container<HistoryViewState, HistorySideEffect> =
        container(HistoryViewState.Empty)

    init {
        onRefresh()
    }

    fun onRefresh() = intent {
        val bpRecords = bpRecordDao.fetchAllRecordsFlow().first()

        reduce {
            state.copy(
                bpRecords = bpRecords,
                showNoRecordsContent = bpRecords.isEmpty()
            )
        }
    }

//    fun onBack(activity: Activity) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        showInterAdContainer = activity
//
//        if (!hasInterAdShowing) {
//            interstitialAdManager.tryToShowAd(activity, "exit_bp_list")
//        }
//    }

//    @Volatile
//    private var hasInterAdShowing = false
//
//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(HistorySideEffect.NavUp)
//                    }
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        if (!hasInterAdShowing) {
//                            interstitialAdManager.tryToShowAd(containerActivity, null)
//                        }
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(HistorySideEffect.NavUp)
//                    }
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {
//                    if (showInterAdContainer != null) {
//                        hasInterAdShowing = true
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//    }
}
