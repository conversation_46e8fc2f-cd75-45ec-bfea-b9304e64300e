package com.nbpt.app.ui.screen.guide

import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeightIn
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.ListAdaptiveRectangleAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.NavigateAction
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.destinations.HeartRateMeasureDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.HrDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootNavGraph
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

sealed class GuideStep(
    @StringRes val titleStringId: Int,
    @StringRes val contentStringId: Int,
    @DrawableRes val fgResId: Int,
) {
    data object First : GuideStep(
        titleStringId = R.string.text_title_guide_step1,
        contentStringId = R.string.text_content_guide_step1,
        fgResId = R.drawable.img_guide_1
    )

    data object Second : GuideStep(
        titleStringId = R.string.text_title_guide_step2,
        contentStringId = R.string.text_content_guide_step2,
        fgResId = R.drawable.img_guide_2
    )

    data object Third : GuideStep(
        titleStringId = R.string.text_title_guide_step3,
        contentStringId = R.string.text_content_guide_step3,
        fgResId = R.drawable.img_guide_3
    )

    companion object {
        val values = listOf(First, Second, Third)
    }
}

@RootNavGraph(start = false)
@Destination
@Composable
fun Guide(
    navigator: DestinationsNavigator,
) {
    val context = LocalContext.current
    val viewModel: GuideViewModel = koinViewModel()

    val userBehaviorDataStore: UserBehaviorDataStore = koinInject()
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    val scope = rememberCoroutineScope()

    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f,
        pageCount = { GuideStep.values.size }
    )
    val viewState by viewModel.collectAsState()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    val onBack = {
        if (pagerState.currentPage != 0) {
            scope.launch {
                pagerState.animateScrollToPage(
                    page = pagerState.currentPage - 1,
                    animationSpec = tween(200)
                )
            }
        }
    }

    BackHandler(onBack = onBack)

    val onFinish = {
        scope.launch {
            userBehaviorDataStore.setGuideFinish()

            val navAction: NavigateAction = {
                popBackStack()
                navigate(HomeDestination())
                navigate(HrDestination)
                navigate(HeartRateMeasureDestination)
            }

            if (useLegacyAd) {
                interstitialAdManager.tryToShowAd(
                    "guide",
                    whenShowingExecuteAction = false,
                    onAdLoadingAfter = {
                        navAction(navigator)
                    })
            } else {
                admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                    activity = context.findActivity(),
                    navAction = navAction,
                    adPlaceName = "guide"
                )
            }
        }
    }

    val onNext = {
        if (pagerState.currentPage != GuideStep.values.lastIndex) {
            scope.launch {
                pagerState.animateScrollToPage(
                    page = pagerState.currentPage + 1,
                    animationSpec = tween(200)
                )
            }
            logEventRecord("click_intro_next")
        } else {
            onFinish()
            logEventRecord("click_intro_start_now")
        }
        Unit
    }

    val onSkip = {
        onFinish()
        logEventRecord("click_intro_skip")
        Unit
    }

    Column(modifier = Modifier.navigationBarsPadding()) {
        HorizontalPager(state = pagerState, modifier = Modifier.weight(1f)) {
            val currentGuideStep = GuideStep.values[it]

            GuideScreen(
                viewState = viewState,
                guideStep = currentGuideStep,
                onBack = onBack,
                onNext = onNext,
                onSkip = onSkip,
                modifier = Modifier.fillMaxSize()
            )
        }

        ListAdaptiveRectangleAd(
            bannerAdPlace = BannerAdPlaceholder.GUIDE,
            nativeAdPlace = NativeAdPlaceholder.Guide,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun GuideScreen(
    viewState: GuideViewState,
    guideStep: GuideStep,
    onBack: () -> Unit,
    onNext: () -> Unit,
    onSkip: () -> Unit,
    modifier: Modifier = Modifier,
) {

    Scaffold(
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
        ) {
            Spacer(modifier = Modifier.weight(1f))

            Image(
                painter = painterResource(id = guideStep.fgResId),
                contentDescription = null,
                modifier = Modifier
                    .requiredHeightIn(max = 320.dp)
                    .padding(horizontal = 16.dp)
                    .padding(top = 24.dp)
                    .bodyWidth()
            )



            Spacer(modifier = Modifier.weight(1f))

            Text(
                text = stringResource(id = guideStep.contentStringId),
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .bodyWidth()
                    .padding(horizontal = 48.dp)
            )

            Spacer(modifier = Modifier.weight(1f))

            GuideScreenIndicator(guideStep = guideStep, modifier = Modifier.bodyWidth())

            Spacer(modifier = Modifier.weight(1f))

            Row(modifier = Modifier.bodyWidth()) {

                if (guideStep != GuideStep.First) {
                    CardButton(
                        text = stringResource(R.string.text_back),
                        onClick = onBack,
                        modifier = Modifier.requiredWidthIn(min = 130.dp),
                        containerBrush = Brush.horizontalGradient(
                            listOf(
                                AppTheme.Color.White,
                                AppTheme.Color.White
                            )
                        ),
                        contentColor = AppTheme.Color.textSecondary
                    )
                    BlankSpacer(width = Layout.bodyMargin)
                }


                CardButton(
                    text = if (guideStep == GuideStep.Third) stringResource(R.string.text_start_now) else stringResource(
                        R.string.text_next
                    ),
                    onClick = onNext,
                    modifier = Modifier.requiredWidthIn(min = 130.dp)
                )
            }

            BlankSpacer(height = 12.dp)


            if (guideStep != GuideStep.Third) {
                TextButton(onClick = onSkip, modifier = Modifier.bodyWidth()) {
                    Text(
                        text = stringResource(R.string.text_skip_for_now),
                        style = MaterialTheme.typography.bodySmall.copy(color = AppTheme.Color.textSecondary),
                    )
                }
            } else { // like a placeholder
                TextButton(onClick = {}, modifier = Modifier.bodyWidth(), enabled = false) {
                    Text(
                        text = "",
                        style = MaterialTheme.typography.bodySmall.copy(color = AppTheme.Color.textSecondary),
                    )
                }
            }

            BlankSpacer(height = 10.dp)
        }

    }
}

@Composable
private fun GuideScreenIndicator(
    guideStep: GuideStep,
    modifier: Modifier = Modifier,
) {
    Row(modifier) {
        GuideStep.values.forEachIndexed { index, step ->
            val indicatorColor = if (step == guideStep)
                AppTheme.Color.Primary else AppTheme.Color.Primary.copy(.22f)

            GuideScreenIndicatorSelect(color = indicatorColor)

            if (index != GuideStep.values.lastIndex) {
                BlankSpacer(width = 4.dp)
            }
        }
    }
}

@Composable
private fun GuideScreenIndicatorSelect(
    color: Color,
    modifier: Modifier = Modifier,
    width: Dp = 22.dp,
    height: Dp = 4.dp
) {
    Surface(
        color = color,
        shape = CircleShape,
        modifier = modifier.size(width = width, height = height)
    ) {}
}

@Preview
@Composable
private fun GuidePreview() {
    Guide(navigator = EmptyDestinationsNavigator)
}
