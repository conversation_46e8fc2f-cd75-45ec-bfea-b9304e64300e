package com.nbpt.app.ui.screen.alarmguide

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.logEventRecord
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.alarm.AlarmTheme
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator

@Destination(
    style = AdaptiveHeightDialog::class,
)
@Composable
fun AlarmGuide(
    navigator: DestinationsNavigator,
) {

    AlarmTheme {
        Column(
            modifier = Modifier
                .navigationBarsPadding()
                .padding(horizontal = 24.dp)
                .fillMaxWidth()
        ) {
            Surface(shape = RoundedCornerShape12Dp) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Layout.bodyMargin)
                        .padding(top = Layout.bodyMargin * 1.8f, bottom = Layout.gutter),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.img_alarm),
                        contentDescription = null,
                        modifier = Modifier
                            .size(152.dp)
                            .aspectRatio(ratio = 280 / 180f, matchHeightConstraintsFirst = true),
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    Text(
                        text = stringResource(R.string.text_content_set_alarm_guide_tips),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 15.sp)
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    CardButton(
                        text = stringResource(R.string.text_set),
                        onClick = {
                            navigator.popBackStack()
                            navigator.navigate(AlarmGroupsDestination())
                            logEventRecord("click_alarm")
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 44.dp),
                        containerBrush = AppTheme.Color.AlarmBrush
                    )


                    TextButton(onClick = { navigator.navigateUp() }) {
                        Text(
                            text = stringResource(R.string.text_not_now),
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight.Normal,
                                color = AppTheme.Color.textSecondary.copy(alpha = .6f)
                            ),
                        )
                    }
                }
            }

//        BlankSpacer(height = 6.dp)
//
//        NativeAd(place = NativeAdPlace.DialogAlarmGuideTips)
        }
    }
}

@Preview
@Composable
private fun AlarmGuidePreview() {
    AlarmTheme {
        Dialog(
            onDismissRequest = { },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            AlarmGuide(EmptyDestinationsNavigator)
        }
    }
}
