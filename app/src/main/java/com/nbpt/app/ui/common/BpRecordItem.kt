package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Edit
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.data.db.model.BpRecordEntity
import com.nbpt.app.data.db.model.notesText
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import kotlinx.datetime.Clock
import java.time.format.FormatStyle

@Composable
fun BpRecordItem(
    bpRecord: BpRecordEntity,
    modifier: Modifier = Modifier,
    onEditClick: (() -> Unit)? = null
) {
    val status = BpStatus.from(bpRecord.systolic, bpRecord.diastolic)

    Surface(
        onClick = { onEditClick?.invoke() },
        modifier = modifier.defShadow(),
        shape = RoundedCornerShape12Dp,
    ) {

        Row(
            Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Max)
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BlankSpacer(width = 16.dp)

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                val style = MaterialTheme.typography.titleLarge

                Text(
                    text = bpRecord.systolic.toString(),
                    style = style,
                )

                BlankSpacer(height = 2.dp)

                Surface(
                    modifier = Modifier
                        .width(42.dp)
                        .height(5.dp),
                    shape = CircleShape,
                    color = status.color,
                    content = {}
                )

                BlankSpacer(height = 2.dp)

                Text(
                    text = bpRecord.diastolic.toString(),
                    style = style
                )

            }

            BlankSpacer(width = 16.dp)

            Box(
                Modifier.weight(1f)
            ) {
                Column {
                    val bodySmall = MaterialTheme.typography.bodySmall.copy(
                        color = AppTheme.Color.textSecondary,
                        fontWeight = FontWeight.Normal
                    )
                    val bodyMedium = MaterialTheme.typography.bodyMedium

                    val dateTime = bpRecord.instant.toLocalDatetime()
                    val dateText = dateTime.date.isoFormat(FormatStyle.MEDIUM)
                    val timeText = dateTime.time.isoFormat()
                    val dateTimeText = "$timeText  $dateText"
                    val notesText = bpRecord.notesText
                    val hasNotes = notesText.isNotEmpty()

                    Text(
                        text = stringResource(id = status.titleStringId),
                        style = bodyMedium.copy(
                            fontWeight = FontWeight.ExtraBold,
                            fontSize = (bodyMedium.fontSize.value + 2).sp
                        )
                    )

                    if (hasNotes) {
                        BlankSpacer(height = 2.dp)
                    } else {
                        BlankSpacer(height = 8.dp)
                    }

                    Text(
                        text = dateTimeText,
                        style = bodySmall
                    )

                    if (hasNotes) {
                        BlankSpacer(height = 2.dp)

                        Text(
                            text = notesText,
                            style = bodySmall
                        )
                    }
                }

                Text(
                    text = "❤️ ${bpRecord.pulse}",
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 16.dp, bottom = 1.dp),
                    style = MaterialTheme.typography.bodyLarge
                )

            }


            if (onEditClick != null) {
                IconButton(
                    onClick = onEditClick
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Edit,
                        contentDescription = "edit",
                    )
                }

                BlankSpacer(width = 2.dp)
            }

        }
    }
}

@Preview
@Composable
fun BpRecordItemPreview() {
    BpRecordItem(
        bpRecord = BpRecordEntity(
            uuid = "",
            instant = Clock.System.now(),
            systolic = 120,
            diastolic = 80,
            pulse = 60,
            notes = listOf("a", "b", "c")
        ),
        onEditClick = {}
    )
}
