package com.nbpt.app.ui.common

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmType
import com.nbpt.app.androidcomponent.alarm.remindtorecord.entries
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.defShadow

typealias ExportDataType = RemindToRecordAlarmType

@Composable
fun ExportDataDialog(
    onDismiss: () -> Unit,
    onExport: (ExportDataType) -> Unit,
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth()
        ) {
            Surface(shape = RoundedCornerShape12Dp, color = AppTheme.Color.background) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Layout.bodyMargin)
                        .padding(bottom = Layout.bodyMargin),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BlankSpacer(height = 10.dp)

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.Close,
                            contentDescription = null,
                            tint = AppTheme.Color.textSecondary.copy(.8f),
                            modifier = Modifier.clickable { onDismiss() }
                        )
                    }

                    Text(
                        text = stringResource(R.string.text_export_data),
                        style = MaterialTheme.typography.titleLarge
                    )

                    BlankSpacer(height = 20.dp)

                    ExportDataType.entries.forEach {
                        ExportDataItem(
                            onClick = { exportDataType ->
                                onExport(exportDataType)
                                onDismiss()
                            },
                            exportDataType = it,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 32.dp)
                        )
                        BlankSpacer(height = 14.dp)
                    }

                }
            }

        }
    }
}

@Composable
private fun ExportDataItem(
    onClick: (ExportDataType) -> Unit,
    exportDataType: ExportDataType,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = { onClick(exportDataType) },
        modifier = modifier.defShadow(),
        shape = RoundedCornerShape12Dp,
        contentColor = AppTheme.Color.Primary,
        border = BorderStroke(2.4.dp, SolidColor(AppTheme.Color.Primary))
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = stringResource(id = exportDataType.typeStringId),
                modifier = Modifier
                    .padding(12.dp)
                    .bodyWidth(),
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Visible
            )
        }
    }
}

@Preview
@Composable
fun ExportDataDialogPreview() {
    AppMd3Theme {
        ExportDataDialog(onDismiss = { /*TODO*/ }, onExport = {})
    }
}