package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.nbpt.app.R
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth


@Composable
fun LinearProgressLoadingDialog(
    navUp: () -> Unit,
    title: String?,
    userDismissEnable: Boolean = true,
) {
    AlertDialog(
        onDismissRequest = navUp,
        title = {
            title?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.bodyWidth(),
                )
            }
        },
        text = {
            LinearProgressIndicator(Modifier.fillMaxWidth())
        },
        confirmButton = {},
        properties = DialogProperties(
            dismissOnClickOutside = userDismissEnable,
            dismissOnBackPress = userDismissEnable,
        )
    )
}

@Composable
fun CircularProgressLoadingDialog(
    navUp: () -> Unit,
    text: String? = null,
    userDismissEnable: Boolean = true,
) {
    Dialog(
        onDismissRequest = navUp,
        properties = DialogProperties(
            dismissOnClickOutside = userDismissEnable,
            dismissOnBackPress = userDismissEnable,
        )
    ) {
        Surface(shape = RoundedCornerShape12Dp) {
            Column(
                modifier = Modifier.padding(Layout.bodyMargin),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(52.dp),
//                    color = AppTheme.Color.Primary,
                )
                text?.let { _ ->
                    BlankSpacer(height = Layout.bodyMargin)
                    Text(
                        text = text,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }

        }
    }
}

@Composable
fun AdLoadingDialog(
    navUp: (() -> Unit)? = null,
    text: String = stringResource(R.string.text_loading_ad),
) {
    CircularProgressLoadingDialog(
        navUp = navUp ?: {},
        text = text,
        userDismissEnable = false
    )
}
