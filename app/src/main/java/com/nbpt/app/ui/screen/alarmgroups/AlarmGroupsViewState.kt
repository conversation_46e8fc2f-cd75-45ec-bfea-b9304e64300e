package com.nbpt.app.ui.screen.alarmgroups

import com.nbpt.app.androidcomponent.alarm.remindtorecord.AlarmGroup

data class AlarmGroupsViewState(
    val alarmGroups: List<AlarmGroup> = emptyList(),
    val showAlarmGroupEmpty: Boolean? = null,
    val showRemindTypeChooseLay: Boolean = false,
    val showInterstitialAdLoadingDialog: Boolean = false
) {
    companion object {
        val Empty = AlarmGroupsViewState()
    }
}
