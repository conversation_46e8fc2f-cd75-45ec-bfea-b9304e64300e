package com.nbpt.app.ui.screen.language

import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import androidx.lifecycle.ViewModel
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.Support
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.screen.destinations.GuideDestination
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.util.Locale

private const val TAG = "LanguageViewModel"

class LanguageViewModel(
    private val userBehaviorDataStore: UserBehaviorDataStore,
) : ViewModel(), ContainerHost<LanguageViewState, LanguageSideEffect> {

    override val container: Container<LanguageViewState, LanguageSideEffect> =
        container(LanguageViewState())

    init {
        configureLaunchLanguageScreen()
    }

    private fun configureLaunchLanguageScreen() = intent {
        val showBackIcon = userBehaviorDataStore.getLanguageFinish()
        reduce { state.copy(showBackIcon = showBackIcon) }
    }

    fun onRefresh(locale: Locale) = intent {
        val supportLocaleList = Support.localeList
        val selectLocale = Support.compatibleLanguage(locale) ?: Locale.ENGLISH

        reduce {
            state.copy(
                selectedLocale = selectLocale,
                localeList = supportLocaleList
            )
        }
    }

    fun onLanguageSelect(locale: Locale) = intent {
        reduce { state.copy(selectedLocale = locale) }
    }

    fun onConfirmLanguage(locale: Locale? = null) = intent {
        debugLog(tag = "language") { "state.selectedLocale: ${state.selectedLocale}" }
        changeLocale(locale ?: state.selectedLocale) {
            debugLog(tag = "language") { "changeLocale: $it" }
        }
        userBehaviorDataStore.setLanguageFinish()

        val isFinishGuide = userBehaviorDataStore.getGuideFinish()
        if (!isFinishGuide) {
            GlobalNavigator.navigate {
                popBackStack()
                navigate(GuideDestination.route)
            }
        } else {
            GlobalNavigator.navigate { navigateUp() }
        }
    }

    private fun changeLocale(
        locale: Locale,
        onLocaleChanged: ((changeSuccessful: Boolean) -> Unit)? = null
    ) {
        if (locale != Locale.getDefault()) {
            GlobalScope.launch {
                debugLog(tag = TAG) { "setApplicationLocales(): ${locale.toLanguageTag()}" }
                launch(Dispatchers.Main.immediate) {
                    AppCompatDelegate.setApplicationLocales(LocaleListCompat.forLanguageTags(locale.toLanguageTag()))
                    onLocaleChanged?.invoke(true)
                }
            }
        } else {
            onLocaleChanged?.invoke(false)
        }
    }

    private fun resetLocale() {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            AppCompatDelegate.setApplicationLocales(
                LocaleListCompat.getEmptyLocaleList()
            )
        }
    }

}
