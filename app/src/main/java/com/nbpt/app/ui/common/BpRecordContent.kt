package com.nbpt.app.ui.common

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.rounded.AccessTime
import androidx.compose.material.icons.rounded.CalendarMonth
import androidx.compose.material.icons.rounded.DateRange
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.common.datetime.isoFormat
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.marosseleng.compose.material3.datetimepickers.date.ui.dialog.DatePickerDialog
import com.marosseleng.compose.material3.datetimepickers.time.ui.dialog.TimePickerDialog
import com.nbpt.app.ui.theme.defShadow
import com.sd.lib.compose.wheel_picker.FVerticalWheelPicker
import com.sd.lib.compose.wheel_picker.FWheelPickerFocusVertical
import com.sd.lib.compose.wheel_picker.rememberFWheelPickerState
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toJavaLocalTime
import kotlinx.datetime.toKotlinLocalDate
import kotlinx.datetime.toKotlinLocalTime
import kotlinx.datetime.toLocalDateTime
import java.time.format.FormatStyle

@Composable
fun BpStatusImageAndDescription(
    bpStatus: BpStatus,
    onInfoClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val img = when (bpStatus) {
        BpStatus.Elevated -> painterResource(id = R.drawable.img_bp_type_elev)
        BpStatus.HypertensionS1 -> painterResource(id = R.drawable.img_bp_type_stage1)
        BpStatus.HypertensionS2 -> painterResource(id = R.drawable.img_bp_type_stage2)
        BpStatus.Hypertensive -> painterResource(id = R.drawable.img_bp_type_hype)
        BpStatus.Hypotension -> painterResource(id = R.drawable.img_bp_type_elev)
        BpStatus.Normal -> painterResource(id = R.drawable.img_bp_type_normal)
    }

    val gutter = Layout.gutter

    Surface(
        shape = RoundedCornerShape12Dp,
        modifier = modifier.defShadow()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 16.dp)
        ) {
            Image(
                painter = img,
                contentDescription = stringResource(id = bpStatus.descriptionStringId),
                modifier = Modifier
                    .size(80.dp),
                contentScale = ContentScale.FillBounds
            )

            BlankSpacer(width = 12.dp)

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = stringResource(id = bpStatus.titleStringId),
                    style = MaterialTheme.typography.bodyLarge.copy(color = bpStatus.color),
                )

                BlankSpacer(height = 6.dp)

                Text(
                    text = stringResource(id = bpStatus.descriptionStringId),
                    style = MaterialTheme.typography.bodyMedium.copy(color = AppTheme.Color.textSecondary)
                )

            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(end = 8.dp)
        ) {
            IconButton(
                onClick = onInfoClick,
                modifier = Modifier.align(Alignment.CenterEnd),
            ) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "info",
                    modifier = Modifier.size(28.dp),
                    tint = AppTheme.Color.BpPrimaryLight
                )
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BpInfoNumberPicker(
    infoTitle: String,
    infoUnit: String,
    currentValue: Int,
    minValue: Int,
    maxValue: Int,
    onValueChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
    displayItemCount: Int = 3,
    selectedTextColor: Color = AppTheme.Color.textPrimary,
    unselectTextColor: Color = AppTheme.Color.textSecondary,
) {

    val titleStyle =
        MaterialTheme.typography.titleMedium.copy(color = selectedTextColor)
    val unitStyle =
        MaterialTheme.typography.titleMedium.copy(color = selectedTextColor)

    Column(modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Text(text = infoTitle, modifier = Modifier.basicMarquee(), maxLines = 1, style = titleStyle)

        BlankSpacer(height = Layout.gutter)

        val surfaceColor = AppTheme.Color.White
        Surface(color = surfaceColor, shape = RoundedCornerShape12Dp, modifier = Modifier.defShadow()) {
            Log.d("BpInfoNumberPicker", "$infoTitle currentValue:${currentValue}")

            val wheelPickerState = rememberFWheelPickerState()

            var updateCount by remember { mutableStateOf(0) }

            LaunchedEffect(currentValue) {
                Log.d(
                    "BpInfoNumberPicker",
                    "$infoTitle scrollToIndex to ${currentValue - minValue}"
                )
                wheelPickerState.scrollToIndex(currentValue - minValue)
            }

            LaunchedEffect(Unit) {
                snapshotFlow {
                    wheelPickerState.currentIndex
                }.collect {
                    ++updateCount
                    Log.d("BpInfoNumberPicker", "$infoTitle collect:${it}")
                    Log.d("BpInfoNumberPicker", "$infoTitle updateCount:${updateCount}")

                    // 1st update is collect -1 (default index)
                    // 2nd update is collect ViewState.Empty value
                    if (updateCount >= 3) {
                        onValueChange(wheelPickerState.currentIndex + minValue)
                    }
                }
            }

            FVerticalWheelPicker(
                count = maxValue - minValue + 1,
                state = wheelPickerState,
                modifier = Modifier.requiredWidthIn(max = 100.dp),
                unfocusedCount = (displayItemCount - 1) / 2,
                focus = {
                    FWheelPickerFocusVertical(
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 20.dp),
                        dividerSize = 2.5.dp,
                        dividerColor = AppTheme.Color.BpPrimaryLight
                    )
                },
                itemHeight = 42.dp
            ) {
                val style =
                    MaterialTheme.typography.headlineMedium.copy(color = if (it == wheelPickerState.currentIndex) selectedTextColor else unselectTextColor)
                Text(
                    text = (it + minValue).toString(),
                    style = style
                )
            }
        }

        BlankSpacer(height = Layout.gutter)

        Text(text = infoUnit, modifier = Modifier.basicMarquee(), maxLines = 1, style = unitStyle)
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun BpInfoDatetimeAndNotesEditor(
    noteCount: Int,
    dateTime: LocalDateTime,
    onDateClick: () -> Unit,
    onTimeClick: () -> Unit,
    onNotesClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(R.string.text_date_n_time),
                modifier = Modifier.weight(1f),
                style = MaterialTheme.typography.titleMedium
            )

            TextButton(onClick = onNotesClick) {
                Box(contentAlignment = Alignment.Center) {
                    Text(
                        text = stringResource(R.string.text_note_s, noteCount),
                        style = MaterialTheme.typography.labelLarge.copy(color = AppTheme.Color.textPrimary),
                    )
                }

            }
        }

//        BlankSpacer(height = Layout.gutter / 4)

        Row(verticalAlignment = Alignment.CenterVertically) {

            val textStyle = MaterialTheme.typography.labelLarge
            Surface(
                modifier = Modifier.weight(1f).defShadow(),
                shape = RoundedCornerShape12Dp,
            ) {
                Row(
                    Modifier
                        .clickable(onClick = onDateClick)
                        .padding(
                            vertical = 14.dp,
                            horizontal = 12.dp
                        ),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Rounded.CalendarMonth,
                        contentDescription = "date",
                    )
                    BlankSpacer(width = Layout.gutter)
                    Text(
                        text = dateTime.date.isoFormat(FormatStyle.MEDIUM),
                        modifier = Modifier.basicMarquee(),
                        maxLines = 1,
                        style = textStyle
                    )
                }
            }

            Spacer(modifier = Modifier.requiredWidthIn(Layout.bodyMargin))

            Surface(
                modifier = Modifier.weight(1f).defShadow(),
                shape = RoundedCornerShape12Dp,
            ) {
                Row(
                    Modifier
                        .clickable(onClick = onTimeClick)
                        .padding(
                            vertical = 14.dp,
                            horizontal = 12.dp
                        )
                        .basicMarquee(),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Icon(
                        imageVector = Icons.Rounded.AccessTime,
                        contentDescription = "time",
                    )
                    BlankSpacer(width = Layout.gutter)
                    Text(
                        text = dateTime.time.isoFormat(),
                        modifier = Modifier.basicMarquee(),
                        maxLines = 1,
                        style = textStyle
                    )
                }
            }
        }

    }
}

@Composable
fun BpInfoEditor(
    systolic: Int,
    diastolic: Int,
    pulse: Int,
    onSystolicChange: (Int) -> Unit,
    onDiastolicChange: (Int) -> Unit,
    onPulseChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(modifier) {
        BlankSpacer(width = 4.dp)

        BpInfoNumberPicker(
            infoTitle = stringResource(R.string.text_systolic),
            infoUnit = "mmHg",
            currentValue = systolic,
            minValue = 20,
            maxValue = 300,
            onValueChange = onSystolicChange,
            modifier = Modifier.weight(1f),
        )

        BlankSpacer(width = 4.dp)

        BpInfoNumberPicker(
            infoTitle = stringResource(R.string.text_diastolic),
            infoUnit = "mmHg",
            currentValue = diastolic,
            minValue = 20,
            maxValue = 300,
            onValueChange = onDiastolicChange,
            modifier = Modifier.weight(1f)
        )

        BlankSpacer(width = 4.dp)

        BpInfoNumberPicker(
            infoTitle = stringResource(R.string.text_pulse),
            infoUnit = stringResource(R.string.text_bpm),
            currentValue = pulse,
            minValue = 20,
            maxValue = 200,
            onValueChange = onPulseChange,
            modifier = Modifier.weight(1f)
        )

        BlankSpacer(width = 4.dp)
    }
}

@Preview
@Composable
private fun BpStatusImageAndDescriptionPreview() {
    BpStatusImageAndDescription(bpStatus = BpStatus.Normal, onInfoClick = { /*TODO*/ })
}

@Preview
@Composable
private fun BpInfoNumberPickerPreview() {

    var cv by remember {
        mutableStateOf(90)
    }

    BpInfoNumberPicker(
        infoTitle = stringResource(id = R.string.text_systolic),
        infoUnit = "mmHg",
        currentValue = cv,
        minValue = 1,
        maxValue = 100,
        onValueChange = { cv = it }
    )
}

@Preview
@Composable
private fun BpInfoEditorPreview() {

    var s by remember {
        mutableStateOf(90)
    }

    var d by remember {
        mutableStateOf(90)
    }

    var p by remember {
        mutableStateOf(90)
    }

    BpInfoEditor(
        systolic = s,
        diastolic = d,
        pulse = p,
        onSystolicChange = {
            s = it
        },
        onDiastolicChange = {
            d = it
        },
        onPulseChange = {
            p = it
        }
    )
}

@OptIn(ExperimentalComposeUiApi::class, ExperimentalMaterial3Api::class)
@Preview
@Composable
private fun BpInfoDatetimeAndNotesEditorPreview() {

    val ldt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())

    var date by remember {
        mutableStateOf(ldt.date)
    }

    var time by remember {
        mutableStateOf(ldt.time)
    }

    var showDatePicker by remember {
        mutableStateOf(false)
    }

    var showTimePicker by remember {
        mutableStateOf(false)
    }

    if (showDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            onDateChange = {
                date = it.toKotlinLocalDate()
                showDatePicker = false
            },
            initialDate = date.toJavaLocalDate(),
        )
    }

    if (showTimePicker) {
        TimePickerDialog(
            onDismissRequest = { showTimePicker = false },
            onTimeChange = {
                time = it.toKotlinLocalTime()

                showTimePicker = false
            },
            initialTime = time.toJavaLocalTime(),
        )
    }

    BpInfoDatetimeAndNotesEditor(
        5,
        dateTime = LocalDateTime(date, time),
        onDateClick = { showDatePicker = true },
        onTimeClick = { showTimePicker = true },
        {}
    )
}
