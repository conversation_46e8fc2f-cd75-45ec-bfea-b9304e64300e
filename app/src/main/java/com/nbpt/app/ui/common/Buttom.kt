package com.nbpt.app.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow

@Suppress("LocalVariableName")
@Composable
fun CardButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    textStyle: TextStyle = MaterialTheme.typography.titleMedium,
    enabled: Boolean = true,
    containerBrush: Brush = AppTheme.Color.PrimaryBrush,
    contentColor: Color = AppTheme.Color.White,
    elevation: Dp = 0.dp,
    shape: Shape = CircleShape
) {

    val (_containerBrush, _contentColor)  = if (enabled)
        containerBrush to contentColor
    else SolidColor(Color.LightGray) to AppTheme.Color.textSecondary


    Surface(
        modifier = modifier.defShadow(shape = shape),
        contentColor = _contentColor,
        tonalElevation = elevation,
        shadowElevation = elevation,
        shape = shape,
    ) {
        Box(
            modifier = Modifier
                .clickable(onClick = onClick, enabled = enabled)
                .background(brush = _containerBrush)
                .padding(Layout.gutter + 1.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = text,
                style = textStyle.copy(
                    fontSize = (textStyle.fontSize.value + 1.6).sp,
                    lineHeight = (textStyle.lineHeight.value - 6).sp
                ),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview
@Composable
private fun CardButtonPreview() {
    CardButton(
        text = "lalala",
        onClick = {},
        modifier = Modifier.requiredWidthIn(min = 130.dp, max = 230.dp)
    )
}
