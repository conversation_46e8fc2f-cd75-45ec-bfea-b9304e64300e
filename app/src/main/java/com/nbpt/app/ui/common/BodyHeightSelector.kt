package com.nbpt.app.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.nbpt.app.ui.screen.heartrateeditor.NumberPicker
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.bodyWidth

@Composable
fun BodyHeightSelectorContentImperial(
    title: String,
    ft: Int,
    `in`: Int,
    onFtChange: (Int) -> Unit,
    onInChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier) {
        BlankSpacer(height = 16.dp)

        Text(
            text = title,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary, fontSize = 18.sp)
        )

        Row(
            modifier = Modifier.bodyWidth().scale(.92f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.weight(1f))

            NumberPicker(
                currentValue = ft,
                minValue = 0,
                maxValue = 9,
                onValueChange = onFtChange,
                dividerColor = AppTheme.Color.BmiPrimary,
            )

            Text(
                text = "ft",
                style = MaterialTheme.typography.bodyLarge.copy(color = AppTheme.Color.textPrimary)
            )

            Spacer(modifier = Modifier.weight(.5f))

            NumberPicker(
                currentValue = `in`,
                minValue = 0,
                maxValue = 11,
                onValueChange = onInChange,
                dividerColor = AppTheme.Color.BmiPrimary,
            )

            Text(
                text = "in",
                style = MaterialTheme.typography.bodyLarge.copy(color = AppTheme.Color.textPrimary)
            )

            Spacer(modifier = Modifier.weight(1f))

        }

        BlankSpacer(height = 8.dp)

    }
}

@Composable
fun BodyHeightSelectorContentMetric(
    title: String,
    cm: Int,
    onCmChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Column(modifier) {
        BlankSpacer(height = 16.dp)

        Text(
            text = title,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textPrimary, fontSize = 18.sp)
        )

        Row(
            modifier = Modifier.bodyWidth().scale(.92f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.weight(1f))

            NumberPicker(
                currentValue = cm,
                minValue = 30,
                maxValue = 280,
                onValueChange = onCmChange,
                dividerColor = AppTheme.Color.BmiPrimary,
            )

            Text(
                text = "cm",
                modifier = Modifier.weight(1f),
                style = MaterialTheme.typography.bodyLarge.copy(color = AppTheme.Color.textPrimary),
                textAlign = TextAlign.Start
            )
        }

        BlankSpacer(height = 8.dp)
    }
}

@Preview
@Composable
fun BodyHeightSelectorContentMetricPreview() {
    BodyHeightSelectorContentMetric(title = "lalala", cm = 170, onCmChange = {})
}

@Preview
@Composable
fun BodyHeightSelectorContentImperialPreview() {
    BodyHeightSelectorContentImperial(
        title = "lalala",
        ft = 7,
        `in` = 1,
        onFtChange = {},
        onInChange = {})
}
