package com.nbpt.app.ui.screen.heartratemeasure

import com.ramcosta.composedestinations.spec.Direction

sealed interface HeartRateMeasureSideEffect {
    data class MeasureFinish(
        val genderNumber: Int,
        val age: Int,
        val bpm: Int
    ) : HeartRateMeasureSideEffect

    data class NavTo(val destination: Direction) : HeartRateMeasureSideEffect
    data object NavUp : HeartRateMeasureSideEffect
}
