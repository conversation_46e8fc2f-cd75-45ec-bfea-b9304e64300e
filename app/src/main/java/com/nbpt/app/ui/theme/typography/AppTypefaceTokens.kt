package com.nbpt.app.ui.theme.typography

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.nbpt.app.R


val AppFont = FontFamily(
    Font(R.font.figtree_medium),
    Font(R.font.figtree_bold),
    Font(R.font.figtree_extrabold),
)

object AppTypefaceTokens {
//    val Brand = FontFamily.SansSerif
//    val Plain = FontFamily.SansSerif
    val Brand = AppFont
    val Plain = AppFont
    val WeightBold = FontWeight.Bold
    val WeightMedium = FontWeight.Medium
    val WeightRegular = FontWeight.Normal
}
