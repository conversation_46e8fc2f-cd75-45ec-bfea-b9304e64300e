package com.nbpt.app.ui.theme.bp
import androidx.compose.ui.graphics.Color
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.seed

val md_theme_light_primary = AppTheme.Color.BpPrimary
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = /*Color(0xFFFFDADA)*/ Color.White
val md_theme_light_onPrimaryContainer = /*Color(0xFF40000B)*/ seed
val md_theme_light_secondary = Color(0xFFAC3040)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = /*Color(0xFFFFDADA)*/ md_theme_light_primaryContainer
val md_theme_light_onSecondaryContainer = Color(0xFF40000B)
val md_theme_light_tertiary = Color(0xFFAC3040)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = /*Color(0xFFFFDADA)*/ md_theme_light_primaryContainer
val md_theme_light_onTertiaryContainer = seed
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFFFFFF)
val md_theme_light_onBackground = seed
val md_theme_light_surface = /*Color(0xFFFFFBFF)*/ md_theme_light_primaryContainer
val md_theme_light_onSurface = seed
val md_theme_light_surfaceVariant = /*Color(0xFFF4DDDD)*/ md_theme_light_primaryContainer
val md_theme_light_onSurfaceVariant = seed
val md_theme_light_outline = Color(0xFF857373)
val md_theme_light_inverseOnSurface = Color(0xFFFFECF3)
val md_theme_light_inverseSurface = Color(0xFF5A1145)
val md_theme_light_inversePrimary = Color(0xFFFFB3B5)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = /*Color(0xFFAC3040)*/ md_theme_light_primaryContainer
val md_theme_light_outlineVariant = Color(0xFFD7C1C1)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFFFFB3B5)
val md_theme_dark_onPrimary = Color(0xFF680018)
val md_theme_dark_primaryContainer = Color(0xFF8B162A)
val md_theme_dark_onPrimaryContainer = Color(0xFFFFDADA)
val md_theme_dark_secondary = Color(0xFFFFB3B5)
val md_theme_dark_onSecondary = Color(0xFF680018)
val md_theme_dark_secondaryContainer = Color(0xFF8B162A)
val md_theme_dark_onSecondaryContainer = Color(0xFFFFDADA)
val md_theme_dark_tertiary = Color(0xFFFFB3B5)
val md_theme_dark_onTertiary = Color(0xFF680018)
val md_theme_dark_tertiaryContainer = Color(0xFF8B162A)
val md_theme_dark_onTertiaryContainer = Color(0xFFFFDADA)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF3C002C)
val md_theme_dark_onBackground = Color(0xFFFFD8EB)
val md_theme_dark_surface = Color(0xFF3C002C)
val md_theme_dark_onSurface = Color(0xFFFFD8EB)
val md_theme_dark_surfaceVariant = Color(0xFF524343)
val md_theme_dark_onSurfaceVariant = Color(0xFFD7C1C1)
val md_theme_dark_outline = Color(0xFF9F8C8C)
val md_theme_dark_inverseOnSurface = Color(0xFF3C002C)
val md_theme_dark_inverseSurface = Color(0xFFFFD8EB)
val md_theme_dark_inversePrimary = Color(0xFFAC3040)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFFFFB3B5)
val md_theme_dark_outlineVariant = Color(0xFF524343)
val md_theme_dark_scrim = Color(0xFF000000)


val CustomColor1 = Color(0xFFFC6C78)
val CustomColor2 = Color(0xFFFC6C78)
val CustomColor3 = Color(0xFFFC6C78)
val CustomColor4 = Color(0xFFFBF5F5)
val light_CustomColor1 = Color(0xFFAC3040)
val light_onCustomColor1 = Color(0xFFFFFFFF)
val light_CustomColor1Container = Color(0xFFFFDADA)
val light_onCustomColor1Container = Color(0xFF40000B)
val dark_CustomColor1 = Color(0xFFFFB3B5)
val dark_onCustomColor1 = Color(0xFF680018)
val dark_CustomColor1Container = Color(0xFF8B162A)
val dark_onCustomColor1Container = Color(0xFFFFDADA)
val light_CustomColor2 = Color(0xFFAC3040)
val light_onCustomColor2 = Color(0xFFFFFFFF)
val light_CustomColor2Container = Color(0xFFFFDADA)
val light_onCustomColor2Container = Color(0xFF40000B)
val dark_CustomColor2 = Color(0xFFFFB3B5)
val dark_onCustomColor2 = Color(0xFF680018)
val dark_CustomColor2Container = Color(0xFF8B162A)
val dark_onCustomColor2Container = Color(0xFFFFDADA)
val light_CustomColor3 = Color(0xFFAC3040)
val light_onCustomColor3 = Color(0xFFFFFFFF)
val light_CustomColor3Container = Color(0xFFFFDADA)
val light_onCustomColor3Container = Color(0xFF40000B)
val dark_CustomColor3 = Color(0xFFFFB3B5)
val dark_onCustomColor3 = Color(0xFF680018)
val dark_CustomColor3Container = Color(0xFF8B162A)
val dark_onCustomColor3Container = Color(0xFFFFDADA)
val light_CustomColor4 = Color(0xFF94416F)
val light_onCustomColor4 = Color(0xFFFFFFFF)
val light_CustomColor4Container = Color(0xFFFFD8E8)
val light_onCustomColor4Container = Color(0xFF3C0028)
val dark_CustomColor4 = Color(0xFFFFAFD5)
val dark_onCustomColor4 = Color(0xFF5B113F)
val dark_CustomColor4Container = Color(0xFF772957)
val dark_onCustomColor4Container = Color(0xFFFFD8E8)
