package com.nbpt.app.ui.screen.bp

import com.nbpt.app.data.db.model.BpRecordEntity
import com.nbpt.app.data.healtharticles.HealthArticle

data class TrackerViewState(
    val statDataMode: TrackerRoughStatModeAdt = TrackerRoughStatModeAdt.Avg,
    val maxStatData: TrackerRoughStatData? = null,
    val minStatData: TrackerRoughStatData? = null,
    val avgStatData: TrackerRoughStatData? = null,
    val latestStatData: TrackerRoughStatData? = null,
    val showAddRecordGuideLay: Boolean = false,
    val bpRecords: List<BpRecordEntity> = emptyList(),
    val showAdLoadingDialog: Boolean = false,
    val isUnlockBpRecordStat: Boolean = false,

    val bpArticles: List<HealthArticle> = emptyList(),
) {
    companion object {
        val Empty = TrackerViewState()
    }
}
