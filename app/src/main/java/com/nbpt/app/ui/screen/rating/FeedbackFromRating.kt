package com.nbpt.app.ui.screen.rating

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.R
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.android.openFeedbackDef
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import org.koin.compose.koinInject

@Destination(
    style = AdaptiveHeightDialog::class,
)
@Composable
fun FeedbackFromRating(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val splashManager: SplashManager = koinInject()


    Column(
        modifier = Modifier
            .navigationBarsPadding()
            .fillMaxWidth()
            .padding(horizontal = 42.dp)
    ) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape12Dp
        ) {
            Box(modifier = Modifier.padding(Layout.gutter)) {
                IconButton(
                    onClick = navigator::navigateUp,
                    modifier = Modifier.align(Alignment.TopEnd),
                    colors = IconButtonDefaults.iconButtonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppTheme.Color.PrimaryMedium
                    )
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = "close",
                    )
                }

                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BlankSpacer(height = Layout.bodyMargin * 1.5f)

                    Image(
                        painter = painterResource(id = R.drawable.img_rating),
                        contentDescription = null,
                        modifier = Modifier.size(width = 162.dp, height = 104.dp)
                    )

//                    BlankSpacer(height = Layout.bodyMargin)
//
//                    Text(
//                        text = stringResource(R.string.text_oh_sorry),
//                        style = MaterialTheme.typography.titleLarge
//                    )
                    BlankSpacer(height = 4.dp)
                    Text(
                        text = stringResource(R.string.text_tips_feedback),
                        style = MaterialTheme.typography.bodyMedium
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    CardButton(
                        text = stringResource(R.string.text_feedback),
                        onClick = {
                            context.openFeedbackDef()
                            splashManager.doSkipSplash(true)
                            navigator.navigateUp()
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 58.dp),
                        shape = RoundedCornerShape12Dp,
                        containerBrush = SolidColor(AppTheme.Color.PrimaryMedium)
                    )

                    BlankSpacer(height = Layout.bodyMargin * 2)
                }
            }
        }
    }

}

@Preview
@Composable
fun FeedbackFromRatingPreview() {
    AppMd3Theme {
        FeedbackFromRating(navigator = EmptyDestinationsNavigator)
    }
}
