package com.nbpt.app.ui.screen.heartratemeasure

import android.app.Activity
import android.content.Context
import androidx.camera.view.PreviewView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.biz.heartrate.HeartRateMeasureState
import com.nbpt.app.biz.heartrate.HeartRateMonitor
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.db.dao.HrRecordDao
import com.ramcosta.composedestinations.spec.Direction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class HeartRateMeasureViewModel(
    private val heartRateMonitor: HeartRateMonitor,
    private val hrRecordDao: HrRecordDao,
//    private val interstitialAdManager: InterstitialAdManager,
) : ViewModel(), ContainerHost<HeartRateMeasureViewState, HeartRateMeasureSideEffect> {

    override val container: Container<HeartRateMeasureViewState, HeartRateMeasureSideEffect> =
        container(HeartRateMeasureViewState.Empty)

    init {
        heartRateMonitor.measureStateFlow.onEach { hrms ->
            intent {
                reduce { state.copy(measureState = hrms) }


                if (hrms is HeartRateMeasureState.Finish) {
                    val lastHrRecord = hrRecordDao.fetchRecordsFlow(1).first().firstOrNull()
                    postSideEffect(
                        HeartRateMeasureSideEffect.MeasureFinish(
                            genderNumber = lastHrRecord?.gender ?: 0,
                            age = lastHrRecord?.age ?: 25,
                            bpm = hrms.heartRateBpm ?: 0
                        )
                    )
                }
            }
        }.launchIn(viewModelScope)
    }

    fun onHRMVFLayout(
        context: Context,
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView
    ) = intent {
        heartRateMonitor.prepare(
            context = context,
            lifecycleOwner = lifecycleOwner,
            previewView = previewView,
        )
        heartRateMonitor.launch(lifecycleOwner)
    }

    fun onFlashSwitch(use: Boolean) = intent {
        heartRateMonitor.flashSwitch(use)
        reduce { state.copy(useCameraFlash = use) }
    }

    fun onInterruptCamera() {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            heartRateMonitor.interrupt()
        }
    }


//    sealed interface ExecuteInterAd {
//        object Navigate : ExecuteInterAd
//        object OnBack : ExecuteInterAd
//    }
//
//    private val executeInterAdFlow = MutableStateFlow<ExecuteInterAd?>(null)
//
//    @Volatile
//    private var hasInterAdShowing = false
    fun onBack(activity: Activity) = intent {
        reduce { state.copy(showInterstitialAdLoadingDialog = true) }

//        executeInterAdFlow.update { ExecuteInterAd.OnBack }

        onInterruptCamera()

        postSideEffect(HeartRateMeasureSideEffect.NavUp)

//        showInterAdContainer = activity
//
//        if (!hasInterAdShowing) {
//            interstitialAdManager.tryToShowAd(activity, "back_hr")
//        }
    }

//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    private var showInterAdAfterDestination: Direction? = null
//    fun onTryToShowInterAdAndNavTo(
//        activity: Activity,
//        destination: Direction,
//        adPlaceName: String,
//    ) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        executeInterAdFlow.update { ExecuteInterAd.Navigate }
//
//        showInterAdContainer = activity
//        showInterAdAfterDestination = destination
//
//        interstitialAdManager.tryToShowAd(activity, adPlaceName)
//    }
//
//    @Suppress("MoveVariableDeclarationIntoWhen")
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach { adLoadingStateEvent ->
//            val executeInterAdMode = executeInterAdFlow.first()
//
//            debugLog(tag = "executeInterAdMode") { "adLoadingStateEventFlow.onEach executeInterAdMode: ${executeInterAdMode?.javaClass?.simpleName}" }
//            when (executeInterAdMode) {
//                ExecuteInterAd.Navigate -> when (adLoadingStateEvent) {
//                    InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                    InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                        showInterAdAfterDestination?.let { destination ->
//                            showInterAdContainer = null
//                            showInterAdAfterDestination = null
//
//                            postSideEffect(HeartRateMeasureSideEffect.NavTo(destination))
//                        }
//                    }
//
//                    InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                        showInterAdContainer?.let { containerActivity ->
//                            interstitialAdManager.tryToShowAd(containerActivity, null)
//                        }
//                    }
//                }
//
//                ExecuteInterAd.OnBack -> when (adLoadingStateEvent) {
//                    InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                    InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                        if (showInterAdContainer != null) {
//                            showInterAdContainer = null
//                            hasInterAdShowing = false
//
//                            postSideEffect(HeartRateMeasureSideEffect.NavUp)
//                        }
//                    }
//
//                    InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                        showInterAdContainer?.let { containerActivity ->
//                            if (!hasInterAdShowing) {
//                                interstitialAdManager.tryToShowAd(containerActivity, null)
//                            }
//                        }
//                    }
//                }
//
//                null -> {}
//            }
//
//
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach { adShowStateEvent ->
//            val executeInterAdMode = executeInterAdFlow.first()
//
//            debugLog(tag = "executeInterAdMode") { "adShowStateEventFlow.onEach executeInterAdMode: ${executeInterAdMode?.javaClass?.simpleName}" }
//
//            when (executeInterAdMode) {
//                ExecuteInterAd.Navigate -> when (adShowStateEvent) {
//                    InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                    InterstitialAdManager.AdShowStateEvent.Finish,
//                    InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                        showInterAdAfterDestination?.let { destination ->
//                            showInterAdContainer = null
//                            showInterAdAfterDestination = null
//
//                            postSideEffect(HeartRateMeasureSideEffect.NavTo(destination))
//                        }
//                    }
//
//                    InterstitialAdManager.AdShowStateEvent.Showing -> {}
//                }
//
//                ExecuteInterAd.OnBack -> when (adShowStateEvent) {
//                    InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                    InterstitialAdManager.AdShowStateEvent.Finish,
//                    InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                        reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                        if (showInterAdContainer != null) {
//                            showInterAdContainer = null
//                            hasInterAdShowing = false
//
//                            postSideEffect(HeartRateMeasureSideEffect.NavUp)
//                        }
//                    }
//
//                    InterstitialAdManager.AdShowStateEvent.Showing -> {
//                        if (showInterAdContainer != null) {
//                            hasInterAdShowing = true
//                        }
//                    }
//                }
//
//                null -> {}
//            }
//
//        }.launchIn(lifecycleScope)
//    }
}
