package com.nbpt.app.ui.screen.permissonsmanager

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.BatteryChargingFull
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.IgnoringBatteryOptimizationRequester
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState

@Destination
@Composable
fun PermissionsManager(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current

    val viewModel: PermissionsViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    LaunchedEffect(Unit) {
        IgnoringBatteryOptimizationRequester.ignoringEventFlow.onEach {
            if (it != null) {
                viewModel.onRefresh(context.findActivity())
            }
        }.launchIn(this)
    }

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh(context.findActivity())
            else -> {}
        }
    }

    Scaffold(
        topBar = {
            PermissionsManagerAppBar(navUp = navigator::navigateUp)
        },
        bottomBar = {
            SmartRectAd(
                pageType = SmartAdPageType.FEAT_BOTTOM_BAR,
                bannerAdPlace = BannerAdPlace.PERMISSIONS,
                nativeAdPlace = NativeAdPlace.Permissions,
                modifier = Modifier.navigationBarsPadding()
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        ) {
            BlankSpacer(height = 16.dp)

            PermissionItem(
                isGranted = viewState.isNotificationGranted,
                iconPainter = painterResource(id = R.drawable.ic_permission_notification),
                title = stringResource(id = R.string.text_permission_notification),
                description = stringResource(id = R.string.text_permission_notification_desc),
                onRequestPermission = {
                    viewModel.requestNotificationPermission(context.findActivity())
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            PermissionItem(
                isGranted = viewState.isIgnoreBatteryOptimizationsGranted,
                iconPainter = painterResource(id = R.drawable.ic_permission_battery),
                title = stringResource(id = R.string.text_permission_battery),
                description = stringResource(id = R.string.text_permission_battery_desc0),
                onRequestPermission = {
                    viewModel.requestIgnoringBatteryOptimization(context.findActivity())
                },
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.FEAT_CONTENT,
                bannerAdPlace = BannerAdPlace.PERMISSIONS,
                nativeAdPlace = NativeAdPlace.Permissions,
                modifier = Modifier.navigationBarsPadding()
            )

            BlankSpacer(height = 16.dp)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PermissionsManagerAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier) {
        TopAppBar(
            title = { Text(text = stringResource(R.string.text_permissions)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
        )

        SmartRectAd(
            pageType = SmartAdPageType.FEAT_TOP_BAR,
            bannerAdPlace = BannerAdPlace.PERMISSIONS,
            nativeAdPlace = NativeAdPlace.Permissions,
        )
    }
}

@Composable
private fun PermissionItem(
    isGranted: Boolean,
    iconPainter: Painter,
    title: String,
    description: String,
    onRequestPermission: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .noRippleClickable {
                if (!isGranted) {
                    onRequestPermission()
                }
            }
            .defShadow(),
        shape = RoundedCornerShape12Dp,
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            BlankSpacer(width = 6.dp)
            Box(modifier = Modifier.padding(horizontal = 12.dp, vertical = 20.dp)) {
                Image(
                    painter = iconPainter,
                    contentDescription = null,
                    modifier = Modifier.size(40.dp),
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(text = title, fontSize = 16.sp, lineHeight = 17.sp)
                BlankSpacer(height = 6.dp)
                Text(
                    text = description,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 13.sp
                )
            }

            if (isGranted) {
                BlankSpacer(width = 8.dp)
                Icon(
                    imageVector = Icons.Rounded.CheckCircle,
                    contentDescription = null,
                    tint = AppTheme.Color.Primary,
                    modifier = Modifier.size(28.dp)
                )
                BlankSpacer(width = 8.dp)
            } else {
                Switch(
                    checked = false,
                    onCheckedChange = null,
                    modifier = Modifier
                        .scale(.8f)
                        .alpha(.7f)
                )
            }

            BlankSpacer(width = 12.dp)
        }
    }
}

@Preview
@Composable
private fun PermissionItemPreview() {
    PermissionItem(
        isGranted = true,
        iconPainter = rememberVectorPainter(image = Icons.Rounded.BatteryChargingFull),
        title = "Lalalala",
        description = "lalalala",
        onRequestPermission = {}
    )
}