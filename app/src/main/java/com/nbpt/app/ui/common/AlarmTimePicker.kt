package com.nbpt.app.ui.common

import android.text.format.DateFormat
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import com.nbpt.app.ui.theme.AppTheme
import com.commandiron.wheel_picker_compose.WheelTimePicker
import com.commandiron.wheel_picker_compose.core.SelectorProperties
import com.commandiron.wheel_picker_compose.core.TimeFormat
import com.commandiron.wheel_picker_compose.core.WheelPickerDefaults
import kotlinx.datetime.LocalTime
import kotlinx.datetime.toJavaLocalTime
import kotlinx.datetime.toKotlinLocalTime

@Composable
fun AlarmTimePicker(
    time: LocalTime?,
    onTimeChange: (LocalTime) -> Unit,
    modifier: Modifier = Modifier,
    size: DpSize = DpSize(260.dp, 200.dp),
    timeFormat: TimeFormat = if (DateFormat.is24HourFormat(LocalContext.current)) TimeFormat.HOUR_24 else TimeFormat.AM_PM,
    textStyle: TextStyle = MaterialTheme.typography.headlineMedium,
    selectorProperties: SelectorProperties = WheelPickerDefaults.selectorProperties(
        color = AppTheme.Color.AlarmPrimaryLight.copy(.12f),
        border = BorderStroke(2.dp, AppTheme.Color.AlarmPrimaryLight.copy(.8f))
    )
) {
    if (time == null) {
        WheelTimePicker(
            modifier = modifier,
            timeFormat = timeFormat,
            size = size,
            textStyle = textStyle,
            selectorProperties = selectorProperties,
        )
    } else {
        WheelTimePicker(
            modifier = modifier,
            startTime = time.toJavaLocalTime(),
            timeFormat = timeFormat,
            size = size,
            textStyle = textStyle,
            selectorProperties = selectorProperties,
            onSnappedTime = { selectTime ->
                onTimeChange(selectTime.toKotlinLocalTime())
            }
        )
    }
}
