package com.nbpt.app.ui.screen.heartraterecordnotesmanager

import androidx.lifecycle.ViewModel
import com.nbpt.app.data.db.dao.HrRecordsNoteDao
import com.nbpt.app.data.db.model.HrRecordsNoteEntity
import com.nbpt.app.data.pojo.HrRecordsNote
import kotlinx.coroutines.flow.first
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class HeartRateRecordNotesManagerViewModel(
    private val noteDao: HrRecordsNoteDao
) : ViewModel(),
    ContainerHost<HeartRateRecordNotesManagerViewState, HeartRateRecordNotesManagerSideEffect> {

    override val container: Container<HeartRateRecordNotesManagerViewState, HeartRateRecordNotesManagerSideEffect> =
        container(HeartRateRecordNotesManagerViewState.Empty)

    init {
        intent {
            val notes = noteDao.fetchAllNotesFlow().first()

            reduce { state.copy(notes = notes) }
        }
    }

    private val pendingAddNotes = mutableListOf<HrRecordsNoteEntity>()
    val pendingDeleteNotes = mutableListOf<HrRecordsNoteEntity>()

    fun onAdd(
        noteText: String
    ) = intent {
        val noteSingle = HrRecordsNote(content = noteText.trim()).toDbEntity()

        val newNotes = listOf(noteSingle) + state.notes

        pendingAddNotes.add(noteSingle)

        reduce { state.copy(notes = newNotes) }

        onDismissAddNoteDialog()
    }

    fun onDelete(note: HrRecordsNoteEntity) = intent {
        val newNotes = state.notes.toMutableList().apply {
            remove(note)
        }

        pendingDeleteNotes.add(note)
        pendingAddNotes.remove(note)

        reduce { state.copy(notes = newNotes) }
    }

    fun onSave() = intent {
        reduce { state.copy(notesSaving = true) }

        if (pendingDeleteNotes.isNotEmpty()) {
            noteDao.delete(pendingDeleteNotes)
        }

        if (pendingAddNotes.isNotEmpty()) {
            noteDao.add(pendingAddNotes)
        }

        reduce { state.copy(notesSaving = false) }

        postSideEffect(HeartRateRecordNotesManagerSideEffect.NavUp)
    }

    fun onShowAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = true) }
    }

    fun onDismissAddNoteDialog() = intent {
        reduce { state.copy(addNoteDialogShow = false) }
    }
}
