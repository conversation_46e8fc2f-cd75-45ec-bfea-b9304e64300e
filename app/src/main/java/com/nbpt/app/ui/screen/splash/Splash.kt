package com.nbpt.app.ui.screen.splash

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.GlobalNavigator
import com.nbpt.app.MainActivity
import com.nbpt.app.NotificationActionNavigator
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import com.nbpt.app.androidcomponent.fixednoti.NotiClickManager
import com.nbpt.app.androidcomponent.repeatnoti.HandsUpNotiManager
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.androidcomponent.simplefcmnoti.SimpleFirebaseMessagingNotification
import com.nbpt.app.biz.admanager.appopen.AdmobAppOpenAdManager
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.configureFirebaseMessagingTopicIfNeeded
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.destinations.GuideDestination
import com.nbpt.app.ui.screen.destinations.HomeDestination
import com.nbpt.app.ui.screen.destinations.LanguageDestination
import com.nbpt.app.ui.screen.destinations.SplashDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.spec.Direction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectSideEffect

private var splashDisplayCount = 0

data class SplashNavArgs(
    val isColdLaunch: Boolean = true,
    val afterLaunchDestinationRoute: String = "",
    val previousDestinationWasHome: Boolean = false
)

@Destination(
    navArgsDelegate = SplashNavArgs::class
)
@Composable
fun Splash(
    navigator: DestinationsNavigator
) {
    BackHandler {}

    val context = LocalContext.current
    val activity = context.findActivity()

    val handsUpNotiManager: HandsUpNotiManager = koinInject()
    val simpleFirebaseMessagingNotification: SimpleFirebaseMessagingNotification = koinInject()
    val notiClickManager: NotiClickManager = koinInject()
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()

    val admobAppOpenAdManager: AdmobAppOpenAdManager = koinInject()

    val scope = rememberCoroutineScope()
    val userBehaviorDataStore: UserBehaviorDataStore = koinInject()

    val viewModel: SplashViewModel = koinViewModel()


    viewModel.collectSideEffect {
        when (it) {
            SplashSideEffect.NavUp -> {
                navigator.navigateUp()
            }

            SplashSideEffect.PopupSelfAndNavToNextScreen -> {
                scope.launch {
                    val (isFinishGuide, isFinishLanguage) =
                        userBehaviorDataStore.getGuideFinish() to userBehaviorDataStore.getLanguageFinish()

                    val startRoute = if (!isFinishLanguage && !isFinishGuide) {
                        LanguageDestination.route
                    } else if (!isFinishGuide) {
                        GuideDestination.route
                    } else {
                        HomeDestination.route
                    }

                    navigator.popBackStack()
                    navigator.navigate(Direction(startRoute))

                    handsUpNotiManager.handleNotiIntent(
                        intent = activity.intent,
                        isActivityInForeground = true
                    )
                    NotificationActionNavigator.handleIntentAction(
                        intent = activity.intent,
                        isActivityInForeground = true
                    )
                    simpleFirebaseMessagingNotification.handleIntentAction(
                        intent = activity.intent,
                        isActivityInForeground = true
                    )
                    notiClickManager.handleAction(
                        intent = activity.intent,
                        isActivityInForeground = true
                    )
                }
            }

            is SplashSideEffect.PopupSelfAndNavAction -> {
                navigator.popBackStack()
                navigator.navigate(Direction(it.route))
            }

            SplashSideEffect.ShowAdmobAppOpenAd -> {
                admobAppOpenAdManager.tryToShowAd(
                    context.findActivity(),
                    immediate = true
                )
            }
        }
    }

    SplashContent()

    var loadingSeconds by remember { mutableFloatStateOf(0f) }

    val isWindowFocused by MainActivity.windowFocusChangedFlow.collectAsState()
    LaunchedEffect(isWindowFocused) {
        delay(100)
        if (MainActivity.windowFocusChangedFlow.first() == true) {
            notificationPermissionRequester.tryToRequestIfNeeded(activity)
        }

        delay(250)
        if (MainActivity.windowFocusChangedFlow.first() == true) {
            viewModel.requestConsentAndConfigureAds(activity, scope) {

                scope.launch(Dispatchers.Default) {
                    repeat(Int.MAX_VALUE) {
                        delay(500)
                        loadingSeconds += 0.5f

                        debugLog { "loadingSeconds: $loadingSeconds" }
                    }
                }
            }
        }
    }


    LaunchedEffect(Unit) {
        if (viewModel.args.previousDestinationWasHome) {
            debugLog(tag = "splash") { "previousDestinationWasHome" }
            nativeAdManager.destroy(NativeAdPlaceholder.Home)
            nativeAdManager.destroy(NativeAdPlaceholder.Info)
            nativeAdManager.destroy(NativeAdPlaceholder.Settings)
        }
    }

    LaunchedEffect(Unit) {
        if (splashDisplayCount > 0) {
//            notificationPermissionRequester.tryToShowDefaultFixedNotification(context)
            configureFirebaseMessagingTopicIfNeeded(context)
        }
        splashDisplayCount++
    }
}

@Composable
private fun SplashContent(
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Image(
            painter = painterResource(id = R.drawable.splash),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize(),
            alignment = Alignment.BottomCenter
        )

        Column(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.launcher_icon),
                contentDescription = null,
                modifier = Modifier.size(108.dp)
            )

            BlankSpacer(height = 20.dp)

            Text(
                text = stringResource(id = R.string.app_name),
                style = MaterialTheme.typography.titleMedium.copy(
                    color = AppTheme.Color.White,
                    fontWeight = FontWeight.ExtraBold,
                    fontSize = 19.sp
                )
            )

            BlankSpacer(height = 60.dp)

            Text(
                text = stringResource(R.string.text_splash_tips),
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = AppTheme.Color.White,
                    fontSize = 15.sp,
                )
            )

            BlankSpacer(height = 52.dp)
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .requiredWidthIn(max = 380.dp)
                .padding(horizontal = 70.dp)
                .align(Alignment.BottomCenter)
                .padding(bottom = 108.dp)
                .navigationBarsPadding(),
        ) {
//            Text(
//                text = stringResource(R.string.text_this_action_might_contain_ad),
//                modifier = Modifier.bodyWidth(),
//                style = MaterialTheme.typography.bodySmall.copy(
//                    color = AppTheme.Color.White,
//                )
//            )

            BlankSpacer(height = 16.dp)

            LinearProgressIndicator(color = AppTheme.Color.Primary, strokeCap = StrokeCap.Round)
        }

    }
}

@Preview
@Composable
private fun SplashContentPreview() {
    SplashContent()
}
