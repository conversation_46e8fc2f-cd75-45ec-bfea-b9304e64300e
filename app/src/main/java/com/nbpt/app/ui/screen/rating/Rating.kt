package com.nbpt.app.ui.screen.rating

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.R
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.destinations.FeedbackFromRatingDestination
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import org.koin.compose.koinInject

@Destination(
    style = AdaptiveHeightDialog::class,
)
@Composable
fun Rating(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val ratingManager: RatingManager = koinInject()


    Column(
        modifier = Modifier
            .navigationBarsPadding()
            .padding(horizontal = 42.dp)
            .fillMaxWidth()
    ) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape12Dp
        ) {
            Box(modifier = Modifier.padding(Layout.gutter)) {
                IconButton(
                    onClick = navigator::navigateUp,
                    modifier = Modifier.align(Alignment.TopEnd),
                    colors = IconButtonDefaults.iconButtonColors(
                        containerColor = Color.Transparent,
                        contentColor = AppTheme.Color.PrimaryMedium
                    )
                ) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = "close",
                    )
                }

                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    BlankSpacer(height = Layout.bodyMargin * 1.5f)

                    Image(
                        painter = painterResource(id = R.drawable.img_rating),
                        contentDescription = null,
                        modifier = Modifier.size(width = 162.dp, height = 104.dp)
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    Text(
                        text = stringResource(
                            R.string.text_enjoy_,
                            stringResource(id = R.string.app_name)
                        ),
                        style = MaterialTheme.typography.bodyLarge
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    RatingStar(
                        maxStarCount = 5,
                        selectedStarCount = 0,
                        onStarSelect = { ratingStart ->
                            if (ratingStart >= 5) {
                                navigator.navigateUp()
                                ratingManager.openGooglePlayInAppReviews(context.findActivity())

                                logEventRecord("click_rate_dialog_review")
                            } else {
                                navigator.popBackStack()
                                navigator.navigate(FeedbackFromRatingDestination)

                                logEventRecord("click_rate_dialog_feedback")
                            }
                        }
                    )

                    BlankSpacer(height = Layout.bodyMargin)
                }
            }
        }

    }

}

@Composable
fun RatingStar(
    maxStarCount: Int,
    selectedStarCount: Int,
    onStarSelect: (selectedStarCount: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier) {
        (0 until maxStarCount).forEach { index ->
            val tintColor = if (selectedStarCount < index + 1) Color.LightGray else Color(0xFFFFC107)

            Icon(
                imageVector = Icons.Rounded.Star,
                contentDescription = null,
                tint = tintColor,
                modifier = Modifier
                    .size(40.dp)
                    .clickable {
                        onStarSelect(index + 1)
                    }
            )

            if (maxStarCount != index + 1) {
                BlankSpacer(width = 4.dp)
            }
        }
    }
}

@Preview
@Composable
private fun RatingPreview() {
    AppMd3Theme {
        Rating(navigator = EmptyDestinationsNavigator)
    }
}
