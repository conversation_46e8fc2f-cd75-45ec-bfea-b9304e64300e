package com.nbpt.app.ui.screen.bprecordeditor

import android.app.Activity
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.common.datetime.toInstant
import com.nbpt.app.common.datetime.toLocalDatetime
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.data.db.dao.BpRecordDao
import com.nbpt.app.data.db.model.BpRecordEntity
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.nbpt.app.data.healtharticles.HealthArticleType
import com.nbpt.app.data.healtharticles.HealthArticles
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.flow.first
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.LocalTime
import org.koin.core.component.KoinComponent
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container
import java.util.UUID

class BpRecordEditorViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val bpRecordDao: BpRecordDao,
    private val fixedNotificationHelper: FixedNotificationHelper
) : ViewModel(), ContainerHost<BpRecordEditorViewState, BpRecordEditorSideEffect>, KoinComponent {

    override val container: Container<BpRecordEditorViewState, BpRecordEditorSideEffect> =
        container(BpRecordEditorViewState.Empty)

    val navArgs = savedStateHandle.navArgs<BpRecordEditorNavArgs>()

    init {
        handleArgs()
    }

    private var recordUUID: String = navArgs.recordUUID ?: UUID.randomUUID().toString()

    private fun handleArgs() = intent {

        val bpArticles = HealthArticles.fetch()[HealthArticleType.BP]

        if (navArgs.editorMode == BpRecordEditorMode.Add) {
            val latestSingle = bpRecordDao.fetchRecordsFlow(1).first().firstOrNull()

            if (latestSingle != null) {
                reduce {
                    state.copy(
                        editorMode = navArgs.editorMode,
                        dateTime = navArgs.instant.toLocalDatetime(),
                        systolic = latestSingle.systolic,
                        diastolic = latestSingle.diastolic,
                        pulse = latestSingle.pulse,
                        notesSelection = navArgs.notesSelection ?: emptyList(),
                        bpArticles = bpArticles ?: emptyList()
                    )
                }
                return@intent
            }
        }


        reduce {
            state.copy(
                editorMode = navArgs.editorMode,
                dateTime = navArgs.instant.toLocalDatetime(),
                systolic = navArgs.systolic,
                diastolic = navArgs.diastolic,
                pulse = navArgs.pulse,
                notesSelection = navArgs.notesSelection ?: emptyList(),
                bpArticles = bpArticles ?: emptyList()
            )
        }
    }

    fun onDismissDatePicker() = intent {
        reduce { state.copy(showDatePicker = false) }
    }

    fun onShowDatePicker() = intent {
        reduce { state.copy(showDatePicker = true) }
    }

    fun onDismissTimePicker() = intent {
        reduce { state.copy(showTimePicker = false) }
    }

    fun onShowTimePicker() = intent {
        reduce { state.copy(showTimePicker = true) }
    }

    fun onDismissDeleteRecordDialog() = intent {
        reduce { state.copy(showDeleteRecordDialog = false) }
    }

    fun onShowDeleteRecordDialog() = intent {
        reduce { state.copy(showDeleteRecordDialog = true) }
    }

    fun onDismissBpTypeDialog() = intent {
        reduce { state.copy(showBpTypeDialog = false) }
    }

    fun onShowBpTypeDialog() = intent {
        reduce { state.copy(showBpTypeDialog = true) }
    }

    fun onDateChange(date: LocalDate) = intent {
        val time = state.dateTime.time

        reduce { state.copy(dateTime = LocalDateTime(date, time)) }
        onDismissDatePicker()
    }

    fun onTimeChange(time: LocalTime) = intent {
        val date = state.dateTime.date

        reduce { state.copy(dateTime = LocalDateTime(date, time)) }
        onDismissTimePicker()
    }

    fun onSystolicChange(systolic: Int) = intent {
        reduce { state.copy(systolic = systolic) }
    }

    fun onDiastolicChange(diastolic: Int) = intent {
        reduce { state.copy(diastolic = diastolic) }
    }

    fun onPulseChange(pulse: Int) = intent {
        reduce { state.copy(pulse = pulse) }
    }

    fun onNotesSelectionUpdate(notesSelection: List<String>) = intent {
        reduce { state.copy(notesSelection = notesSelection) }
    }

    suspend fun deletedSelection(deletedNotes: List<BpRecordsNoteEntity>): Set<String> {
        val state = container.stateFlow.first()

        val deletedSelection = mutableSetOf<String>()

        deletedNotes.forEach { bpRecordsNoteEntity ->
            if (bpRecordsNoteEntity.content in state.notesSelection) {
                deletedSelection.add(bpRecordsNoteEntity.content)
            }
        }

        return deletedSelection
    }

    fun onNotesDelete(deletedSelection: Set<String>) = intent {
        reduce { state.copy(notesSelection = state.notesSelection - deletedSelection) }
    }

    fun onSave() = intent {
        val instant = state.dateTime.toInstant()
        val systolic = state.systolic ?: return@intent
        val diastolic = state.diastolic ?: return@intent
        val pulse = state.pulse ?: return@intent
        val notes = state.notesSelection

        if (systolic <= diastolic) {
            postSideEffect(BpRecordEditorSideEffect.Toast("Systolic blood pressure must be greater than diastolic blood pressure"))
            return@intent
        }

        bpRecordDao.upsert(
            BpRecordEntity(
                uuid = recordUUID,
                instant = instant,
                systolic = systolic,
                diastolic = diastolic,
                pulse = pulse,
                notes = notes,
            )
        )

        val bpStatus = bpStatus()
        postSideEffect(BpRecordEditorSideEffect.SaveAndNavUp(bpStatus))
        fixedNotificationHelper.updateNoti()
    }

    fun onDelete() = intent {
        bpRecordDao.delete(recordUUID)

        postSideEffect(BpRecordEditorSideEffect.NavUp)
        fixedNotificationHelper.updateNoti()
    }

    fun onBack(force: Boolean = false) = intent {
        if (force) {
            postSideEffect(BpRecordEditorSideEffect.NavUp)
            return@intent
        }

        if (navArgs.editorMode == BpRecordEditorMode.Add) {
            reduce { state.copy(showExitEditorTipsDialog = true) }
        } else {
            postSideEffect(BpRecordEditorSideEffect.NavUp)
        }
    }

    fun onDismissExitEditorTipsDialog() = intent {
        reduce { state.copy(showExitEditorTipsDialog = false) }
    }

    private suspend fun bpStatus(): BpStatus {
        val viewState = container.stateFlow.first()

        return BpStatus.from(
            systolic = viewState.systolic ?: 100,
            diastolic = viewState.diastolic ?: 75
        )
    }
}
