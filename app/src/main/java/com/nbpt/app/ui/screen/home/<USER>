package com.nbpt.app.ui.screen.home

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.ui.screen.navArgs
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

internal class HomeViewModel(
    private val savedStateHandle: SavedStateHandle,
) : ViewModel(), ContainerHost<HomeViewState, Unit> {

    override val container: Container<HomeViewState, Unit> = container(HomeViewState.Empty)

    init {
//        savedStateHandle.getStateFlow("tabIndex", -1).onEach {
//            Log.d("HomeViewModel", "tabIndexFlow onEach: $it")
//            if (it > -1) {
//                onNavTabChange(it)
//            }
//        }.launchIn(viewModelScope)

        val navArgs = savedStateHandle.navArgs<HomeNavArgs>()
        onNavTabChange(navArgs.tabIndex)
    }

    fun onNavTabChange(index: Int) = intent {
        if (state.selectedNavigationItemIndex == index) return@intent

        if (index in HomeNavigationItems.indices) {
            reduce { state.copy(selectedNavigationItemIndex = index) }
        } else {
            reduce { state.copy(selectedNavigationItemIndex = 0) }
        }
    }
}
