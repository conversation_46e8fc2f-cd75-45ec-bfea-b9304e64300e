package com.nbpt.app.ui.screen.home

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.banner.AdmobBannerAdManager
import com.nbpt.app.biz.admanager.banner.MaxBannerAdHelper
import com.nbpt.app.biz.admanager.nat1ve.AdmobNativeAdManager
import com.nbpt.app.biz.admanager.nat1ve.MaxNativeAdManager
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
//import com.nbpt.app.biz.admanager.banner.BannerAdManager
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdManager
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import org.koin.compose.koinInject

private const val INSTANT_EXIT_TRIGGER_MILLIS = 400L

private var latestClickBackEventOnHomeMillis = 0L

@Composable
fun HomeOnBackHandle() {
    val maxNativeAdManager: MaxNativeAdManager = koinInject()
    val maxBannerAdManager: MaxBannerAdHelper = koinInject()
    val admobNativeAdManager: AdmobNativeAdManager = koinInject()
    val admobBannerAdManager: AdmobBannerAdManager = koinInject()

    val activity = LocalContext.current.findActivity()
    var showOnBackTipsDialog by remember { mutableStateOf(false) }

    if (showOnBackTipsDialog) {
        HomeScreenOnBackTipsDialog(
            onDismiss = {
//                val currentEpochMillis = nowInstant().toEpochMilliseconds()
//                if (currentEpochMillis - latestClickBackEventOnHomeMillis <= INSTANT_EXIT_TRIGGER_MILLIS) {
//                    showOnBackTipsDialog = false
//                    activity.moveTaskToBack(true)
//                } else {
//                    showOnBackTipsDialog = false
//                    latestClickBackEventOnHomeMillis = currentEpochMillis
//                }
                showOnBackTipsDialog = false
            },
            onExit = {
                maxNativeAdManager.destroyAll()
                maxBannerAdManager.destroyAll()
                admobNativeAdManager.destroyAll()
                admobBannerAdManager.destroyAll()

                showOnBackTipsDialog = false
                activity.moveTaskToBack(true)

//                GlobalScope.launch {
//                    delay(800)
//                    bannerAdManager.destroy(BannerAdPlace.HomeTopBar)
//                    nativeAdManager.destroy(NativeAdPlace.Settings)
//                }
            }
        )
    }

    BackHandler {
//        val currentEpochMillis = nowInstant().toEpochMilliseconds()
//        if (currentEpochMillis - latestClickBackEventOnHomeMillis <= INSTANT_EXIT_TRIGGER_MILLIS) {
//            showOnBackTipsDialog = false
//            activity.moveTaskToBack(true)
//        } else {
//            showOnBackTipsDialog = true
//            latestClickBackEventOnHomeMillis = currentEpochMillis
//        }
        showOnBackTipsDialog = true
    }
}

@Composable
private fun HomeScreenOnBackTipsDialog(
    onDismiss: () -> Unit,
    onExit: () -> Unit,
) {
//    AlertDialog(
//        onDismissRequest = onDismiss,
//        confirmButton = {
//            TextButton(onClick = onExit) {
//                Text(text = "Exit")
//            }
//        },
//        dismissButton = {
//            TextButton(onClick = onDismiss) {
//                Text(text = "Cancel")
//            }
//        },
//        title = {
//            Text(text = "Exit App", style = MaterialTheme.typography.titleLarge)
//        },
//        text = {
//            Text(text = "Are you sure to exit app?")
//        }
//    )

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = false,
            usePlatformDefaultWidth = false
        )
    ) {
        Column(
            modifier = Modifier
                .navigationBarsPadding()
                .padding(horizontal = 24.dp)
                .fillMaxWidth()
        ) {
            Surface(shape = RoundedCornerShape12Dp) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Layout.bodyMargin)
                        .padding(top = Layout.bodyMargin * 1.8f, bottom = Layout.gutter),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.img_app_exit_tips),
                        contentDescription = null,
                        modifier = Modifier.size(90.dp),
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    Text(
                        text = stringResource(R.string.text_tips_exit_app),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyMedium.copy(fontSize = 15.sp)
                    )

                    BlankSpacer(height = Layout.bodyMargin)

                    CardButton(
                        text = stringResource(R.string.text_exit),
                        onClick = onExit,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 44.dp)
                    )


                    TextButton(onClick = onDismiss) {
                        Text(
                            text = stringResource(R.string.text_cancel),
                            style = MaterialTheme.typography.bodySmall.copy(
                                fontWeight = FontWeight.Normal,
                                color = AppTheme.Color.textSecondary.copy(alpha = .6f)
                            ),
                        )
                    }
                }
            }

            BlankSpacer(height = 12.dp)

            NativeAd(place = NativeAdPlace.ExitApp)
        }
    }

}
