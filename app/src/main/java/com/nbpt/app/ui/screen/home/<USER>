package com.nbpt.app.ui.screen.home

import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.MainActivity
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.PermissionRequestManager
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.screen.info.InfoScreen
import com.nbpt.app.ui.screen.info.InfoViewModel
import com.nbpt.app.ui.screen.settings.SettingsScreen
import com.nbpt.app.ui.screen.settings.SettingsViewModel
import com.nbpt.app.ui.screen.tool.Tool
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootNavGraph
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState

internal sealed interface HomeNavigationTabScreen {
    data object Tool : HomeNavigationTabScreen
    data object Info : HomeNavigationTabScreen
    data object Settings : HomeNavigationTabScreen
}

internal sealed class HomeNavigationItem(
    val insideScreen: HomeNavigationTabScreen,
    @StringRes val labelResId: Int,
    @StringRes val contentDescriptionResId: Int,
) {
    class ResourceIcon(
        screen: HomeNavigationTabScreen,
        @StringRes labelResId: Int,
        @StringRes contentDescriptionResId: Int,
        @DrawableRes val iconResId: Int,
        @DrawableRes val selectedIconResId: Int? = null,
    ) : HomeNavigationItem(screen, labelResId, contentDescriptionResId)

    class ImageVectorIcon(
        screen: HomeNavigationTabScreen,
        @StringRes labelResId: Int,
        @StringRes contentDescriptionResId: Int,
        val iconImageVector: ImageVector,
        val selectedImageVector: ImageVector? = null,
    ) : HomeNavigationItem(screen, labelResId, contentDescriptionResId)
}

internal val HomeNavigationItems = listOf(
    HomeNavigationItem.ResourceIcon(
        screen = HomeNavigationTabScreen.Tool,
        labelResId = R.string.text_home,
        contentDescriptionResId = R.string.text_home,
        iconResId = R.drawable.ic_tab_home,
        selectedIconResId = R.drawable.ic_tab_home_selected,
    ),
    HomeNavigationItem.ResourceIcon(
        screen = HomeNavigationTabScreen.Info,
        labelResId = R.string.info_title,
        contentDescriptionResId = R.string.cd_info_title,
        iconResId = R.drawable.ic_tab_info,
        selectedIconResId = R.drawable.ic_tab_info_selected,
    ),
    HomeNavigationItem.ResourceIcon(
        screen = HomeNavigationTabScreen.Settings,
        labelResId = R.string.settings_title,
        contentDescriptionResId = R.string.cd_settings_title,
        iconResId = R.drawable.ic_tab_settings,
        selectedIconResId = R.drawable.ic_tab_settings_selected,
    ),
)

@Composable
private fun HomeNavigationItemIcon(item: HomeNavigationItem, selected: Boolean) {
    val painter = when (item) {
        is HomeNavigationItem.ResourceIcon -> painterResource(item.iconResId)
        is HomeNavigationItem.ImageVectorIcon -> rememberVectorPainter(item.iconImageVector)
    }
    val selectedPainter = when (item) {
        is HomeNavigationItem.ResourceIcon -> item.selectedIconResId?.let { painterResource(it) }
        is HomeNavigationItem.ImageVectorIcon -> item.selectedImageVector?.let {
            rememberVectorPainter(it)
        }
    }

    if (selectedPainter != null) {
        Crossfade(targetState = selected, label = "") {
            Image(
                painter = if (it) selectedPainter else painter,
                contentDescription = stringResource(item.contentDescriptionResId),
                modifier = Modifier.size(32.dp),
            )
        }
    } else {
        Image(
            painter = painter,
            contentDescription = stringResource(item.contentDescriptionResId),
            modifier = Modifier.size(32.dp)
        )
    }
}

@Composable
internal fun HomeNavigationBar(
    selectedNavigation: HomeNavigationTabScreen?,
    onNavigationSelected: HomeNavigationTabScreen.(Int) -> Unit,
    modifier: Modifier = Modifier,
) {

    Surface(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .padding(vertical = 6.dp)
                .navigationBarsPadding(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            BlankSpacer(width = 8.dp)

            HomeNavigationItems.forEachIndexed { index, item ->
                BottomNavBarItem(
                    onClick = { onNavigationSelected(item.insideScreen, index) },
                    item = item,
                    selected = selectedNavigation == item.insideScreen,
                    modifier = Modifier.weight(1f)
                )
            }

            BlankSpacer(width = 8.dp)
        }
    }
}

@Suppress("LocalVariableName")
@Composable
internal fun BottomNavBarItem(
    onClick: () -> Unit,
    item: HomeNavigationItem,
    selected: Boolean,
    modifier: Modifier = Modifier
) {
    Crossfade(
        targetState = selected,
        modifier = modifier.noRippleClickable {
            onClick()
        },
        label = ""
    ) { _selected ->

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.bodyWidth()
        ) {
            HomeNavigationItemIcon(
                item = item,
                selected = _selected
            )

            BlankSpacer(height = 1.dp)

            val textColor = if (_selected) {
                AppTheme.Color.Primary
            } else {
                AppTheme.Color.textSecondary
            }

            Text(
                text = stringResource(item.labelResId),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = textColor,
                    fontSize = 11.sp
                ),
                textAlign = TextAlign.Center
            )
        }

    }
}

@SuppressLint("UnusedMaterial3ScaffoldPaddingParameter")
@RootNavGraph(start = true)
@Destination(
    navArgsDelegate = HomeNavArgs::class,
)
@Composable
fun Home(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val permissionRequestManager: PermissionRequestManager = koinInject()

    val viewModel: HomeViewModel = koinViewModel()
    val infoViewModel: InfoViewModel = koinViewModel()
    val settingsViewModel: SettingsViewModel = koinViewModel()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()

    val viewState by viewModel.collectAsState()

    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    val currentInsideScreen =
        HomeNavigationItems[viewState.selectedNavigationItemIndex].insideScreen

    LaunchedEffect(currentInsideScreen) {
        delay(200)
        if ((context.findActivity() as? MainActivity)?.isInForeground == true) {
            permissionRequestManager.doRequestIfNeeded(context.findActivity())
        }
    }

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    HomeOnBackHandle()

    Scaffold(
        bottomBar = {
            Column {
                SmartRectAd(
                    pageType = SmartAdPageType.HOME_BOTTOM_BAR,
                    bannerAdPlace = BannerAdPlace.HOME,
                    nativeAdPlace = NativeAdPlace.Home,
                )
                HomeNavigationBar(
                    selectedNavigation = currentInsideScreen,
                    onNavigationSelected = {
                        viewModel.onNavTabChange(it)
                    },
                )
            }
        }
    ) { paddingValues ->
        Crossfade(
            modifier = Modifier.padding(bottom = paddingValues.calculateBottomPadding()),
            targetState = currentInsideScreen,
        ) {
            when (it) {
                HomeNavigationTabScreen.Tool -> Tool(
                    navigator = navigator,
                    admobInterstitialAdViewModel = admobInterstitialAdViewModel
                )

                HomeNavigationTabScreen.Info -> InfoScreen(
                    navigator = navigator,
                    viewModel = infoViewModel
                )

                HomeNavigationTabScreen.Settings -> SettingsScreen(
                    navigator = navigator,
                    viewModel = settingsViewModel
                )
            }
        }
    }
}

data class HomeNavArgs(
    val tabIndex: Int = 0,
)

@Preview
@Composable
private fun HomeNavigationBarPreview() {
    HomeNavigationBar(
        selectedNavigation = HomeNavigationTabScreen.Info,
        onNavigationSelected = {},
    )
}
