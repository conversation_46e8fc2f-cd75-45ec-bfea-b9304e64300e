package com.nbpt.app.ui.screen.ignoringbatteryoptimization

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.AdaptiveHeightDialog
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.IgnoringBatteryOptimizationRequester
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.compose.koinInject

@Destination(
    style = AdaptiveHeightDialog::class,
)
@Composable
fun IgnoringBatteryOptimizationDialog(
    fromPermissionsManager: Boolean = false,
    navigator: DestinationsNavigator,
) {
    val context = LocalContext.current
    val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester = koinInject()

    LaunchedEffect(Unit) {
        if (!fromPermissionsManager) {
            ignoringBatteryOptimizationRequester.apply {
                requestTimes++
                storeOpenBatteryOptimizationSettingsInstant(nowInstant())
            }
        }
        logEventRecord("battery_permission_request")
        debugLog { "battery_permission_request" }
    }

    val navUp = {
        navigator.navigateUp()
    }

    BackHandler {
        navUp()
        logEventRecord("battery_permission_request_fail")
        debugLog { "battery_permission_request_fail" }
    }

    Column(
        modifier = Modifier
            .navigationBarsPadding()
            .padding(horizontal = 24.dp)
            .fillMaxWidth()
    ) {
        Surface(shape = RoundedCornerShape12Dp) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp, bottom = 16.dp)
                    .padding(horizontal = Layout.bodyMargin),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Row(horizontalArrangement = Arrangement.End, modifier = Modifier.fillMaxWidth()) {
                    Icon(
                        imageVector = Icons.Rounded.Close,
                        contentDescription = null,
                        modifier = Modifier.clickable { navUp() },
                        tint = AppTheme.Color.textSecondary.copy(.8f)
                    )
                }

                Image(
                    painterResource(id = R.drawable.ic_permission_battery),
                    contentDescription = null,
                    modifier = Modifier.size(width = 132.dp, height = 84.dp),
                )

                BlankSpacer(height = 16.dp)

                Text(
                    text = stringResource(R.string.text_permission_battery),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleLarge.copy(fontSize = 21.sp)
                )

                BlankSpacer(height = 16.dp)

                Text(
                    text = stringResource(R.string.text_permission_battery_desc1),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 15.sp,
                        lineHeight = 17.sp
                    ),
                    modifier = Modifier.fillMaxWidth()
                )

                if (fromPermissionsManager) {
                    BlankSpacer(height = 16.dp)

                    Text(
                        text = stringResource(R.string.text_you_can_disable_it_anytime),
                        style = MaterialTheme.typography.labelSmall.copy(color = AppTheme.Color.textSecondary)
                    )
                }

                BlankSpacer(height = 20.dp)

                CardButton(
                    text = stringResource(R.string.text_allow),
                    onClick = {
                        navUp()
                        ignoringBatteryOptimizationRequester.tryToOpenBatteryOptimizationSystemSettings(
                            context.findActivity()
                        )
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 40.dp),
                    shape = RoundedCornerShape12Dp,
                    containerBrush = SolidColor(AppTheme.Color.PrimaryMedium)
                )

                BlankSpacer(height = 22.dp)
            }
        }
    }
}