package com.nbpt.app.ui.screen.permissonsmanager

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.nbpt.app.androidcomponent.IgnoringBatteryOptimizationRequester
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class PermissionsViewModel(
    private val ignoringBatteryOptimizationRequester: IgnoringBatteryOptimizationRequester,
    private val notificationPermissionRequester: NotificationPermissionRequester,
) : ViewModel(), ContainerHost<PermissionsManagerViewState, Unit> {

    override val container: Container<PermissionsManagerViewState, Unit> =
        container(PermissionsManagerViewState.Empty)

    fun onRefresh(activity: Activity) = intent {

        val isIgnoreBatteryOptimizationsGranted =
            ignoringBatteryOptimizationRequester.hasIgnoring(activity) == true

        val isNotificationGranted = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        reduce {
            state.copy(
                isIgnoreBatteryOptimizationsGranted = isIgnoreBatteryOptimizationsGranted,
                isNotificationGranted = isNotificationGranted
            )
        }
    }

    fun requestIgnoringBatteryOptimization(activity: Activity) {
        ignoringBatteryOptimizationRequester.tryToOpenBatteryOptimizationDialog(activity, true)
    }

    fun requestNotificationPermission(activity: Activity) {
        notificationPermissionRequester.showCustomRequester(activity, fromPermissionsManager = true)
    }
}