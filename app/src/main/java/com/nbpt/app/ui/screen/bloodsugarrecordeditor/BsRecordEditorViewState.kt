package com.nbpt.app.ui.screen.bloodsugarrecordeditor

import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.data.healtharticles.HealthArticle
import kotlinx.datetime.LocalDateTime

data class BsRecordEditorViewState(
    val stateHandleFinished: Boolean = false,

    val editorMode: BsRecordEditorMode? = null,

    val mmolL: Float? = null,
    val mgDl: Float? = null,
    val bsUnit: BsUnit = BsUnit.SI,
    val bsStatus: BsStatus? = mmolL?.let(BsStatus::fromMmolL),

    val bsRecordState: BsRecordState = BsRecordState.Default,

    val dateTime: LocalDateTime? = null,
    val showDeleteRecordDialog: Boolean = false,
    val showBsTypeDialog: Boolean = false,
    val showExitEditorTipsDialog: Boolean = false,

    val bsArticle: HealthArticle? = null,

    val notesSelection: List<String> = emptyList(),
)