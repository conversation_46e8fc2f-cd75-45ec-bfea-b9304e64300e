@file:Suppress("MemberVisibilityCanBePrivate")

package com.nbpt.app.ui.screen.alarmeditor

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.nbpt.app.androidcomponent.alarm.remindtorecord.AlarmGroup
import com.nbpt.app.androidcomponent.alarm.remindtorecord.RemindToRecordAlarmDataStore
import com.nbpt.app.biz.analytics.logEventRecord
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
//import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.ui.screen.navArgs
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.LocalTime
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class AlarmEditorViewModel(
    private val savedStateHandle: SavedStateHandle,
    private val alarmDataStore: RemindToRecordAlarmDataStore,
//    private val interstitialAdManager: InterstitialAdManager,
) : ViewModel(),
    ContainerHost<AlarmEditorViewState, AlarmEditorSideEffect> {

    override val container: Container<AlarmEditorViewState, AlarmEditorSideEffect> =
        container(AlarmEditorViewState.Empty)

    val navArgs = savedStateHandle.navArgs<AlarmEditorNavArgs>()
    val alarmGroup = navArgs.alarmGroup
    val editorMode = navArgs.editorMode

    init {
        intent {
            reduce {
                state.copy(
                    isSetNewAlarm = editorMode == AlarmEditorMode.Add,
                    localTime = if (alarmGroup.hour != null && alarmGroup.minute != null)
                        LocalTime(
                            alarmGroup.hour,
                            alarmGroup.minute
                        ) else null,
                    dayOfWeekSelections = alarmGroup.dayOfWeek.toSet(),
                    soundEnable = alarmGroup.soundEnable,
                    vibrateEnable = alarmGroup.vibrateEnable,
                    remindType = alarmGroup.type
                )
            }
        }
    }

    fun onSelect(
        dayOfWeek: DayOfWeek,
        select: Boolean
    ) = intent {
        val newSelections = if (select) {
            state.dayOfWeekSelections + setOf(dayOfWeek)
        } else {
            state.dayOfWeekSelections - setOf(dayOfWeek)
        }
        reduce { state.copy(dayOfWeekSelections = newSelections) }
    }

    fun onTimeChange(
        localTime: LocalTime
    ) = intent {
        reduce { state.copy(localTime = localTime) }
    }

    fun onSave(activity: Activity) = intent {
        reduce { state.copy(showCrudProcessingDialog = true) }

        when (editorMode) {
            AlarmEditorMode.Add -> {
                val newAlarmGroup = AlarmGroup(
                    typeSid = alarmGroup.typeSid,
                    hour = state.localTime?.hour,
                    minute = state.localTime?.minute,
                    soundEnable = state.soundEnable,
                    vibrateEnable = state.vibrateEnable,
                    dayOfWeekIntValues = state.dayOfWeekSelections.map { it.value }
                )
                alarmDataStore.addAlarmGroup(newAlarmGroup)

                reduce {
                    state.copy(
                        showCrudProcessingDialog = false,
                        showInterstitialAdLoadingDialog = true
                    )
                }

//                showInterAdContainer = activity
//                interstitialAdManager.tryToShowAd(activity, "back_alarm")
//
                logEventRecord(
                    "add_alarm_success_${
                        navArgs.alarmGroup.type.typeName.replace(
                            " ",
                            "_"
                        ).lowercase()
                    }"
                )
                reduce { state.copy(showCrudProcessingDialog = false) }
                postSideEffect(AlarmEditorSideEffect.AddAlarmAndNavUp)
            }

            AlarmEditorMode.Edit -> {
                val modifyAlarmGroup = alarmGroup.copy(
                    hour = state.localTime?.hour,
                    minute = state.localTime?.minute,
                    soundEnable = state.soundEnable,
                    vibrateEnable = state.vibrateEnable,
                    dayOfWeekIntValues = state.dayOfWeekSelections.map { it.value }
                )
                alarmDataStore.editAlarmGroup(modifyAlarmGroup)

                reduce { state.copy(showCrudProcessingDialog = false) }
                postSideEffect(AlarmEditorSideEffect.NavUp(true))
            }
        }

//        reduce { state.copy(showCrudProcessingDialog = false) }
//
//        postSideEffect(AlarmEditorSideEffect.NavUp)
    }

    fun onDelete() = intent {
        reduce { state.copy(showCrudProcessingDialog = true) }

        alarmDataStore.deleteAlarmGroup(alarmGroup.id)

        reduce { state.copy(showCrudProcessingDialog = false) }

        postSideEffect(AlarmEditorSideEffect.NavUp(false))
    }

    fun onSoundSettingChange(enable: Boolean) = intent {
        reduce { state.copy(soundEnable = enable) }
    }

    fun onVibrateSettingChange(enable: Boolean) = intent {
        reduce { state.copy(vibrateEnable = enable) }
    }

    fun onShowDeleteAlarmDialog() = intent {
        reduce { state.copy(showDeleteAlarmDialog = true) }
    }

    fun onDismissDeleteAlarmDialog() = intent {
        reduce { state.copy(showDeleteAlarmDialog = false) }
    }

    fun onDismissExitEditorTipsDialog() = intent {
        reduce { state.copy(showExitEditorTipsDialog = false) }
    }

//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//
//                        postSideEffect(AlarmEditorSideEffect.NavUp(true))
//                    }
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        interstitialAdManager.tryToShowAd(containerActivity, null)
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//
//                        postSideEffect(AlarmEditorSideEffect.NavUp(true))
//                    }
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {}
//            }
//        }.launchIn(lifecycleScope)
//    }
}
