package com.nbpt.app.ui.common

import android.annotation.SuppressLint
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.nbpt.app.ui.theme.AppTheme

@Suppress("CrossfadeLabel")
@SuppressLint("UnusedCrossfadeTargetStateParameter")
@Composable
fun CircleCheckBox(
    checked: Boolean,
    onCheck: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    checkedColor: Color = AppTheme.Color.AlarmPrimaryLight,
    uncheckColor: Color = Color(0xFFB0B5BC),
    checkedIcon: ImageVector = Icons.Rounded.Check,
    uncheckIcon: ImageVector = Icons.Rounded.Check,
    innerIconSize: Dp = 18.dp
) {
    Crossfade(
        targetState = checked,
        modifier = modifier
    ) { _ ->
        val (color, icon) = if (checked) {
            checkedColor to checkedIcon
        } else {
            uncheckColor to uncheckIcon
        }
        Surface(
            onClick = { onCheck(!checked) },
            shape = CircleShape,
            color = color,
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.padding(3.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(innerIconSize)
                )
            }
        }
    }
}

@Preview
@Composable
private fun CheckBoxPreview() {
    var check by remember {
        mutableStateOf(false)
    }

    CircleCheckBox(
        checked = check,
        onCheck = {
            check = it
        })
}
