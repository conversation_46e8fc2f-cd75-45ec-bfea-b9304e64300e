package com.nbpt.app.ui.common

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.compose.HrTheme
import com.nbpt.app.R
import com.nbpt.app.data.adt.HrStatus
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth

@Composable
fun HrStatusTypeDialog(
    onDismiss: () -> Unit,
) {
    HrTheme {
        AlertDialog(
            onDismissRequest = onDismiss,
            confirmButton = {
                CardButton(
                    text = stringResource(id = R.string.text_ok),
                    onClick = onDismiss,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 22.dp),
                    containerBrush = AppTheme.Color.HrBrush
                )
            },
            title = {
                Text(
                    text = stringResource(R.string.text_heart_rate_type),
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.bodyWidth(),
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .bodyWidth()
                        .padding(end = 14.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    HrStatus.entries.forEachIndexed { index, status ->
                        HrBpmTypeItem(hrStatus = status)

                        if (index != HrStatus.entries.lastIndex) {
                            BlankSpacer(height = Layout.bodyMargin)
                        }
                    }
                }
            },
        )
    }
}

@Composable
fun HrBpmTypeItem(
    hrStatus: HrStatus,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        BlankWidthIn(min = Layout.gutter, max = Layout.bodyMargin)

        Surface(
            shape = CircleShape,
            color = hrStatus.color,
            modifier = Modifier.size(size = 24.dp)
        ) {}

        BlankSpacer(width = 12.dp)

        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = hrStatus.titleStringId),
                style = MaterialTheme.typography.titleMedium.copy(
                    color = AppTheme.Color.textPrimary,
                    fontSize = 18.sp
                )
            )

            BlankSpacer(width = 5.dp)

            Text(
                text = stringResource(id = hrStatus.descriptionStringId),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = AppTheme.Color.textSecondary,
                    fontSize = 15.sp
                ),
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}

@Preview
@Composable
private fun HrBpmTypeDialogPreview() {

    var showDialog by remember {
        mutableStateOf(true)
    }
    AppMd3Theme {
        HrTheme {
            HrStatusTypeDialog(onDismiss = { showDialog })
        }
    }
}
