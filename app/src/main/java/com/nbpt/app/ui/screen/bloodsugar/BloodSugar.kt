package com.nbpt.app.ui.screen.bloodsugar

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Alarm
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.OnLifecycleEvent
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.adt.BsRecordState
import com.nbpt.app.data.adt.BsStatus
import com.nbpt.app.data.adt.BsUnit
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BsRecordItem
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.common.statTypeface
import com.nbpt.app.ui.screen.anr.RecommendedReading
import com.nbpt.app.ui.screen.bloodsugarrecordeditor.BsRecordEditorMode
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.screen.destinations.BsHistoryDestination
import com.nbpt.app.ui.screen.destinations.BsRecordEditorDestination
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bs.BsTheme
import com.nbpt.app.ui.theme.defShadow
import com.nbpt.app.ui.theme.noRippleClickable
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import java.math.RoundingMode

@Destination
@Composable
fun Bs(navigator: DestinationsNavigator) {
    val viewModel: BloodSugarViewModel = koinViewModel()

    val viewState by viewModel.collectAsState()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    BsTheme {
        BloodSugar(navigator, viewModel, admobInterstitialAdViewModel, viewState)
    }
}

@Composable
fun BloodSugar(
    navigator: DestinationsNavigator,
    viewModel: BloodSugarViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel,
    viewState: BloodSugarViewState,
) {
    val context = LocalContext.current
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    OnLifecycleEvent { _, event ->
        when (event) {
            Lifecycle.Event.ON_START -> viewModel.onRefresh()
            else -> {}
        }
    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd("bs_back_home", onAdLoadingAfter = {
                navigator.navigateUp()
            })
        } else {
            admobInterstitialAdViewModel.onBackAndBeforeTryToShowInterAd(
                activity = context.findActivity(),
                adPlaceName = "bs_back_home"
            )
        }

        Unit
    }

    BackHandler(onBack = onBack)


    Scaffold(
        topBar = {
            BsAppBar(
                navUp = onBack,
                openAlarmGroup = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_alarm", onAdLoadingAfter = {
                            navigator.navigate(AlarmGroupsDestination(showInterAdWhenBack = true))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(AlarmGroupsDestination(showInterAdWhenBack = true)) },
                            adPlaceName = "enter_alarm"
                        )
                    }
                },
                openHistory = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_bs_list", onAdLoadingAfter = {
                            navigator.navigate(BsHistoryDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(BsHistoryDestination) },
                            adPlaceName = "enter_bs_list"
                        )
                    }
                })
        },
        bottomBar = {
            SmartRectAd(
                pageType = SmartAdPageType.HOME_BOTTOM_BAR,
                bannerAdPlace = BannerAdPlace.BS,
                nativeAdPlace = NativeAdPlace.Bs,
                modifier = Modifier.navigationBarsPadding()
            )
        },
        floatingActionButton = {
            val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp

            BsFab(
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd(from = "bs_add", onAdLoadingAfter = {
                            navigator.navigate(BsRecordEditorDestination(editorMode = BsRecordEditorMode.Add))
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(BsRecordEditorDestination(editorMode = BsRecordEditorMode.Add)) },
                            adPlaceName = "bs_add"
                        )
                    }
                },
                modifier = Modifier.padding(bottom = (screenHeightDp / 2) - 72.dp)
            )
        },
        containerColor = AppTheme.Color.BsBackground
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 16.dp)
            BsStat(
                viewState = viewState,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)

            )
            BlankSpacer(height = 16.dp)

            repeat(3) { index ->
                val bsRecord = viewState.bsRecords.getOrNull(index)

                bsRecord?.let {
                    BsRecordItem(bsUnit = viewState.bsUnit, bsRecord = bsRecord, modifier = Modifier.padding(horizontal = 16.dp))
                    BlankSpacer(height = 16.dp)
                }
            }

            CardButton(
                text = stringResource(id = R.string.text_history),
                onClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("enter_bs_list", onAdLoadingAfter = {
                            navigator.navigate(BsHistoryDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = { navigate(BsHistoryDestination) },
                            adPlaceName = "enter_bs_list"
                        )
                    }
                },
                containerBrush = AppTheme.Color.BsBrush,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(horizontal = 56.dp)
            )

            BlankSpacer(height = 16.dp)

            SmartRectAd(
                pageType = SmartAdPageType.HOME_CONTENT,
                bannerAdPlace = BannerAdPlace.BS,
                nativeAdPlace = NativeAdPlace.Bs,
            )

            BlankSpacer(height = 16.dp)

            viewState.bsArticles?.let {
                RecommendedReading(
                    title = stringResource(R.string.text_recommended_reading),
                    onArticleClick = { article ->
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("enter_article", onAdLoadingAfter = {
                                navigator.navigate(ArticleDestination(article))
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(ArticleDestination(article)) },
                                adPlaceName = "enter_article"
                            )
                        }
                    },
                    articles = viewState.bsArticles,
                    modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp)
                )
            }
        }
    }
}

@Composable
private fun BsStatPicChart(
    bsStatMap: Map<BsStatus, Int>,
    recordsCount: Int,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape12Dp,
        color = Color(0xFFF6F2FF),
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = 18.dp)
                .fillMaxWidth()
        ) {
            Box(modifier = Modifier.bodyWidth()) {
                AndroidView(
                    factory = {
                        PieChart(it).apply {
                            legend.apply {
                                isEnabled = false
                            }

                            val typeface = statTypeface(it)

                            // Configure the pie chart
                            description.isEnabled = false
                            centerText = recordsCount.toString()
                            typeface?.let {
                                setCenterTextTypeface(typeface)
                            }
                            setCenterTextSize(20f)
                            setCenterTextColor(Color(0xFF7B5BA1).toArgb())
                            setDrawEntryLabels(false)
//                            animateY(700)
                        }
                    },
                    modifier = Modifier.size(192.dp)
                ) {
                    debugLog { "BS Pie Chart UPDATE" }
                    it.apply {
                        val entries = listOf(
                            PieEntry(
                                bsStatMap[BsStatus.Low]?.toFloat() ?: 0f,
                                "Low"
                            ),
                            PieEntry(
                                bsStatMap[BsStatus.Normal]?.toFloat() ?: 0f,
                                "Normal"
                            ),
                            PieEntry(
                                bsStatMap[BsStatus.PreDiabetes]?.toFloat() ?: 0f,
                                "PreDiabetes"
                            ),
                            PieEntry(
                                bsStatMap[BsStatus.Diabetes]?.toFloat() ?: 0f,
                                "Diabetes"
                            )
                        )

                        val typeface = statTypeface(it.context)

                        // Create a PieDataSet object
                        val dataSet = PieDataSet(entries, "Pie Data Set").apply {
                            valueTextSize = 16f
                            valueTextColor = android.graphics.Color.WHITE
                            typeface?.let {
                                valueTypeface = typeface
                            }
                            valueFormatter = object : ValueFormatter() {
                                override fun getFormattedValue(value: Float): String {
                                    return if (value == 0f) "" else value.toInt().toString()
                                }
                            }
                        }

                        dataSet.colors = listOf(
                            AppTheme.BsStatusColor.low.toArgb(),
                            AppTheme.BsStatusColor.normal.toArgb(),
                            AppTheme.BsStatusColor.preDiabetes.toArgb(),
                            AppTheme.BsStatusColor.diabetes.toArgb(),
                        )

                        // Create a PieData object
                        val pieData = PieData(dataSet)

                        // Set the data to the pie chart
                        data = pieData

                        // Configure the pie chart
                        centerText = recordsCount.toString()
                        animateY(1000)
                    }
                }
            }

            BlankSpacer(height = 10.dp)

            Row(modifier = Modifier.fillMaxWidth()) {
                BsStatPicChartSmallLabel(
                    status = BsStatus.Low,
                    modifier = Modifier.weight(0.8f)
                )

                BsStatPicChartSmallLabel(
                    status = BsStatus.Normal,
                    modifier = Modifier.weight(1f)
                )

                BsStatPicChartSmallLabel(
                    status = BsStatus.PreDiabetes,
                    modifier = Modifier.weight(1.3f)
                )

                BsStatPicChartSmallLabel(
                    status = BsStatus.Diabetes,
                    modifier = Modifier.weight(1.1f)
                )
            }
        }
    }
}

@Composable
private fun BsStatPicChartSmallLabel(
    status: BsStatus,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Surface(
            color = status.color,
            shape = CircleShape,
            content = {},
            modifier = Modifier.size(10.dp)
        )

        BlankSpacer(width = 6.dp)

        Text(
            text = stringResource(id = status.titleStringId),
            style = MaterialTheme.typography.labelSmall.copy(color = Color(0xFFA38DBE)),
            maxLines = 1,
        )
    }
}

@Composable
private fun BsStat(
    viewState: BloodSugarViewState,
    modifier: Modifier = Modifier
) {
    Surface(modifier = modifier.defShadow(), shape = RoundedCornerShape12Dp) {
        Column(modifier = Modifier.fillMaxWidth()) {
            BlankSpacer(height = 20.dp)
            BsRecordStateSwitch(viewState.bsRecordState, modifier = Modifier.bodyWidth())
            BlankSpacer(height = 12.dp)

            Row {
                val (latestValue, avgValue) = when (viewState.bsUnit) {
                    BsUnit.SI -> Pair(viewState.latestBsMmolL, viewState.avgBsMmolL)
                    BsUnit.NonSI -> Pair(viewState.latestBsMgDl, viewState.avgBsMgDl)
                }

                BsStatValue(
                    title = stringResource(R.string.text_recent),
                    value = latestValue?.scale(1, RoundingMode.DOWN)?.toString() ?: "- -",
                    unit = viewState.bsUnit.text,
                    modifier = Modifier.weight(1f)
                )

                BsStatValue(
                    title = stringResource(R.string.text_average),
                    value = avgValue?.scale(1, RoundingMode.DOWN)?.toString() ?: "- -",
                    unit = viewState.bsUnit.text,
                    modifier = Modifier.weight(1f)
                )
            }

            BlankSpacer(height = 12.dp)

            BsStatPicChart(
                bsStatMap = viewState.bsStatMap,
                recordsCount = viewState.bsRecords.size,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            BlankSpacer(height = 20.dp)
        }
    }
}

@Composable
private fun BsStatValue(
    title: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textSecondaryDark)
        )

        BlankSpacer(height = 6.dp)

        Text(text = value, style = MaterialTheme.typography.headlineSmall)

        BlankSpacer(height = 6.dp)

        Text(
            text = unit,
            style = MaterialTheme.typography.titleMedium.copy(color = AppTheme.Color.textSecondaryDark)
        )
    }
}

@Composable
fun BsRecordStateSwitch(
    bsRecordState: BsRecordState,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke((1.5).dp, AppTheme.Color.BsPrimary)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.height(IntrinsicSize.Max)
        ) {
            Text(
                text = stringResource(id = bsRecordState.textId),
                style = MaterialTheme.typography.bodySmall.copy(color = AppTheme.Color.BsPrimary),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(vertical = 8.dp)
            )

//            Surface(
//                shape = RectangleShape,
//                color = AppTheme.Color.BsPrimary,
//                contentColor = Color.White,
//                modifier = Modifier
//                    .fillMaxHeight()
//                    .width(28.dp)
//            ) {
//                Icon(
//                    imageVector = Icons.Rounded.KeyboardArrowDown,
//                    contentDescription = null,
//                )
//            }

        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BsAppBar(
    navUp: () -> Unit,
    openAlarmGroup: () -> Unit,
    openHistory: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = {
                Text(text = stringResource(id = R.string.text_blood_sugar))
            },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                IconButton(onClick = openAlarmGroup) {
                    Icon(
                        imageVector = Icons.Rounded.Alarm,
                        contentDescription = "alarm",
                    )
                }

                IconButton(onClick = openHistory) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_history),
                        contentDescription = "history",
                        modifier = Modifier.size(AppBarActionButtonDpSize)
                    )
                }
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.HOME_TOP_BAR,
            bannerAdPlace = BannerAdPlace.BS,
            nativeAdPlace = NativeAdPlace.Bs,
        )
    }
}

@Composable
private fun BsFab(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    size: Dp = 72.dp,
) {
    Box(modifier = modifier) {
        Image(
            painter = painterResource(id = R.drawable.img_fab_add_bs),
            contentDescription = "add record",
            modifier = Modifier
                .size(size)
                .noRippleClickable(onClick = onClick),
        )
    }
}
