package com.nbpt.app.ui.screen.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Language
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.nbpt.app.BuildConfig
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.SplashManager
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.android.openBrowser
import com.nbpt.app.common.android.openFeedbackDef
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.ExportDataDialog
import com.nbpt.app.ui.common.LinearProgressLoadingDialog
import com.nbpt.app.ui.common.SettingsItem
import com.nbpt.app.ui.screen.destinations.AlarmGroupsDestination
import com.nbpt.app.ui.screen.destinations.LanguageDestination
import com.nbpt.app.ui.screen.destinations.PermissionsManagerDestination
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.bodyWidth
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

@Composable
fun SettingsScreen(
    navigator: DestinationsNavigator,
    viewModel: SettingsViewModel,
) {
    val interstitialAdManager: MaxInterstitialAdManager = koinInject()

    val context = LocalContext.current

    viewModel.collectSideEffect {
        when (it) {
            is SettingsSideEffect.NavTo -> navigator.navigate(it.destination)
        }
    }

    val viewState by viewModel.collectAsState()

    if (viewState.dataExporting) {
        LinearProgressLoadingDialog(
            navUp = navigator::navigateUp,
            title = stringResource(R.string.text_exporting_file),
            userDismissEnable = false
        )
    }

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }


    if (viewState.showExportDataDialog) {
        ExportDataDialog(
            onDismiss = { viewModel.switchShowExportDataDialog(false) },
            onExport = { viewModel.onExport(context, it) }
        )
    }

    Scaffold(
        topBar = {
            SettingsAppBar()
        },
        bottomBar = {
            SettingsBottomBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Layout.bodyMargin)
                    .padding(vertical = Layout.gutter)
            )
        },
        contentWindowInsets = ScaffoldDefaults.contentWindowInsets.exclude(WindowInsets.navigationBars)
    ) {
        val splashManager: SplashManager = koinInject()

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxWidth()
                .padding(horizontal = Layout.bodyMargin)
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = Layout.bodyMargin)

            SettingsItem(
                onItemClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("language", onAdLoadingAfter = {
                            navigator.navigate(LanguageDestination)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = {
                                navigator.navigate(LanguageDestination)
                            },
                            adPlaceName = "language"
                        )
                    }
                },
                iconPainter = rememberVectorPainter(Icons.Rounded.Language),
                title = stringResource(R.string.text_language),
                modifier = Modifier.fillMaxWidth(),
                iconTint = AppTheme.Color.Primary
            )

            BlankSpacer(height = Layout.bodyMargin)

            SettingsItem(
                onItemClick = {
                    if (useLegacyAd) {
                        interstitialAdManager.tryToShowAd("export_file", onAdLoadingAfter = {
                            viewModel.switchShowExportDataDialog(true)
                        })
                    } else {
                        admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                            activity = context.findActivity(),
                            navAction = {
                                viewModel.switchShowExportDataDialog(true)
                            },
                            adPlaceName = "export_file"
                        )
                    }

                    logEventRecord("click_export_file")
                },
                iconPainter = painterResource(id = R.drawable.ic_settings_export),
                title = stringResource(R.string.text_export_data),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = Layout.bodyMargin)

            SettingsItem(
                onItemClick = {
                    navigator.navigate(AlarmGroupsDestination())
//                    viewModel.onTryToShowInterAdAndNavTo(
//                        activity = context.findActivity(),
//                        destination = AlarmGroupsDestination,
//                        adPlaceName = "enter_alarm_list"
//                    )
                    logEventRecord("click_alarm")
                },
                iconPainter = painterResource(id = R.drawable.ic_settings_alarm),
                title = stringResource(R.string.text_alarm),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = Layout.bodyMargin)

            SettingsItem(
                onItemClick = {
                    context.openFeedbackDef()
                    splashManager.doSkipSplash(true)
                    logEventRecord("click_feedback")
                },
                iconPainter = painterResource(id = R.drawable.ic_settings_feedback),
                title = stringResource(R.string.text_send_feedback),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = Layout.bodyMargin)

//            SettingsItem(
//                onItemClick = {
//                    navigator.navigate(AboutDestination)
//                },
//                iconPainter = rememberVectorPainter(image = Icons.Outlined.Info),
//                title = stringResource(R.string.text_about),
//                modifier = Modifier.fillMaxWidth(),
//                iconTint = AppTheme.Color.Primary
//            )

            SettingsItem(
                onItemClick = {
                    navigator.navigate(PermissionsManagerDestination)
                    logEventRecord("click_permissions_manager")
                },
                iconPainter = painterResource(id = R.drawable.ic_settings_permissions_manager),
                title = stringResource(R.string.text_permissions),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = Layout.bodyMargin)

            SettingsItem(
                onItemClick = {
                    context.openBrowser(BuildConfig.privacyPolicyUrl)
                    splashManager.doSkipSplash(true)
                    logEventRecord("click_privacy")
                },
                iconPainter = painterResource(id = R.drawable.ic_pp),
                title = stringResource(R.string.text_privacy_policy),
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = 32.dp)

            Text(
                text = stringResource(R.string.text_version, BuildConfig.VERSION_NAME),
                modifier = Modifier.bodyWidth(),
                style = MaterialTheme.typography.labelSmall.copy(color = AppTheme.Color.textSecondary)
            )

            BlankSpacer(height = 32.dp)

//            NativeAd(place = NativeAdPlaceholder.Settings)
//
//            BlankSpacer(height = 16.dp)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingsAppBar(
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        TopAppBar(
            title = {
                Text(text = stringResource(id = R.string.settings_title))
            },
        )
        SmartRectAd(
            pageType = SmartAdPageType.HOME_TOP_BAR,
            bannerAdPlace = BannerAdPlace.HOME,
            nativeAdPlace = NativeAdPlace.Settings,
        )
    }

}

@Composable
private fun SettingsBottomBar(
    modifier: Modifier = Modifier
) {
//    NativeAd(
//        place = NativeAdPlace.Settings,
//        modifier = modifier
//    )
}
