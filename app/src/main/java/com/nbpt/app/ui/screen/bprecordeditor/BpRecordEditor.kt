package com.nbpt.app.ui.screen.bprecordeditor

import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material.icons.rounded.DeleteForever
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.alarmguide.AddRemindAlarmGuideManager
//import com.nbpt.app.biz.analytics.logEventRecord
//import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.android.showToast
import com.nbpt.app.common.datetime.nowInstant
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.adt.BpStatus
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.BpInfoDatetimeAndNotesEditor
import com.nbpt.app.ui.common.BpInfoEditor
import com.nbpt.app.ui.common.BpStatusImageAndDescription
import com.nbpt.app.ui.common.BpTypeDialog
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.common.DiscardThisXTipsDialog
import com.nbpt.app.ui.screen.destinations.BpRecordNotesDestination
import com.nbpt.app.ui.screen.bpnotesmanager.notesManagerOnBackEvent
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.marosseleng.compose.material3.datetimepickers.date.ui.dialog.DatePickerDialog
import com.marosseleng.compose.material3.datetimepickers.time.domain.TimePickerDefaults
import com.marosseleng.compose.material3.datetimepickers.time.ui.dialog.TimePickerDialog
import com.nbpt.app.biz.admanager.adaptive.SmartAdPageType
import com.nbpt.app.biz.admanager.adaptive.SmartRectAd
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.banner.BannerAdPlaceholder
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.biz.rating.RatingManager
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.ui.screen.destinations.ArticleDestination
import com.nbpt.app.ui.theme.AppBarActionButtonDpSize
import com.nbpt.app.ui.theme.bp.BpTheme

import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import com.ramcosta.composedestinations.navigation.EmptyDestinationsNavigator
import com.ramcosta.composedestinations.result.EmptyResultRecipient
import com.ramcosta.composedestinations.result.NavResult
import com.ramcosta.composedestinations.result.ResultRecipient
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toJavaLocalTime
import kotlinx.datetime.toKotlinLocalDate
import kotlinx.datetime.toKotlinLocalTime
import kotlinx.parcelize.Parcelize
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

sealed interface BpRecordEditorRecipientEvent : Parcelable {

    @Parcelize
    data class NotesSelectionFinish(
        val notesSelection: ArrayList<String>
    ) : BpRecordEditorRecipientEvent

    @Parcelize
    data object NotesManagerNavUp : BpRecordEditorRecipientEvent
}

enum class BpRecordEditorMode(@StringRes val titleStringId: Int) {
    Add(R.string.text_new_record),
    Edit(R.string.text_edit)
}

data class BpRecordEditorNavArgs(
    val editorMode: BpRecordEditorMode? = null,
    val recordUUID: String? = null,
    val instant: Instant = nowInstant(),
    val systolic: Int = 100,
    val diastolic: Int = 75,
    val pulse: Int = 70,
    val notesSelection: ArrayList<String>? = null,
)

@Destination(
    navArgsDelegate = BpRecordEditorNavArgs::class
)
@Composable
fun BpRecordEditor(
    navigator: DestinationsNavigator,
    resultRecipient: ResultRecipient<BpRecordNotesDestination, BpRecordEditorRecipientEvent>,
) {
    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    BpTheme {
        BpRecordEditor(
            navigator = navigator,
            resultRecipient = resultRecipient,
            viewModel = koinViewModel(),
            admobInterstitialAdViewModel = admobInterstitialAdViewModel,
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class)
@Composable
private fun BpRecordEditor(
    navigator: DestinationsNavigator,
    resultRecipient: ResultRecipient<BpRecordNotesDestination, BpRecordEditorRecipientEvent>,
    viewModel: BpRecordEditorViewModel,
    admobInterstitialAdViewModel: AdmobInterstitialAdViewModel
) {
    val context = LocalContext.current

    val ratingManager: RatingManager = koinInject()
    val alarmGuideManager: AddRemindAlarmGuideManager = koinInject()

    BackHandler {
        viewModel.onBack()
    }

    val viewState by viewModel.collectAsState()

    LaunchedEffect(Unit) {
//        viewModel.registerInterAdEventFlow(this)

        notesManagerOnBackEvent.onEach {
            val deletedSelection = viewModel.deletedSelection(it)

            viewModel.onNotesDelete(deletedSelection)

            navigator.navigate(
                BpRecordNotesDestination(
                    notesSelection = ArrayList(
                        viewState.notesSelection - deletedSelection
                    )
                )
            )
        }.launchIn(this)
    }

    resultRecipient.onNavResult { result ->
        when (result) {
            NavResult.Canceled -> {}
            is NavResult.Value -> {
                when (result.value) {
                    is BpRecordEditorRecipientEvent.NotesSelectionFinish -> viewModel.onNotesSelectionUpdate(
                        (result.value as BpRecordEditorRecipientEvent.NotesSelectionFinish).notesSelection
                    )

                    BpRecordEditorRecipientEvent.NotesManagerNavUp -> navigator.navigate(
                        BpRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                }
            }
        }
    }

    viewModel.collectSideEffect {
        when (it) {
            BpRecordEditorSideEffect.NavUp -> {
                navigator.navigateUp()
            }

            is BpRecordEditorSideEffect.SaveAndNavUp -> {
                if (viewModel.navArgs.editorMode == BpRecordEditorMode.Add) {
                    navigator.navigateUp()

                    GlobalScope.launch {
                        val hasOpenRatingSheet =
                            ratingManager.tryToOpenRatingSheet(context.findActivity())

                        debugLog { "hasOpenRatingSheet: $hasOpenRatingSheet" }
                        if (!hasOpenRatingSheet) {
                            alarmGuideManager.tryToOpenGuideDialog()
                        }
                    }
                } else {
                    navigator.navigateUp()
                }
            }

            is BpRecordEditorSideEffect.Toast -> context.showToast(it.content)
        }
    }


    if (viewState.showDatePicker) {
        DatePickerDialog(
            onDismissRequest = viewModel::onDismissDatePicker,
            onDateChange = { viewModel.onDateChange(it.toKotlinLocalDate()) },
            initialDate = viewState.dateTime.date.toJavaLocalDate(),
        )
    }

    if (viewState.showTimePicker) {
        TimePickerDialog(
            onDismissRequest = viewModel::onDismissTimePicker,
            onTimeChange = {
                viewModel.onTimeChange(it.toKotlinLocalTime())
            },
            initialTime = viewState.dateTime.time.toJavaLocalTime(),
            colors = TimePickerDefaults.colors(
                clockDigitsUnselectedBackgroundColor = AppTheme.Color.BpBackground,
                amPmSwitchFieldUnselectedBackgroundColor = AppTheme.Color.BpBackground,
                dialBackgroundColor = AppTheme.Color.BpBackground
            )
        )
    }

    if (viewState.showDeleteRecordDialog) {
        AlertDialog(
            onDismissRequest = viewModel::onDismissDeleteRecordDialog,
            confirmButton = {
                TextButton(onClick = {
                    viewModel.onDismissDeleteRecordDialog()
                    viewModel.onDelete()
                    logEventRecord("click_record_delete")
                }) {
                    Text(text = stringResource(id = R.string.text_delete))
                }
            },
            dismissButton = {
                TextButton(onClick = viewModel::onDismissDeleteRecordDialog) {
                    Text(text = stringResource(id = R.string.text_cancel))
                }
            },
            title = {
                Text(
                    text = stringResource(id = R.string.text_delete_record),
                    style = MaterialTheme.typography.titleLarge,
                )
            },
            text = {
                Text(text = stringResource(id = R.string.text_tips_delete_record))
            }
        )
    }

    if (viewState.showBpTypeDialog) {
        BpTypeDialog(onDismiss = viewModel::onDismissBpTypeDialog)
    }

//    if (viewState.showInterstitialAdLoadingDialog) {
//        AdLoadingDialog()
//    }

    if (viewState.showExitEditorTipsDialog) {
        if (viewState.editorMode == BpRecordEditorMode.Add) {
            DiscardThisXTipsDialog(
                onDismiss = viewModel::onDismissExitEditorTipsDialog,
                onDiscard = {
                    viewModel.onDismissExitEditorTipsDialog()
                    viewModel.onBack(force = true)
                },
                title = stringResource(id = R.string.text_title_discard_record),
            )
        }
    }

    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
    Scaffold(
        topBar = {
            BpRecordEditorAppBar(
                editorMode = viewState.editorMode,
                navUp = viewModel::onBack,
                onQuestionClick = {
                    viewState.bpArticles.firstOrNull()?.let {
                        if (useLegacyAd) {
                            interstitialAdManager.tryToShowAd("enter_article", onAdLoadingAfter = {
                                navigator.navigate(ArticleDestination(it))
                            })
                        } else {
                            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                                activity = context.findActivity(),
                                navAction = { navigate(ArticleDestination(it)) },
                                adPlaceName = "enter_article"
                            )
                        }
                    }
                },
                onDelete = if (viewState.editorMode == BpRecordEditorMode.Edit) {
                    { viewModel.onShowDeleteRecordDialog() }
                } else null
            )
        },
        bottomBar = {
            BpRecordEditorBottomBar(
                onSave = {
                    viewModel.onSave()
                    logEventRecord("click_record_save")
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
            )
        },
        containerColor = AppTheme.Color.BpBackground
    ) {
        val bodyMargin = Layout.bodyMargin
        val gutter = Layout.gutter

        Column(
            modifier = Modifier
                .padding(it)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = 18.dp)

            val bpStatus = BpStatus.from(
                viewState.systolic ?: 100,
                viewState.diastolic ?: 75
            )

            BpStatusImageAndDescription(
                bpStatus = bpStatus,
                onInfoClick = viewModel::onShowBpTypeDialog,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = bodyMargin)
            )

            BlankSpacer(height = bodyMargin)

//                NativeAd(
//                    place = NativeAdPlace.BpRecordEditor,
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(horizontal = bodyMargin),
//                )

//            BlankSpacer(height = 12.dp)

            BpInfoEditor(
                systolic = viewState.systolic ?: 100,
                diastolic = viewState.diastolic ?: 75,
                pulse = viewState.pulse ?: 70,
                onSystolicChange = viewModel::onSystolicChange,
                onDiastolicChange = viewModel::onDiastolicChange,
                onPulseChange = viewModel::onPulseChange,
                modifier = Modifier.fillMaxWidth()
            )

            BlankSpacer(height = gutter / 2)

            BpInfoDatetimeAndNotesEditor(
                noteCount = viewState.notesSelection.size,
                dateTime = viewState.dateTime,
                onDateClick = viewModel::onShowDatePicker,
                onTimeClick = viewModel::onShowTimePicker,
                onNotesClick = {
                    navigator.navigate(
                        BpRecordNotesDestination(
                            notesSelection = ArrayList(
                                viewState.notesSelection
                            )
                        )
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = bodyMargin)
            )

            BlankSpacer(height = 22.dp)

            SmartRectAd(
                pageType = SmartAdPageType.FEAT_CONTENT,
                bannerAdPlace = BannerAdPlace.BP_EDITOR,
                nativeAdPlace = NativeAdPlace.BpEditor,
            )

            BlankSpacer(height = bodyMargin)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BpRecordEditorAppBar(
    editorMode: BpRecordEditorMode?,
    navUp: () -> Unit,
    onQuestionClick: () -> Unit,
    modifier: Modifier = Modifier,
    onDelete: (() -> Unit)? = null,
) {
    Column(modifier) {
        TopAppBar(
            title = { Text(text = if (editorMode == null) "" else stringResource(id = editorMode.titleStringId)) },
            navigationIcon = {
                IconButton(onClick = navUp) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBackIosNew,
                        contentDescription = "back"
                    )
                }
            },
            actions = {
                IconButton(onClick = onQuestionClick) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_question),
                        contentDescription = "",
                        modifier = Modifier.size(AppBarActionButtonDpSize)
                    )
                }

                if (onDelete != null) {
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Rounded.DeleteForever,
                            contentDescription = "delete",
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth()
        )

        SmartRectAd(
            pageType = SmartAdPageType.FEAT_TOP_BAR,
            bannerAdPlace = BannerAdPlace.BP_EDITOR,
            nativeAdPlace = NativeAdPlace.BpEditor,
        )
    }

}

@Composable
private fun BpRecordEditorBottomBar(
    onSave: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        CardButton(
            text = stringResource(id = R.string.text_save),
            onClick = onSave,
            modifier = Modifier
                .requiredWidthIn(max = 264.dp)
                .fillMaxWidth()
                .padding(horizontal = Layout.bodyMargin),
            containerBrush = AppTheme.Color.BpBrush
        )

        BlankSpacer(height = 8.dp)

        SmartRectAd(
            pageType = SmartAdPageType.FEAT_BOTTOM_BAR,
            bannerAdPlace = BannerAdPlace.BP_EDITOR,
            nativeAdPlace = NativeAdPlace.BpEditor,
        )
    }
}

@Preview
@Composable
private fun BpRecordEditorPreview() {
    BpRecordEditor(
        navigator = EmptyDestinationsNavigator,
        resultRecipient = EmptyResultRecipient(),
    )
}
