@file:Suppress("ObjectPropertyName")

package com.nbpt.app.ui.screen.bpnotesmanager

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ElevatedFilterChip
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.nbpt.app.R
import com.nbpt.app.common.EventFlow
import com.nbpt.app.common.send
import com.nbpt.app.data.db.model.BpRecordsNoteEntity
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.LinearProgressLoadingDialog

import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.bodyWidth
import com.nbpt.app.ui.theme.bp.BpTheme
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

val notesManagerOnBackEvent = EventFlow<List<BpRecordsNoteEntity>>()

@Destination
@Composable
fun BpNotesManager(
    navigator: DestinationsNavigator,
) {

    val viewModel: BpNotesManagerViewModel = koinViewModel()

    viewModel.collectSideEffect {
        when (it) {
            BpNotesManagerSideEffect.NavUp -> navigator.navigateUp()
        }
    }

    val viewState by viewModel.collectAsState()

    BpTheme {
        BpNotesManager(
            navigator = navigator,
            viewModel = viewModel,
            viewState = viewState,
        )
    }

    DisposableEffect(Unit) {
        onDispose {
            notesManagerOnBackEvent.send(viewModel.pendingDeleteNotes)
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun BpNotesManager(
    navigator: DestinationsNavigator,
    viewModel: BpNotesManagerViewModel,
    viewState: BpNotesManagerViewState,
) {

    if (viewState.notesSaving) {
        LinearProgressLoadingDialog(
            navUp = {},
            title = stringResource(R.string.text_saving),
            userDismissEnable = false
        )
    }

    if (viewState.addNoteDialogShow) {
        AddNoteDialog(
            onDismiss = viewModel::onDismissAddNoteDialog,
            onAddNote = viewModel::onAdd
        )
    }

    Scaffold(
        topBar = {
            NotesManagerAppBar(
                navUp = navigator::navigateUp,
                modifier = Modifier.fillMaxWidth()
            )
        },
        bottomBar = {
            NotesBottomBar(
                onShowAddNoteDialog = viewModel::onShowAddNoteDialog,
                onSave = viewModel::onSave,
                modifier = Modifier.navigationBarsPadding()
            )
        },
        containerColor = AppTheme.Color.BpBackground
    ) { paddingValues ->

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingValues)
                .padding(Layout.bodyMargin)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
        ) {
            FlowRow {
                viewState.notes.forEach { note ->
                    EditableNoteChip(
                        note = note,
                        onDeleteClick = viewModel::onDelete,
                        modifier = Modifier.padding(
                            horizontal = Layout.gutter / 2,
                        )
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun NotesManagerAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_note_management)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.Close,
                    contentDescription = "close"
                )
            }
        }
    )
}

@Composable
private fun NotesBottomBar(
    onShowAddNoteDialog: () -> Unit,
    onSave: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .bodyWidth()
            .requiredWidthIn(min = 400.dp)
            .padding(Layout.bodyMargin)
            .padding(horizontal = Layout.bodyMargin)
    ) {

        val textStyle = MaterialTheme.typography.titleLarge

        Surface(
            onClick = onShowAddNoteDialog,
            shape = CircleShape,
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.padding(vertical = Layout.bodyMargin)
            ) {
                Text(
                    text = stringResource(R.string.text_add_add),
                    style = textStyle.copy(color = AppTheme.Color.BpPrimary)
                )
            }
        }

        BlankSpacer(height = Layout.bodyMargin)

        Surface(
            onClick = onSave,
            shape = CircleShape,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.background(brush = AppTheme.Color.BpBrush)
            ) {
                Box(
                    modifier = Modifier.padding(vertical = Layout.bodyMargin)
                ) {
                    Text(
                        text = stringResource(R.string.text_save),
                        style = textStyle.copy(color = AppTheme.Color.White),
                    )
                }

            }
        }

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EditableNoteChip(
    note: BpRecordsNoteEntity,
    onDeleteClick: (BpRecordsNoteEntity) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier, contentAlignment = Alignment.Center) {
        ElevatedFilterChip(
            selected = false,
            onClick = {},
            label = { Text(text = note.content) },
            trailingIcon = {
                Icon(
                    imageVector = Icons.Rounded.Close,
                    contentDescription = "delete",
                    modifier = Modifier.clickable { onDeleteClick(note) })
            },
            elevation = FilterChipDefaults.filterChipElevation(),
            shape = CircleShape,
            colors = FilterChipDefaults.elevatedFilterChipColors(
                containerColor = AppTheme.Color.White,
                labelColor = AppTheme.Color.textPrimary,
                iconColor = AppTheme.Color.textPrimary,
            )
        )
    }
}

@Composable
private fun AddNoteDialog(
    onDismiss: () -> Unit,
    onAddNote: (String) -> Unit
) {
    var input by remember {
        mutableStateOf("")
    }

    val textFieldFocusRequester = FocusRequester()

    LaunchedEffect(Unit) {
        delay(250)
        textFieldFocusRequester.requestFocus()
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.text_add_note),
                style = MaterialTheme.typography.titleLarge,
            )
        },
        text = {
            OutlinedTextField(
                value = input,
                onValueChange = {
                    input = it
                },
                modifier = Modifier.focusRequester(textFieldFocusRequester),
                placeholder = {
                    Text(
                        text = stringResource(R.string.text_please_enter_something),
                        style = MaterialTheme.typography.bodyLarge.copy(
                            color = AppTheme.Color.textSecondary,
                            fontWeight = FontWeight.Normal
                        )
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(focusedBorderColor = AppTheme.Color.BpPrimary),
                shape = RoundedCornerShape12Dp,
            )
        },
        confirmButton = {
            TextButton(onClick = { onAddNote(input) }) {
                Text(text = stringResource(R.string.text_ok), color = AppTheme.Color.BpPrimary)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(text = stringResource(R.string.text_cancel), color = AppTheme.Color.BpPrimary)
            }
        }
    )
}
