package com.nbpt.app.ui.screen.bmicalculator

import androidx.lifecycle.ViewModel
import com.nbpt.app.common.calculate.bmi
import com.nbpt.app.common.calculate.cmToFtAndIn
import com.nbpt.app.common.calculate.ftAndInToCm
import com.nbpt.app.common.calculate.kgToLbs
import com.nbpt.app.common.calculate.lbsToKg
import com.nbpt.app.common.calculate.scale
import com.nbpt.app.data.adt.BmiCalculateUnit
import com.nbpt.app.data.adt.BmiStatus
import com.nbpt.app.data.datastore.UserBehaviorDataStore
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class BmiCalculatorViewModel(
    private val userBehaviorDataStore: UserBehaviorDataStore
) : ViewModel(),
    ContainerHost<BmiCalculatorViewState, BmiCalculatorSideEffect> {
    override val container: Container<BmiCalculatorViewState, BmiCalculatorSideEffect> =
        container(BmiCalculatorViewState())

    init {
        intent { reduce { state.importCache(userBehaviorDataStore.bmiCalculatorCache) } }
    }

    fun onUnitSwitch(bmiCalculateUnit: BmiCalculateUnit) = intent {
        reduce { state.copy(bmiCalculateUnit = bmiCalculateUnit) }
    }

    fun onCmChange(cm: Int) = intent {
        val (ft, `in`) = cmToFtAndIn(cm.toDouble())
        reduce { state.copy(cm = cm, ft = ft, `in` = `in`) }
    }

    fun onFtChange(ft: Int) = intent {
        val cm = ftAndInToCm(ft, state.`in`)
        reduce { state.copy(ft = ft, cm = cm) }
    }

    fun onInChange(`in`: Int) = intent {
        val cm = ftAndInToCm(state.ft, `in`)
        reduce { state.copy(`in` = `in`, cm = cm) }
    }

    fun onKgChange(kg: Float) = intent {
        val lbs = kgToLbs(kg).scale(1)
        reduce { state.copy(kg = kg, lbs = lbs) }
    }

    fun onLbsChange(lbs: Float) = intent {
        val kg = lbsToKg(lbs).scale(1)
        reduce { state.copy(lbs = lbs, kg = kg) }
    }

    fun onCalculate() = intent {
        val bmi = bmi(cm = state.cm, kg = state.kg).scale(2)
        val bmiStatus = BmiStatus.from(bmi)

        reduce { state.copy(hasCalculate = true) }

        postSideEffect(BmiCalculatorSideEffect.ToResult(bmi, bmiStatus))
    }

    fun onCacheData() = intent {
        userBehaviorDataStore.bmiCalculatorCache = state.toBmiCalculatorCache()
    }
}