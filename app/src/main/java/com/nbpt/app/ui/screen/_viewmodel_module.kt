package com.nbpt.app.ui.screen

import com.nbpt.app.ui.screen.adloadingdialog.InterAdLoadingDialogViewModel
import com.nbpt.app.ui.screen.alarmeditor.AlarmEditorViewModel
import com.nbpt.app.ui.screen.alarmgroups.AlarmGroupsViewModel
import com.nbpt.app.ui.screen.article.ArticleViewModel
import com.nbpt.app.ui.screen.guide.GuideViewModel
import com.nbpt.app.ui.screen.heartrate.HeartRateViewModel
import com.nbpt.app.ui.screen.heartrateeditor.HeartRateEditorViewModel
import com.nbpt.app.ui.screen.heartratehistory.HeartRateHistoryViewModel
import com.nbpt.app.ui.screen.heartratemeasure.HeartRateMeasureViewModel
import com.nbpt.app.ui.screen.heartraterecordnotes.HeartRateRecordNotesViewModel
import com.nbpt.app.ui.screen.heartraterecordnotesmanager.HeartRateRecordNotesManagerViewModel
import com.nbpt.app.ui.screen.history.HistoryViewModel
import com.nbpt.app.ui.screen.home.HomeViewModel
import com.nbpt.app.ui.screen.info.InfoViewModel
import com.nbpt.app.ui.screen.bpnotesmanager.BpNotesManagerViewModel
import com.nbpt.app.ui.screen.anr.AnrViewModel
import com.nbpt.app.ui.screen.bloodsugar.BloodSugarViewModel
import com.nbpt.app.ui.screen.bloodsugarhistory.BsHistoryViewModel
import com.nbpt.app.ui.screen.bloodsugarnotemanager.BsRecordNotesManagerViewModel
import com.nbpt.app.ui.screen.bloodsugarrecordeditor.BsRecordEditorViewModel
import com.nbpt.app.ui.screen.bloodsugarrecordnote.BsRecordNotesViewModel
import com.nbpt.app.ui.screen.bmicalculator.BmiCalculatorViewModel
import com.nbpt.app.ui.screen.bprecordeditor.BpRecordEditorViewModel
import com.nbpt.app.ui.screen.bprecordnotes.BpRecordNotesViewModel
import com.nbpt.app.ui.screen.settings.SettingsViewModel
import com.nbpt.app.ui.screen.splash.SplashViewModel
import com.nbpt.app.ui.screen.bp.TrackerViewModel
import com.nbpt.app.ui.screen.language.LanguageViewModel
import com.nbpt.app.ui.screen.permissonsmanager.PermissionsViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val viewModelsModule = module {
    viewModelOf(::HomeViewModel)
    viewModelOf(::TrackerViewModel)
    viewModelOf(::InfoViewModel)
    viewModelOf(::SettingsViewModel)
    viewModelOf(::BpRecordEditorViewModel)
    viewModelOf(::BpRecordNotesViewModel)
    viewModelOf(::BpNotesManagerViewModel)
    viewModelOf(::HistoryViewModel)
    viewModelOf(::SplashViewModel)
    viewModelOf(::ArticleViewModel)
    viewModelOf(::GuideViewModel)
    viewModelOf(::AlarmEditorViewModel)
    viewModelOf(::AlarmGroupsViewModel)
    viewModelOf(::HeartRateMeasureViewModel)
    viewModelOf(::HeartRateEditorViewModel)
    viewModelOf(::HeartRateHistoryViewModel)
    viewModelOf(::HeartRateRecordNotesViewModel)
    viewModelOf(::HeartRateRecordNotesManagerViewModel)
    viewModelOf(::HeartRateViewModel)
    viewModelOf(::AnrViewModel)
    viewModelOf(::BmiCalculatorViewModel)
    viewModelOf(::BloodSugarViewModel)
    viewModelOf(::BsHistoryViewModel)
    viewModelOf(::BsRecordEditorViewModel)
    viewModelOf(::BsRecordNotesViewModel)
    viewModelOf(::BsRecordNotesManagerViewModel)
    viewModelOf(::PermissionsViewModel)
    viewModelOf(::InterAdLoadingDialogViewModel)
    viewModelOf(::LanguageViewModel)
}
