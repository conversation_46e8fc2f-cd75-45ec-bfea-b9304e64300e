package com.nbpt.app.ui.common

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.nbpt.app.R

@Composable
fun DiscardThisXTipsDialog(
    onDismiss: () -> Unit,
    onDiscard: () -> Unit,
    title: String,
    confirmText: String = stringResource(R.string.text_discard),
    dismissText: String = stringResource(R.string.text_cancel)
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
            )
        },
        confirmButton = {
            TextButton(
                onClick = onDiscard,
            ) {
                Text(text = confirmText)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(text = dismissText)
            }
        }
    )
}
