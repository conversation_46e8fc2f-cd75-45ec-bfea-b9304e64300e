package com.nbpt.app.ui.screen.bprecordeditor

import com.nbpt.app.data.healtharticles.HealthArticle
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.LocalTime

data class BpRecordEditorViewState(
    val editorMode: BpRecordEditorMode? = null,
    val dateTime: LocalDateTime = LocalDateTime(
        LocalDate.fromEpochDays(0),
        LocalTime.fromSecondOfDay(0)
    ),
    val systolic: Int? = 100,
    val diastolic: Int? = 75,
    val pulse: Int? = 70,
    val notesSelection: List<String> = emptyList(),
    val showDatePicker: Boolean = false,
    val showTimePicker: Boolean = false,
    val showDeleteRecordDialog: Boolean = false,
    val showBpTypeDialog: Boolean = false,
    val showInterstitialAdLoadingDialog: Boolean = false,
    val showExitEditorTipsDialog: Boolean = false,

    val bpArticles: List<HealthArticle> = emptyList(),
) {
    companion object {
        val Empty = BpRecordEditorViewState()
    }
}
