package com.nbpt.app.ui.theme.hr
import androidx.compose.ui.graphics.Color
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.seed

val md_theme_light_primary = AppTheme.Color.HrPrimary
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color.White
val md_theme_light_onPrimaryContainer = seed
val md_theme_light_secondary = Color(0xFF974065)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = md_theme_light_primaryContainer
val md_theme_light_onSecondaryContainer = Color(0xFF3E0020)
val md_theme_light_tertiary = Color(0xFF974065)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = md_theme_light_primaryContainer
val md_theme_light_onTertiaryContainer = seed
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFFFBFF)
val md_theme_light_onBackground = seed
val md_theme_light_surface = md_theme_light_primaryContainer
val md_theme_light_onSurface = seed
val md_theme_light_surfaceVariant = Color(0xFFF2DDE2)
val md_theme_light_onSurfaceVariant = seed
val md_theme_light_outline = Color(0xFF837377)
val md_theme_light_inverseOnSurface = Color(0xFFFFEBFF)
val md_theme_light_inverseSurface = Color(0xFF4D1661)
val md_theme_light_inversePrimary = Color(0xFFFFB0CC)
val md_theme_light_shadow = Color(0xFF000000)
val md_theme_light_surfaceTint = md_theme_light_primaryContainer
val md_theme_light_outlineVariant = Color(0xFFD5C2C6)
val md_theme_light_scrim = Color(0xFF000000)

val md_theme_dark_primary = Color(0xFFFFB0CC)
val md_theme_dark_onPrimary = Color(0xFF5D1136)
val md_theme_dark_primaryContainer = Color(0xFF7A294D)
val md_theme_dark_onPrimaryContainer = Color(0xFFFFD9E4)
val md_theme_dark_secondary = Color(0xFFFFB0CC)
val md_theme_dark_onSecondary = Color(0xFF5D1136)
val md_theme_dark_secondaryContainer = Color(0xFF7A294D)
val md_theme_dark_onSecondaryContainer = Color(0xFFFFD9E4)
val md_theme_dark_tertiary = Color(0xFFFFB0CC)
val md_theme_dark_onTertiary = Color(0xFF5D1136)
val md_theme_dark_tertiaryContainer = Color(0xFF7A294D)
val md_theme_dark_onTertiaryContainer = Color(0xFFFFD9E4)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF330045)
val md_theme_dark_onBackground = Color(0xFFFAD7FF)
val md_theme_dark_surface = Color(0xFF330045)
val md_theme_dark_onSurface = Color(0xFFFAD7FF)
val md_theme_dark_surfaceVariant = Color(0xFF514347)
val md_theme_dark_onSurfaceVariant = Color(0xFFD5C2C6)
val md_theme_dark_outline = Color(0xFF9D8C91)
val md_theme_dark_inverseOnSurface = Color(0xFF330045)
val md_theme_dark_inverseSurface = Color(0xFFFAD7FF)
val md_theme_dark_inversePrimary = Color(0xFF974065)
val md_theme_dark_shadow = Color(0xFF000000)
val md_theme_dark_surfaceTint = Color(0xFFFFB0CC)
val md_theme_dark_outlineVariant = Color(0xFF514347)
val md_theme_dark_scrim = Color(0xFF000000)
