package com.nbpt.app.ui.screen.heartratehistory

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.ViewModel
//import com.nbpt.app.biz.admanager.interstitial.InterstitialAdManager
import com.nbpt.app.data.db.dao.HrRecordDao
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

class HeartRateHistoryViewModel(
    private val hrRecordDao: HrRecordDao,
//    private val interstitialAdManager: InterstitialAdManager,
) : ViewModel(), ContainerHost<HeartRateHistoryViewState, HeartRateHistorySideEffect> {

    override val container: Container<HeartRateHistoryViewState, HeartRateHistorySideEffect> =
        container(HeartRateHistoryViewState.Empty)

    fun onRefresh() = intent {
        val hrRecords = hrRecordDao.fetchAllRecordsFlow().first()

        reduce {
            state.copy(
                hrRecords = hrRecords,
                showNoRecordsContent = hrRecords.isEmpty()
            )
        }
    }

//    fun onBack(activity: Activity) = intent {
//        reduce { state.copy(showInterstitialAdLoadingDialog = true) }
//
//        showInterAdContainer = activity
//
//        if (!hasInterAdShowing) {
//            interstitialAdManager.tryToShowAd(activity, "exit_hr_list")
//        }
//    }
//
//    @Volatile
//    private var hasInterAdShowing = false
//
//    @SuppressLint("StaticFieldLeak")
//    private var showInterAdContainer: Activity? = null
//    fun registerInterAdEventFlow(
//        lifecycleScope: CoroutineScope
//    ) {
//        interstitialAdManager.adLoadingStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdLoadingStateEvent.TimeOut,
//                InterstitialAdManager.AdLoadingStateEvent.FailedToLoad -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(HeartRateHistorySideEffect.NavUp)
//                    }
//                }
//
//                InterstitialAdManager.AdLoadingStateEvent.Loaded -> {
//                    showInterAdContainer?.let { containerActivity ->
//                        if (!hasInterAdShowing) {
//                            interstitialAdManager.tryToShowAd(containerActivity, null)
//                        }
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//
//        interstitialAdManager.adShowStateEventFlow.onEach {
//            when (it) {
//                InterstitialAdManager.AdShowStateEvent.FailedToShow,
//                InterstitialAdManager.AdShowStateEvent.Finish,
//                InterstitialAdManager.AdShowStateEvent.SkipToShow -> intent {
//                    reduce { state.copy(showInterstitialAdLoadingDialog = false) }
//
//                    if (showInterAdContainer != null) {
//                        showInterAdContainer = null
//                        hasInterAdShowing = false
//
//                        postSideEffect(HeartRateHistorySideEffect.NavUp)
//                    }
//                }
//
//                InterstitialAdManager.AdShowStateEvent.Showing -> {
//                    if (showInterAdContainer != null) {
//                        hasInterAdShowing = true
//                    }
//                }
//            }
//        }.launchIn(lifecycleScope)
//    }
}
