package com.nbpt.app.ui.screen.guidepermissions

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.common.CardButton
import com.nbpt.app.ui.theme.AppMd3Theme
import com.nbpt.app.ui.theme.AppTheme
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp

@Composable
fun GuidePermissionContent(
    icon: Painter,
    title: String,
    description: String,
    onNext: () -> Unit,
    onSkip: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val primaryLightAlpha15 = AppTheme.Color.PrimaryLight.copy(.15f)

    Column(
        modifier = modifier.background(color = AppTheme.Color.PrimaryBackground),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        BlankSpacer(height = WindowInsets.statusBars.asPaddingValues().calculateTopPadding())
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
            TextButton(onClick = onSkip) {
                Text(
                    text = stringResource(id = R.string.text_skip),
                    fontSize = 15.5.sp,
                    color = AppTheme.Color.textSecondary
                )
            }
        }

        Spacer(modifier = Modifier.weight(0.6f))

        Image(
            painter = painterResource(id = R.drawable.launcher_icon_256),
            contentDescription = null,
            modifier = Modifier.size(92.dp)
        )

        BlankSpacer(height = 20.dp)

        Text(
            text = stringResource(id = R.string.app_name),
            style = MaterialTheme.typography.titleLarge
        )

        BlankSpacer(height = 16.dp)

        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier
                .padding(horizontal = 16.dp),
            textAlign = TextAlign.Center
        )

        BlankSpacer(height = 30.dp)

        Surface(
            shape = RoundedCornerShape(10.dp),
            color = primaryLightAlpha15,
            modifier = Modifier.padding(horizontal = 40.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                BlankSpacer(height = 20.dp)

                Image(
                    painter = icon,
                    contentDescription = null,
                    modifier = Modifier.size(54.dp),
                )

                BlankSpacer(height = 16.dp)

                Text(
                    text = description,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.titleMedium,
                )

                BlankSpacer(height = 16.dp)

                Surface(
                    shape = RoundedCornerShape12Dp,
                    color = AppTheme.Color.PrimaryMedium,
                    contentColor = Color.White
                ) {
                    Text(
                        text = stringResource(id = R.string.text_allow),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        textAlign = TextAlign.Center
                    )
                }

                BlankSpacer(height = 8.dp)

                Surface(
                    shape = RoundedCornerShape12Dp,
                    color = primaryLightAlpha15
                ) {
                    Text(
                        text = stringResource(id = R.string.text_dont_allow),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        textAlign = TextAlign.Center,
                        color = primaryLightAlpha15
                    )
                }

                BlankSpacer(height = 20.dp)
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        CardButton(
            text = stringResource(R.string.text_next),
            onClick = onNext,
            modifier = Modifier
                .padding(horizontal = 84.dp)
                .fillMaxWidth(),
        )

        Spacer(modifier = Modifier.weight(1f))
    }
}

@Preview(
    device = Devices.PIXEL_4
)
@Composable
private fun GuidePermissionContentPreview() {
    AppMd3Theme {
        GuidePermissionContent(
            icon = painterResource(id = R.drawable.ic_permission_notification),
            title = "Accept daily reminder from the app",
            description = "Allow Blood Pressure Tool to send you notifications",
            onNext = { /*TODO*/ },
            onSkip = { /*TODO*/ },
            modifier = Modifier.fillMaxSize()
        )
    }
}