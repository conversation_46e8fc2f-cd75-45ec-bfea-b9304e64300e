package com.nbpt.app.ui.theme.typography

import androidx.compose.ui.text.TextStyle

object AppTypographyTokens {
    val BodyLarge =
        TextStyle(
            fontFamily = AppTypeScaleTokens.BodyLargeFont,
            fontWeight = AppTypeScaleTokens.BodyLargeWeight,
            fontSize = AppTypeScaleTokens.BodyLargeSize,
            lineHeight = AppTypeScaleTokens.BodyLargeLineHeight,
            letterSpacing = AppTypeScaleTokens.BodyLargeTracking,
        )
    val BodyMedium =
        TextStyle(
            fontFamily = AppTypeScaleTokens.BodyMediumFont,
            fontWeight = AppTypeScaleTokens.BodyMediumWeight,
            fontSize = AppTypeScaleTokens.BodyMediumSize,
            lineHeight = AppTypeScaleTokens.BodyMediumLineHeight,
            letterSpacing = AppTypeScaleTokens.BodyMediumTracking,
        )
    val BodySmall =
        TextStyle(
            fontFamily = AppTypeScaleTokens.BodySmallFont,
            fontWeight = AppTypeScaleTokens.BodySmallWeight,
            fontSize = AppTypeScaleTokens.BodySmallSize,
            lineHeight = AppTypeScaleTokens.BodySmallLineHeight,
            letterSpacing = AppTypeScaleTokens.BodySmallTracking,
        )
    val DisplayLarge =
        TextStyle(
            fontFamily = AppTypeScaleTokens.DisplayLargeFont,
            fontWeight = AppTypeScaleTokens.DisplayLargeWeight,
            fontSize = AppTypeScaleTokens.DisplayLargeSize,
            lineHeight = AppTypeScaleTokens.DisplayLargeLineHeight,
            letterSpacing = AppTypeScaleTokens.DisplayLargeTracking,
        )
    val DisplayMedium =
        TextStyle(
            fontFamily = AppTypeScaleTokens.DisplayMediumFont,
            fontWeight = AppTypeScaleTokens.DisplayMediumWeight,
            fontSize = AppTypeScaleTokens.DisplayMediumSize,
            lineHeight = AppTypeScaleTokens.DisplayMediumLineHeight,
            letterSpacing = AppTypeScaleTokens.DisplayMediumTracking,
        )
    val DisplaySmall =
        TextStyle(
            fontFamily = AppTypeScaleTokens.DisplaySmallFont,
            fontWeight = AppTypeScaleTokens.DisplaySmallWeight,
            fontSize = AppTypeScaleTokens.DisplaySmallSize,
            lineHeight = AppTypeScaleTokens.DisplaySmallLineHeight,
            letterSpacing = AppTypeScaleTokens.DisplaySmallTracking,
        )
    val HeadlineLarge =
        TextStyle(
            fontFamily = AppTypeScaleTokens.HeadlineLargeFont,
            fontWeight = AppTypeScaleTokens.HeadlineLargeWeight,
            fontSize = AppTypeScaleTokens.HeadlineLargeSize,
            lineHeight = AppTypeScaleTokens.HeadlineLargeLineHeight,
            letterSpacing = AppTypeScaleTokens.HeadlineLargeTracking,
        )
    val HeadlineMedium =
        TextStyle(
            fontFamily = AppTypeScaleTokens.HeadlineMediumFont,
            fontWeight = AppTypeScaleTokens.HeadlineMediumWeight,
            fontSize = AppTypeScaleTokens.HeadlineMediumSize,
            lineHeight = AppTypeScaleTokens.HeadlineMediumLineHeight,
            letterSpacing = AppTypeScaleTokens.HeadlineMediumTracking,
        )
    val HeadlineSmall =
        TextStyle(
            fontFamily = AppTypeScaleTokens.HeadlineSmallFont,
            fontWeight = AppTypeScaleTokens.HeadlineSmallWeight,
            fontSize = AppTypeScaleTokens.HeadlineSmallSize,
            lineHeight = AppTypeScaleTokens.HeadlineSmallLineHeight,
            letterSpacing = AppTypeScaleTokens.HeadlineSmallTracking,
        )
    val LabelLarge =
        TextStyle(
            fontFamily = AppTypeScaleTokens.LabelLargeFont,
            fontWeight = AppTypeScaleTokens.LabelLargeWeight,
            fontSize = AppTypeScaleTokens.LabelLargeSize,
            lineHeight = AppTypeScaleTokens.LabelLargeLineHeight,
            letterSpacing = AppTypeScaleTokens.LabelLargeTracking,
        )
    val LabelMedium =
        TextStyle(
            fontFamily = AppTypeScaleTokens.LabelMediumFont,
            fontWeight = AppTypeScaleTokens.LabelMediumWeight,
            fontSize = AppTypeScaleTokens.LabelMediumSize,
            lineHeight = AppTypeScaleTokens.LabelMediumLineHeight,
            letterSpacing = AppTypeScaleTokens.LabelMediumTracking,
        )
    val LabelSmall =
        TextStyle(
            fontFamily = AppTypeScaleTokens.LabelSmallFont,
            fontWeight = AppTypeScaleTokens.LabelSmallWeight,
            fontSize = AppTypeScaleTokens.LabelSmallSize,
            lineHeight = AppTypeScaleTokens.LabelSmallLineHeight,
            letterSpacing = AppTypeScaleTokens.LabelSmallTracking,
        )
    val TitleLarge =
        TextStyle(
            fontFamily = AppTypeScaleTokens.TitleLargeFont,
            fontWeight = AppTypeScaleTokens.TitleLargeWeight,
            fontSize = AppTypeScaleTokens.TitleLargeSize,
            lineHeight = AppTypeScaleTokens.TitleLargeLineHeight,
            letterSpacing = AppTypeScaleTokens.TitleLargeTracking,
        )
    val TitleMedium =
        TextStyle(
            fontFamily = AppTypeScaleTokens.TitleMediumFont,
            fontWeight = AppTypeScaleTokens.TitleMediumWeight,
            fontSize = AppTypeScaleTokens.TitleMediumSize,
            lineHeight = AppTypeScaleTokens.TitleMediumLineHeight,
            letterSpacing = AppTypeScaleTokens.TitleMediumTracking,
        )
    val TitleSmall =
        TextStyle(
            fontFamily = AppTypeScaleTokens.TitleSmallFont,
            fontWeight = AppTypeScaleTokens.TitleSmallWeight,
            fontSize = AppTypeScaleTokens.TitleSmallSize,
            lineHeight = AppTypeScaleTokens.TitleSmallLineHeight,
            letterSpacing = AppTypeScaleTokens.TitleSmallTracking,
        )
}
