package com.nbpt.app.ui.screen.guidepermissions

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.nbpt.app.R
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.ui.screen.destinations.GuideIgnoreBatteryOptimizationDestination
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import org.koin.compose.koinInject

@Destination
@Composable
fun GuideNotification(
    navigator: DestinationsNavigator
) {
    val context = LocalContext.current
    val notificationPermissionRequester: NotificationPermissionRequester = koinInject()

    val onSkip = {
        navigator.popBackStack()
        navigator.navigate(GuideIgnoreBatteryOptimizationDestination)
    }

    LaunchedEffect(
        notificationPermissionRequester.hasPermission(context.findActivity()),
    ) {
        if (notificationPermissionRequester.hasPermission(context.findActivity())) {
            onSkip()
        }
    }

    GuidePermissionContent(
        icon = painterResource(id = R.drawable.ic_permission_notification),
        title = stringResource(R.string.text_accept_daily_reminder_from_the_app),
        description = "Allow Wellness Mate to send you notifications",
        onNext = {
            notificationPermissionRequester.tryToRequestIfNeeded(context.findActivity())
        },
        onSkip = onSkip
    )
}