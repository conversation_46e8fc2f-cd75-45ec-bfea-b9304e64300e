package com.nbpt.app.ui.screen.article

import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.nbpt.app.R
import com.nbpt.app.biz.admanager.banner.BannerAd
import com.nbpt.app.biz.admanager.banner.BannerAdPlace
import com.nbpt.app.biz.admanager.interstitial.AdmobInterstitialAdViewModel
import com.nbpt.app.biz.admanager.interstitial.MaxInterstitialAdManager
import com.nbpt.app.biz.admanager.interstitial.RegisterHandleSideEffect
import com.nbpt.app.biz.admanager.nat1ve.NativeAd
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
//import com.nbpt.app.biz.admanager.banner.BannerAd
//import com.nbpt.app.biz.admanager.banner.BannerAdPlace
//import com.nbpt.app.biz.admanager.nat1ve.NativeAd
//import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlace
import com.nbpt.app.biz.alarmguide.AddRemindAlarmGuideManager
import com.nbpt.app.biz.remoteconfig.useLegacyAd
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.data.healtharticles.HealthArticle
import com.nbpt.app.ui.common.AdLoadingDialog
import com.nbpt.app.ui.common.BlankSpacer
import com.nbpt.app.ui.theme.Layout
import com.nbpt.app.ui.theme.RoundedCornerShape12Dp
import com.nbpt.app.ui.theme.defShadow
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect

data class ArticleNavArgs(
    val article: HealthArticle,
    val needTryToDoAlarmGuideWhenBack: Boolean = false,
)

@Destination(
    navArgsDelegate = ArticleNavArgs::class,
)
@Composable
fun Article(
    args: ArticleNavArgs,
    navigator: DestinationsNavigator
) {
    val viewModel: ArticleViewModel = koinViewModel()
    val viewState by viewModel.collectAsState()

    val interstitialAdManager: MaxInterstitialAdManager = koinInject()
    val alarmGuideManager: AddRemindAlarmGuideManager = koinInject()

    val admobInterstitialAdViewModel: AdmobInterstitialAdViewModel = koinViewModel()
    val admobInterstitialAdViewState by admobInterstitialAdViewModel.collectAsState()

    LaunchedEffect(Unit) {
        admobInterstitialAdViewModel.registerInterAdEventFlow(this)
    }

    admobInterstitialAdViewModel.RegisterHandleSideEffect(
        navigator = navigator
    )

    if (admobInterstitialAdViewState.adLoading) {
        AdLoadingDialog()
    }

    val context = LocalContext.current

    val adAfterAction: DestinationsNavigator.() -> Unit = {
        if (args.needTryToDoAlarmGuideWhenBack) {
            navigateUp()
            GlobalScope.launch {
                alarmGuideManager.tryToOpenGuideDialog().apply {
                    debugLog { "tryToOpenGuideDialog = $this" }
                }
            }
        } else {
            navigateUp()
        }
    }

    val onBack = {
        if (useLegacyAd) {
            interstitialAdManager.tryToShowAd("exit_article", onAdLoadingAfter = {
                adAfterAction(navigator)
            })
        } else {
            admobInterstitialAdViewModel.onTryToShowInterAdAndNavAction(
                activity = context.findActivity(),
                navAction = { adAfterAction(this) },
                adPlaceName = "exit_article"
            )
        }

        Unit
    }

    BackHandler(onBack = onBack)

    viewModel.collectSideEffect {
        when (it) {
            ArticleSideEffect.NavUp -> onBack()
        }
    }

    Scaffold(
        bottomBar = {
            ArticleBottomBar(
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
            )
        },
        topBar = {
            ArticleAppBar(navUp = onBack)
        },
        containerColor = Color.White.copy(.5f)
    ) {
        Column(
            modifier = Modifier
                .padding(it)
                .padding(horizontal = Layout.bodyMargin)
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            BlankSpacer(height = Layout.bodyMargin)

            ArticleTitleItem(
                title = viewState.article?.title?.trim() ?: "",
                imageResId = viewState.article?.imgRes,
                modifier = Modifier.defShadow()
            )

            BlankSpacer(height = 16.dp)

            NativeAd(place = NativeAdPlaceholder.Article)

            BlankSpacer(height = Layout.bodyMargin)

            ArticleTextContent(content = viewState.article?.content ?: "")

            BlankSpacer(height = Layout.bodyMargin)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ArticleAppBar(
    navUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = { Text(text = stringResource(R.string.text_info_knowledge)) },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = navUp) {
                Icon(
                    imageVector = Icons.Rounded.ArrowBackIosNew,
                    contentDescription = "back"
                )
            }
        },
    )
}

@Composable
private fun ArticleBottomBar(
    modifier: Modifier = Modifier,
) {
    BannerAd(placeholder = BannerAdPlace.ARTICLE, modifier = modifier)
}

@Composable
private fun ArticleTitleItem(
    title: String,
    modifier: Modifier = Modifier,
    @DrawableRes imageResId: Int? = null,
) {
    Surface(
        modifier = modifier,
        color = Color(0xFFEEE4FF),
        shape = RoundedCornerShape12Dp,
    ) {
        Row(
            Modifier.padding(horizontal = 20.dp, vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            imageResId?.let {
                Image(
                    painter = painterResource(imageResId),
                    contentDescription = null,
                    modifier = Modifier.size(64.dp)
                )
            }

            BlankSpacer(width = 6.dp)

            Text(
                text = title,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 10.dp),
                style = MaterialTheme.typography.titleLarge,
            )
        }
    }
}

@Composable
private fun ArticleTextContentPartItem(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.run { typography.bodyLarge.copy(lineHeight = 21.sp) },
        modifier = modifier
    )
}

@Composable
private fun ArticleTextContent(
    content: String,
    modifier: Modifier = Modifier
) {
    val parts = content.split("\n")

    Column(modifier) {
        parts.forEachIndexed { index, text ->
            ArticleTextContentPartItem(text)

            BlankSpacer(height = 22.dp)
        }
    }
}
