package com.nbpt.app

import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph
import androidx.navigation.NavHostController
import com.nbpt.app.ui.screen.NavGraphs
import com.nbpt.app.ui.screen.destinations.SplashDestination
import com.ramcosta.composedestinations.DestinationsNavHost
import com.ramcosta.composedestinations.animations.defaults.RootNavGraphDefaultAnimations
import com.ramcosta.composedestinations.animations.rememberAnimatedNavHostEngine

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun AppNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
) {
    val navHostEngine = rememberAnimatedNavHostEngine(
        rootDefaultAnimations = RootNavGraphDefaultAnimations(
            enterTransition = { defaultEnterTransition() },
            exitTransition = { defaultExitTransition() },
            popEnterTransition = { defaultPopEnterTransition() },
            popExitTransition = { defaultPopExitTransition() },
        )
    )

//    val userBehaviorDataStore: UserBehaviorDataStore = koinInject()
//
//    val (isSkipGuide, isSkipLanguage) = runBlocking {
//        userBehaviorDataStore.getGuideFinish() to userBehaviorDataStore.getLanguageFinish()
//    }
//
//    val startRoute = if (isSkipLanguage && !isSkipGuide) {
//        GuideDestination
//    } else if (isSkipGuide) {
//        SplashDestination
//    } else {
////        LanguageDestination
//        SplashDestination
//    }

    DestinationsNavHost(
        navGraph = NavGraphs.root,
        startRoute = SplashDestination,
        engine = navHostEngine,
        navController = navController,
        modifier = modifier
    )
}

@ExperimentalAnimationApi
private fun defaultEnterTransition(): EnterTransition {
    return slideInHorizontally { fullWidth -> fullWidth }
}

@ExperimentalAnimationApi
private fun defaultExitTransition(): ExitTransition {
    return slideOutHorizontally { fullWidth -> -fullWidth / 4 }
}

private val NavDestination.hostNavGraph: NavGraph
    get() = hierarchy.first { it is NavGraph } as NavGraph

@ExperimentalAnimationApi
private fun defaultPopEnterTransition(): EnterTransition {
    return slideInHorizontally { fullWidth -> -fullWidth / 4 }
}

@ExperimentalAnimationApi
private fun defaultPopExitTransition(): ExitTransition {
    return slideOutHorizontally { fullWidth -> fullWidth }
}
