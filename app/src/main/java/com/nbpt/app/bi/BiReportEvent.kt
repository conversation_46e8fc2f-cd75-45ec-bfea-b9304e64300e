package com.nbpt.app.bi

import com.nbpt.app.BuildConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

suspend fun String.encryptForReportJson(sk: String = BuildConfig.SK) =
    withContext(Dispatchers.Default) {
        buildString {
            append("{")
            append("\"json_data\":")
            append("\"")
            append(AesCipher.encrypt(sk, this@encryptForReportJson).data ?: "")
            append("\"")
            append("}")
        }
    }
