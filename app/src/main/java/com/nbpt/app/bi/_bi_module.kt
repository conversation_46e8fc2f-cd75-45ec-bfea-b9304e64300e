package com.nbpt.app.bi

import com.nbpt.app.BuildConfig
import com.nbpt.app.data.db.BpDB
import de.jensklingenberg.ktorfit.Ktorfit
import de.jensklingenberg.ktorfit.converter.ResponseConverterFactory
import io.ktor.client.engine.okhttp.OkHttp
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import java.security.SecureRandom
import javax.net.ssl.SSLContext


val biModule = module {
    single {
        Ktorfit.Builder().baseUrl(BuildConfig.API_BASE_URL).httpClient(OkHttp) {
            engine {
                config {
                    val trustAllCert = AllCertsTrustManager()
                    val sslContext = SSLContext.getInstance("SSL")
                    sslContext.init(null, arrayOf(trustAllCert), SecureRandom())
                    sslSocketFactory(sslContext.socketFactory, trustAllCert)
                    addInterceptor(BiInterceptor)
                }
            }
        }.converterFactories(ResponseConverterFactory()).build()
    }
    single { get<Ktorfit>().createBiApi() }
    singleOf(::BiReporter)
    singleOf(::DeviceInfoReportReader)
    single { get<BpDB>().biReporterPendingReportEventDao() }
    singleOf(::BiApiRemoteConfig)
}
