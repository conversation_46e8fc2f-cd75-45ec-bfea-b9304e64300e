package com.nbpt.app.bi

import android.content.Context
import android.os.Parcelable
import com.nbpt.app.androidcomponent.repeatnoti.ArticleRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemindRepeatNoti
import com.nbpt.app.androidcomponent.repeatnoti.RemoteConfigMessageRepeatNotification
import com.nbpt.app.biz.remoteconfig.AdConfig
import com.nbpt.app.biz.remoteconfig.AddRemindAlarmGuideConfig
import com.nbpt.app.biz.remoteconfig.AdPlaceControl
import com.nbpt.app.biz.remoteconfig.AdaptiveRectAdConfig
import com.nbpt.app.biz.remoteconfig.RemoteConfigKey
import com.nbpt.app.biz.remoteconfig.RepeatNotiGroup
import com.nbpt.app.biz.remoteconfig.RepeatNotiPushStrategy
import com.nbpt.app.biz.remoteconfig.SmartRectAdConfig
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.mmkv.mmkvWithId
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import com.nbpt.app.configureMaxAdIfNeeded
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Parcelize
class WConfigMap(
    val configMap: Map<String, String>
) : Parcelable

class BiApiRemoteConfig(
    private val context: Context,
    private val biApi: BiApi,
    private val biReporter: BiReporter,
) {
    companion object {
        const val TAG = "BiApiRemoteConfig"

        private const val W_CONFIG_MAP_KEY = "WConfigMap_Key"
    }

    val mmkv = mmkvWithId(TAG)

    val configMap: Map<String, String>
        get() = mmkv.decodeParcelable(W_CONFIG_MAP_KEY, WConfigMap::class.java, null)
            ?.configMap
            ?: emptyMap()

    private fun updateConfigMap(configMap: Map<String, String>) {
        mmkv.encode(W_CONFIG_MAP_KEY, WConfigMap(configMap))
        debugLog(tag = TAG) { "updateConfigMap: $configMap" }
    }

    fun init(
        onUnstableUpdateConfig: (() -> Unit)? = null,
    ) {
        debugLog(tag = TAG) { "init()" }
        GlobalScope.launch {
            if (configMap.isEmpty()) {
                repeat(50) { currentTimes ->
                    fetchConfig()
                    onUnstableUpdateConfig?.invoke()

                    when (currentTimes) {
                        in 0..12 -> {
                            delay(10.toDuration(DurationUnit.SECONDS))
                        }

                        in 13..17 -> {
                            val input = currentTimes

                            // 计算系数：(n-12)^2 - n + 13 - [if n=16 then 1 else 0]
                            val adjustment = if (input == 16) 1 else 0
                            val coefficient = (input - 12) * (input - 12) - input + 13 - adjustment
                            val result = coefficient * 5

                            delay(result.toDuration(DurationUnit.MINUTES))
                        }

                        else -> {
                            delay(60.toDuration(DurationUnit.MINUTES))
                        }
                    }
                }
            } else {
                while (true) {
                    fetchConfig()
                    delay(60.toDuration(DurationUnit.MINUTES))
                }
            }
        }
    }

    private suspend fun fetchConfig() {
        debugLog(tag = TAG) { "fetchConfig()" }

        var header = biReporter.headerMap()

        if (header == null) {
            run {
                repeat(7) {
                    delay(100L * (it + 1))

                    debugLog(tag = TAG) { "repeat getHeader $it" }
                    header = biReporter.headerMap()
                    if (header != null) {
                        debugLog(tag = TAG) { "repeat getHeader non null" }
                        return@run
                    }
                }
            }
        }

        if (header != null) {
            val encryptedConfigMapJson = try {
                biApi.config(
                    headerMap = header!!,
                    body = BiApiConfigRequestBody().toJsonString()!!.encryptForReportJson()
                )
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
            debugLog(tag = TAG) {
                "get encryptedConfigMapJson: ${encryptedConfigMapJson.isNullOrEmpty().not()}"
            }

            if (!encryptedConfigMapJson.isNullOrEmpty()) {
                debugLog(tag = TAG) { "encryptedConfigMapJson: $encryptedConfigMapJson" }

                val decryptedConfigMap = try {
                    encryptedConfigMapJson.toObj<Map<String, String>>()?.mapValues {
                        it.value.trim().decryptCBC()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    null
                } ?: return

                debugLog(tag = TAG) { "decryptedConfigMap: $decryptedConfigMap" }

                updateConfigMap(decryptedConfigMap)

                GlobalScope.launch {
                    configureMaxAdIfNeeded(context)
                }
            }
        }
    }


    fun useLegacyAd(): Boolean? {
        val key = RemoteConfigKey.USE_LEGACY_AD
        return configMap[key]?.toBooleanStrictOrNull()
    }

    fun adConfig1(): AdConfig? {
        val key = RemoteConfigKey.AD_CONFIG_1
        return configMap[key]?.toObj()
    }

    fun adConfig2(): AdConfig? {
        val key = RemoteConfigKey.AD_CONFIG_2
        return configMap[key]?.toObj()
    }

    fun repeatNotiPushStrategy(
        repeatNotification: RemoteConfigMessageRepeatNotification
    ): RepeatNotiPushStrategy? {
        val (key, defaultStrategy) = when (repeatNotification) {
            is RemindRepeatNoti -> {
                RemoteConfigKey.REMIND_REPEAT_NOTI_PUSH_STRATEGY to null
            }

            is ArticleRepeatNoti -> {
                RemoteConfigKey.ARTICLE_REPEAT_NOTI_PUSH_STRATEGY to null
            }
        }

        debugLog(tag = TAG) { "repeatNoti: $repeatNotification, key: $key" }
        return configMap[key]?.toObj() ?: defaultStrategy
    }

    fun remindNotiGroup(): RepeatNotiGroup? {
        val key = RemoteConfigKey.REMIND_NOTI_GROUP
        return configMap[key]?.toObj()
    }

    fun articleNotiGroup(): RepeatNotiGroup? {
        val key = RemoteConfigKey.ARTICLE_NOTI_GROUP
        return configMap[key]?.toObj()
    }

    fun addRemindAlarmGuideConfig(): AddRemindAlarmGuideConfig? {
        val key = RemoteConfigKey.ADD_REMIND_ALARM_GUIDE_CONFIG
        return configMap[key]?.toObj()
    }

    fun dailyNotiTimes(): Int? {
        val key = RemoteConfigKey.DAILY_NOTI_TIMES
        return configMap[key]?.toIntOrNull()
    }

    fun adPlaceControl(): AdPlaceControl? {
        val key = RemoteConfigKey.AD_PLACE_CONTROL
        return configMap[key]?.toObj()
    }

    fun adaptiveRectAdConfig(): AdaptiveRectAdConfig? {
        val key = RemoteConfigKey.ADAPTIVE_RECTANGLE_AD_CONFIG
        return configMap[key]?.toObj()
    }

    fun smartRectAdConfig(): SmartRectAdConfig? {
        val key = RemoteConfigKey.SMART_RECTANGLE_AD_CONFIG
        return configMap[key]?.toObj()
    }

    fun useNoveltyRectAdLayout(): Boolean? {
        val key = RemoteConfigKey.USE_NOVELTY_RECT_AD_LAYOUT

        return configMap[key]?.toBooleanStrictOrNull()
    }
}