package com.nbpt.app.bi

import de.jensklingenberg.ktorfit.Response
import de.jensklingenberg.ktorfit.http.Body
import de.jensklingenberg.ktorfit.http.HeaderMap
import de.jensklingenberg.ktorfit.http.POST
import de.jensklingenberg.ktorfit.http.Path

interface BiApi {

    @POST("api/{path}")
    suspend fun report(
        @Path("path") path: String,
        @HeaderMap headerMap: Map<String, String>,
        @Body body: String
    ): Response<Unit>

    @POST("api/config")
    suspend fun config(
        @HeaderMap headerMap: Map<String, String>,
        @Body body: String
    ): String

}
