package com.nbpt.app.bi

import org.apache.commons.codec.binary.Base64
import java.nio.ByteBuffer
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * AesCipher
 *
 * Encode/Decode text by password using AES-128-CBC algorithm
 */
class AesCipher {
    /**
     * Get encoded/decoded data
     */
    /**
     * Encoded/Decoded data
     */
    var data: String? = null
        protected set
    /**
     * Get initialization vector value
     */
    /**
     * Initialization vector value
     */
    var initVector: String? = null
        protected set
    /**
     * Get error message
     */
    /**
     * Error message if operation failed
     */
    var errorMessage: String? = null
        protected set

    private constructor() : super()

    /**
     * AesCipher constructor.
     *
     * @param initVector   Initialization vector value
     * @param data         Encoded/Decoded data
     * @param errorMessage Error message if operation failed
     */
    private constructor(initVector: String?, data: String?, errorMessage: String?) : super() {
        this.initVector = initVector
        this.data = data
        this.errorMessage = errorMessage
    }

    /**
     * Check that operation failed
     *
     * @return TRUE if failed, FALSE otherwise
     */
    fun hasError(): Boolean {
        return errorMessage != null
    }

    /**
     * To string return resulting data
     *
     * @return Encoded/decoded data
     */
    override fun toString(): String {
        return data!!
    }

    companion object {
        const val INIT_VECTOR_LENGTH = 16

        /**
         * @see [how-to-convert-a-byte-array-to-a-hex-string](https://stackoverflow.com/questions/9655181/how-to-convert-a-byte-array-to-a-hex-string-in-java)
         */
        private val hexArray = "0123456789ABCDEF".toCharArray()

        /**
         * Encrypt input text by AES-128-CBC algorithm
         *
         * @param secretKey 16/24/32 -characters secret password
         * @param plainText Text for encryption
         * @return Encoded string or NULL if error
         */
        fun encrypt(secretKey: String, plainText: String): AesCipher {
            var initVector: String? = null
            return try {
                // Check secret length
                if (!isKeyLengthValid(secretKey)) {
                    throw Exception("Secret key's length must be 128, 192 or 256 bits")
                }

                // Get random initialization vector
                val secureRandom = SecureRandom()
                var initVectorBytes = ByteArray(INIT_VECTOR_LENGTH / 2)
                secureRandom.nextBytes(initVectorBytes)
                initVector = bytesToHex(initVectorBytes)
                initVectorBytes = initVector.toByteArray(charset("UTF-8"))
                val ivParameterSpec = IvParameterSpec(initVectorBytes)
                val secretKeySpec = SecretKeySpec(secretKey.toByteArray(charset("UTF-8")), "AES")
                val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
                cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec)

                // Encrypt input text
                val encrypted = cipher.doFinal(plainText.toByteArray(charset("UTF-8")))
                val byteBuffer = ByteBuffer.allocate(initVectorBytes.size + encrypted.size)
                byteBuffer.put(initVectorBytes)
                byteBuffer.put(encrypted)

                // Result is base64-encoded string: initVector + encrypted result
                val result = String(Base64.encodeBase64(byteBuffer.array()))

                // Return successful encoded object
                AesCipher(initVector, result, null)
            } catch (t: Throwable) {
                t.printStackTrace()
                // Operation failed
                AesCipher(initVector, null, t.message)
            }
        }

        /**
         * Decrypt encoded text by AES-128-CBC algorithm
         *
         * @param secretKey  16/24/32 -characters secret password
         * @param cipherText Encrypted text
         * @return Self object instance with data or error message
         */
        fun decrypt(secretKey: String, cipherText: String?): AesCipher {
            return try {
                // Check secret length
                if (!isKeyLengthValid(secretKey)) {
                    throw Exception("Secret key's length must be 128, 192 or 256 bits")
                }

                // Get raw encoded data
                val encrypted = Base64.decodeBase64(cipherText?.toByteArray())

                // Slice initialization vector
                val ivParameterSpec = IvParameterSpec(encrypted, 0, INIT_VECTOR_LENGTH)
                // Set secret password
                val secretKeySpec = SecretKeySpec(secretKey.toByteArray(charset("UTF-8")), "AES")
                val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec)

                // Trying to get decrypted text
                val result = String(
                    cipher.doFinal(
                        encrypted,
                        INIT_VECTOR_LENGTH,
                        encrypted.size - INIT_VECTOR_LENGTH
                    )
                )

                // Return successful decoded object
                AesCipher(bytesToHex(ivParameterSpec.iv), result, null)
            } catch (t: Throwable) {
                t.printStackTrace()
                // Operation failed
                AesCipher(null, null, t.message)
            }
        }

        /**
         * Check that secret password length is valid
         *
         * @param key 16/24/32 -characters secret password
         * @return TRUE if valid, FALSE otherwise
         */
        fun isKeyLengthValid(key: String): Boolean {
            return key.length == 16 || key.length == 24 || key.length == 32
        }

        /**
         * Convert Bytes to HEX
         *
         * @param bytes Bytes array
         * @return String with bytes values
         */
        fun bytesToHex(bytes: ByteArray): String {
            val hexChars = CharArray(bytes.size * 2)
            for (j in bytes.indices) {
                val v = bytes[j].toInt() and 0xFF
                hexChars[j * 2] = hexArray[v ushr 4]
                hexChars[j * 2 + 1] = hexArray[v and 0x0F]
            }
            return String(hexChars)
        }
    }
}
