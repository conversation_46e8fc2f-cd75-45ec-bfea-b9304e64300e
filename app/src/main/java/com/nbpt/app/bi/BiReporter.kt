package com.nbpt.app.bi

import com.nbpt.app.BuildConfig
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.moshi.toJsonString
import com.nbpt.app.common.moshi.toObj
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.nbpt.app.common.mmkv.mmkvWithId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update


private const val TAG = "BiReporter"

class BiReporter(
    private val biApi: BiApi,
    private val biReporterPendingReportEventDao: BiReporterPendingReportEventDao,
    private val deviceInfoReportReader: DeviceInfoReportReader
) {
    private val handlingPendingReport = MutableStateFlow(false)

    val mmkv = mmkvWithId(TAG)

    suspend fun handlePendingReportEvents() {
        if (handlingPendingReport.first()) return
        handlingPendingReport.update { true }

        val pendingEvents = biReporterPendingReportEventDao.fetchAllFlow().first()
        if (pendingEvents.isEmpty()) {
            handlingPendingReport.update { false }
            return
        }

        val headerMap = headerMap()
        if (headerMap == null) {
            handlingPendingReport.update { false }
            return
        }

        val reportEvents = mutableListOf<ReportCreateEvent?>()
        val reportValues = mutableListOf<ReportCreateValue?>()

        pendingEvents.forEach { event ->
            when (event.path) {
                "createEvent" -> reportEvents.add(event.reportBodyJson.toObj())
                "createValue" -> reportValues.add(event.reportBodyJson.toObj())
                else -> {}
            }
        }

        if (reportEvents.isNotEmpty()) {
            val createEventsResponse = try {
                biApi.report(
                    path = "createEvent",
                    headerMap = headerMap,
                    body = reportEvents.toList().toJsonString()?.encryptForReportJson()!!
                )
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
            if (createEventsResponse?.isSuccessful == true) {
                biReporterPendingReportEventDao.deleteByPath("createEvent")
            }
            debugLog(tag = TAG) { "createEvents response: $createEventsResponse" }
        }

        if (reportValues.isNotEmpty()) {
            val createValuesResponse = try {
                biApi.report(
                    path = "createValue",
                    headerMap = headerMap,
                    body = reportValues.toList().toJsonString()?.encryptForReportJson()!!
                )
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
            if (createValuesResponse?.isSuccessful == true) {
                biReporterPendingReportEventDao.deleteByPath("createValue")
            }
            debugLog(tag = TAG) { "createValues response: $createValuesResponse" }
        }

        handlingPendingReport.update { false }
    }

    suspend fun reportEvent(
        event: ReportCreateEvent
    ): Boolean {
        val path = "createEvent"
        val reportJson = event.toJsonString() ?: return false

        return reportOrSaveToPending(
            path = path,
            reportJson = reportJson
        )
    }

    suspend fun reportValue(
        value: ReportCreateValue
    ): Boolean {
        val path = "createValue"
        val reportJson = value.toJsonString() ?: return false

        return reportOrSaveToPending(
            path = path,
            reportJson = reportJson
        )
    }

    private suspend fun reportOrSaveToPending(
        path: String,
        reportJson: String,
        saveToPending: Boolean = true,
    ): Boolean {
        val headerMap = headerMap()

        if (headerMap == null) {
            if (saveToPending) {
                addToPending(
                    path = path,
                    reportEventJson = reportJson
                )
            }

            return false
        } else {
            val response = try {
                biApi.report(
                    path = path,
                    headerMap = headerMap,
                    body = reportJson.encryptForReportJson()
                )
            } catch (e: Exception) {
                Firebase.crashlytics.recordException(e)
                e.printStackTrace()
                null
            }

            debugLog(tag = TAG) { "response successful: ${response?.isSuccessful}" }

            if (response?.isSuccessful != true && saveToPending) {
                addToPending(
                    path = path,
                    reportEventJson = reportJson
                )
            }

            return response?.isSuccessful ?: false
        }
    }

    private suspend fun addToPending(
        path: String,
        reportEventJson: String
    ) {
        biReporterPendingReportEventDao.addPending(
            event = BiPendingReportEventEntity(
                path = path,
                reportBodyJson = reportEventJson
            )
        )
    }

    suspend fun headerMap(): Map<String, String>? {
        val reportHeadWbData = deviceInfoReportReader.reportHeadWbData() ?: return null
        val encryptedReportHeadWbDataJsonString =
            reportHeadWbData.toJsonString()?.let { AesCipher.encrypt(BuildConfig.SK, it) }?.data
                ?: return null

        return mapOf(
            "accept" to "application/json",
            "content-type" to "application/json",
            "api-version" to "1",
//            "package-name" to BuildConfig.APPLICATION_ID,
            "package-name" to "com.wydevteam.wellnessmate",
            "wb-data" to encryptedReportHeadWbDataJsonString.apply {
                debugLog(tag = TAG) { "wb-data: $this" }
            }
        )

    }

}
