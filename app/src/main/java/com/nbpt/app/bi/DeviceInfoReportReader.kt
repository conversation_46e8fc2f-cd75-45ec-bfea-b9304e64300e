package com.nbpt.app.bi

import android.content.Context
import android.provider.Settings
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.firebase.Firebase
import com.google.firebase.analytics.analytics
import com.google.firebase.crashlytics.crashlytics
import com.nbpt.app.common.mmkv.mmkvWithId
import com.tencent.mmkv.MMKV
import com.tenjin.android.params.TenjinParams
import com.tenjin.android.store.SharedPrefsStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.ZoneId
import java.time.format.TextStyle
import java.util.Locale
import java.util.UUID


private const val TAG = "DeviceInfoReportReader"

class DeviceInfoReportReader(
    private val context: Context
) {
    private val mmkv = mmkvWithId(TAG)

    suspend fun configureUserIdIfNeeded() {
        if (!userId().isNullOrEmpty()) return

        val adId = adId()
        if (!adId.isNullOrEmpty() && adIdIsValid(adId)) {
            storeUserId(adId)
            return
        }

//        val deviceId = deviceId()
//        if (!deviceId.isNullOrEmpty()) {
//            storeUserId(deviceId)
//            return
//        }

        val mmpId = mmpId()
        if (!mmpId.isNullOrEmpty()) {
            storeUserId(mmpId)
            return
        }

        val uuid = UUID.randomUUID().toString()
        storeUserId(uuid)
    }

    suspend fun reportHeadWbData(): ReportHeaderWbData? {
        val userId = userId() ?: return null
        val tenjinId = tenjinId() ?: ""
        val appInstanceId = appInstanceId() ?: ""

        return ReportHeaderWbData(
            user_id = userId,
            tenjin_id = tenjinId,
            app_instance_id = appInstanceId
        )
    }

    suspend fun adId() = withContext(Dispatchers.IO) {
        val adId = mmkv.decodeString("adId", null)

        return@withContext if (adId.isNullOrEmpty()) {
            try {
                val resultAdId = AdvertisingIdClient.getAdvertisingIdInfo(context).id
                if (resultAdId != null) {
                    mmkv.encode("adId", resultAdId)
                }

                resultAdId
            } catch (e: Exception) {
                null
            }
        } else {
            adId
        }
    }

    private fun adIdIsValid(adId: String): Boolean {
        var s = ""

        adId.forEach {
            if (it.isLetterOrDigit()) {
                s += it
            }
        }


        s.forEach {
            if (!it.isDigit()) {
                return true
            } else if (it != '0') {
                return true
            }
        }

        return false
    }

//    fun deviceId(): String? {
//        return try {
//            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
//        } catch (e: Exception) {
//            null
//        }
//    }

    fun mmpId() = tenjinId()

    fun tenjinId(): String? {
        return TenjinParams(SharedPrefsStore(context)).analyticsInstallationID ?: null
    }

   suspend fun appInstanceId(): String? {
       val key = "appInstanceId"

       val storedAppInstanceId = mmkv.decodeString(key)
       if (storedAppInstanceId != null) return storedAppInstanceId

       val fetchFirebaseInstanceId = withContext(Dispatchers.IO) {
           try {
               Firebase.analytics.firebaseInstanceId
           } catch (e: Exception) {
               Firebase.crashlytics.recordException(e)
               null
           }
       }

       if (fetchFirebaseInstanceId != null) {
           mmkv.encode(key, fetchFirebaseInstanceId)
       }

       return fetchFirebaseInstanceId
   }

    fun userId(): String? {
        return mmkv.decodeString("userId", null)
    }

    private fun storeUserId(id: String) {
        val key = "userId"
        mmkv.encode(key, id)
    }
}
