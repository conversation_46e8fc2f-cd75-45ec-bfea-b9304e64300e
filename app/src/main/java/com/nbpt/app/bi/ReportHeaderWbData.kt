@file:Suppress("PropertyName")

package com.nbpt.app.bi

import android.content.Context
import androidx.annotation.Keep
import androidx.compose.ui.text.intl.Locale
import com.nbpt.app.BuildConfig
import com.nbpt.app.androidcomponent.DeviceInfo
import com.nbpt.app.godlikeApplicationContext
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.SerialName

@Keep
@JsonClass(generateAdapter = true)
data class ReportHeaderWbData(
    val package_id: String = "com.wydevteam.wellnessmate",
    val version_name: String = BuildConfig.VERSION_NAME,
    val version_code: Int = BuildConfig.VERSION_CODE,
    val language: String = Locale.current.toLanguageTag(),
    val user_id: String = "",
    val app_instance_id: String = "",
    val tenjin_id: String = "",
    val timezone: Int = DEVICE_TIME_ZONE,

//    @Json(name = "pos_byte")
//    val adb_status: Int = godlikeApplicationContext?.let { context: Context ->
//        if (DeviceInfo.adbEnabled(context)) 1 else 0
//    } ?: 0,
)

private val DEVICE_TIME_ZONE =
    java.util.TimeZone.getDefault().getOffset(System.currentTimeMillis()) / 3600000
