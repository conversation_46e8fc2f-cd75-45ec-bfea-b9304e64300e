package com.nbpt.app.bi

import com.nbpt.app.data.datastore.UserBehaviorDataStore
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.withContext
import org.koin.java.KoinJavaComponent

@OptIn(ExperimentalCoroutinesApi::class, DelicateCoroutinesApi::class)
private val biReportDispatcher = newSingleThreadContext("biReportDispatcher")

suspend fun BiReporter.reportInstallIfNeeded() = withContext(biReportDispatcher) {
    val userBehaviorDataStore: UserBehaviorDataStore =
        KoinJavaComponent.get(UserBehaviorDataStore::class.java)

    val hasReportInstallKey = "hasReportInstall"
    val hasReportInstall = mmkv.decodeBool(
        hasReportInstallKey,
        userBehaviorDataStore.get1stTimeLaunchAppInstant().epochSeconds != 0L
    )
    if (hasReportInstall) return@withContext

    val event = ReportCreateEvent(
        event_name = "install",
        activity = "",
        type = 1
    )

    reportEvent(event)

    mmkv.encode(hasReportInstallKey, true)
}

suspend fun BiReporter.reportPageOnStartEvent(
    pageName: String
) = withContext(biReportDispatcher) {
    val event = ReportCreateEvent(
        event_name = "on_start",
        activity = pageName,
        type = 1
    )

    reportEvent(event)
}

@Suppress("LocalVariableName")
suspend fun BiReporter.reportAdOnPaid(
    value: Float,
    currency: String,
    precisionType: String,
    adNetwork: String,
    adType: String,
    adPlacement: String,
) {
    val _value = ReportCreateValue(
        value = value,
        currency = currency,
        precision_type = precisionType,
        ad_network = adNetwork,
        ad_type = adType,
        ad_placement = adPlacement
    )

    reportValue(_value)
}
