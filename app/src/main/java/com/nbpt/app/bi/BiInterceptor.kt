package com.nbpt.app.bi

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.core.os.postDelayed
import com.nbpt.app.BuildConfig
import com.nbpt.app.common.android.findActivity
import com.nbpt.app.common.logger.debugLog
import es.dmoral.toasty.Toasty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.Buffer

private const val TAG = "BiInterceptor"

internal val BiInterceptor = Interceptor { chain ->
    val request: Request = chain.request()

    val t1 = System.nanoTime()

    debugLog(tag = TAG) {
        String.format(
            "-> Sending request: %s \n-> Headers: %s \n-> Body: %s",
            request.url, request.headers.toString().trim(), request.body?.bodyString()?.run {
                substring(14, this.length - 1).let {
                    AesCipher.decrypt(BuildConfig.SK, it)
                }
            }
        ).also {
//            GlobalScope.launch(Dispatchers.Main) {
//                val context = GlobalContext.get().get<Context>(Context::class)
//                Toasty.info(context, it, Toast.LENGTH_LONG, false).show()
//            }
        }
    }
    debugLog(tag = TAG) {
        String.format(
            "-> wb-data: %s",
            request.headers["wb-data"]?.let {
                AesCipher.decrypt(BuildConfig.SK, it).data
            }
        )
    }

    val response: Response = chain.proceed(request)

    val t2 = System.nanoTime()
    debugLog(tag = TAG) {
        String.format(
            "<- Received response (code=%s) for %s in %.1fms%n<- Body: %s",
            response.code,
            response.request.url,
            (t2 - t1) / 1e6,
            request.body?.bodyString()?.run {
                substring(14, this.length - 1).let {
                    AesCipher.decrypt(BuildConfig.SK, it)
                }
            }
        ).also {
//            GlobalScope.launch(Dispatchers.Main) {
//                val context = GlobalContext.get().get<Context>(Context::class)
//                Toasty.info(context, it.trim(), Toast.LENGTH_LONG, false).show()
//            }
        }
    }

    response
}

fun RequestBody.bodyString(): String {
    val buffer = Buffer()
    this.writeTo(buffer)
    return buffer.readUtf8()
}
