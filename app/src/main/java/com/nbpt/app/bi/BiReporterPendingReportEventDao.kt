package com.nbpt.app.bi

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

@Dao
interface BiReporterPendingReportEventDao {
    @Query("SELECT * FROM bi_pending_report_events")
    fun fetchAllFlow(): Flow<List<BiPendingReportEventEntity>>

    @Query("DELETE FROM bi_pending_report_events WHERE id=:id")
    suspend fun deleteById(id: Long): Int

    @Query("DELETE FROM bi_pending_report_events WHERE path=:path")
    suspend fun deleteByPath(path: String): Int

    @Insert
    suspend fun addPending(event: BiPendingReportEventEntity): Long
}
