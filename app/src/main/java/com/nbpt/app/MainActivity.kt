package com.nbpt.app

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.graphics.Color
import androidx.core.view.WindowCompat
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.findViewTreeViewModelStoreOwner
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.findViewTreeSavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.nbpt.app.androidcomponent.IgnoringBatteryOptimizationRequester
import com.nbpt.app.androidcomponent.NotificationPermissionRequester
import com.nbpt.app.androidcomponent.fixednoti.FixedNotiService
import com.nbpt.app.androidcomponent.fixednoti.NotiClickManager
import com.nbpt.app.androidcomponent.fixednoti.FixedNotificationHelper
import com.nbpt.app.androidcomponent.repeatnoti.HandsUpNotiManager
import com.nbpt.app.androidcomponent.simplefcmnoti.SimpleFirebaseMessagingNotification
import com.nbpt.app.bi.BiReporter
import com.nbpt.app.bi.DeviceInfoReportReader
import com.nbpt.app.biz.analytics.tenjin.TenjinHelper
import com.nbpt.app.bi.reportInstallIfNeeded
import com.nbpt.app.biz.admanager.banner.AdmobBannerAdManager
import com.nbpt.app.biz.admanager.nat1ve.NativeAdPlaceholder
import com.nbpt.app.biz.analytics.AnalyticsLogEvent
import com.nbpt.app.biz.analytics.logEventRecord
import com.nbpt.app.common.android.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS_SETTINGS
import com.nbpt.app.common.android.REQUEST_POST_NOTIFICATION_SETTINGS
import com.nbpt.app.common.logger.debugLog
import com.nbpt.app.common.send
import com.nbpt.app.ui.theme.AppMd3Theme
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject

var mainActivity: MainActivity? = null

class MainActivity : AppCompatActivity() {

    private val maxBannerAdManager: MaxBannerAdHelper by inject()
    private val admobBannerAdManager: AdmobBannerAdManager by inject()
    private val maxNativeAdManager: MaxNativeAdManager by inject()
    private val tenjinHelper: TenjinHelper by inject()
    private val fixedNotificationHelper: FixedNotificationHelper by inject()
    private val handsUpNotiManager: HandsUpNotiManager by inject()
    private val simpleFirebaseMessagingNotification: SimpleFirebaseMessagingNotification by inject()
    private val deviceInfoReportReader: DeviceInfoReportReader by inject()
    private val biReporter: BiReporter by inject()
    private val notificationPermissionRequester: NotificationPermissionRequester by inject()
    private val notiClickManager: NotiClickManager by inject()

    var isInForeground: Boolean? = null

    init {
        notificationPermissionRequester.registerPermissionResult(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        debugLog(tag = "mainActivity") { "onCreate(savedInstanceState: Bundle?)" }
        mainActivity = this
        super.onCreate(savedInstanceState)
        intent?.let { fixedNotificationHelper.handleClickEvent(it) }

        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            AppContent()
        }

        // Copied from setContent {} ext-fun
        setOwners()

        AnalyticsLogEvent.configureActivity(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_IGNORE_BATTERY_OPTIMIZATIONS_SETTINGS) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager?
                val isIgnoringBatteryOptimizations =
                    powerManager?.isIgnoringBatteryOptimizations(packageName) == true

                IgnoringBatteryOptimizationRequester.ignoringEventFlow.send(
                    isIgnoringBatteryOptimizations
                )

                if (isIgnoringBatteryOptimizations) {
                    logEventRecord("battery_permission_request_success")
                    debugLog { "battery_permission_request_success" }
                } else {
                    logEventRecord("battery_permission_request_fail")
                    debugLog { "battery_permission_request_fail" }
                }
            }
        } else if (requestCode == REQUEST_POST_NOTIFICATION_SETTINGS) {
            val hasPermission = notificationPermissionRequester.hasPermission(this)
            if (hasPermission) {
                configureFirebaseMessagingTopicIfNeeded(this)
                logEventRecord("notification_permission_request_success")
                debugLog { "notification_permission_request_success" }
            } else {
                logEventRecord("notification_permission_request_fail")
                debugLog { "notification_permission_request_fail" }
            }
        }
    }

    @Composable
    private fun AppContent() {
        val systemUiController = rememberSystemUiController()

//        val useDarkTheme = isSystemInDarkTheme()
        val useDarkTheme = false
        val useDarkIcons = !useDarkTheme
        DisposableEffect(systemUiController) {
            systemUiController.setSystemBarsColor(
//                color = if (useDarkIcons) md_theme_light_surface else md_theme_dark_surface,
//                darkIcons = useDarkIcons
                color = Color.Transparent,
                darkIcons = useDarkIcons
            )

            onDispose {}
        }

        AppMd3Theme(useDarkTheme = useDarkTheme) {
            Host()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        intent.let { fixedNotificationHelper.handleClickEvent(it) }
        if (isInForeground == true) {
//            bannerAdManager.destroy(BannerAdPlaceholder.HOME)
//            debugLog { "banner_ad destroy HOME" }
            maxNativeAdManager.destroy(NativeAdPlaceholder.Home)
            maxNativeAdManager.destroy(NativeAdPlaceholder.Info)
        }
        handsUpNotiManager.handleNotiIntent(
            intent,
            isActivityInForeground = isInForeground == true
        )
        NotificationActionNavigator.handleIntentAction(
            intent = intent,
            isActivityInForeground = isInForeground == true
        )
        simpleFirebaseMessagingNotification.handleIntentAction(
            intent = intent,
            isActivityInForeground = isInForeground == true
        )
        notiClickManager.handleAction(
            intent = intent,
            isActivityInForeground = isInForeground == true
        )
    }

    override fun onStart() {
        super.onStart()
        tenjinHelper.init(this)
        GlobalScope.launch {
            launch { biReporter.reportInstallIfNeeded() }
        }
        GlobalScope.launch {
            deviceInfoReportReader.configureUserIdIfNeeded()
        }
    }

    override fun onStop() {
        isInForeground = false
        super.onStop()
    }

    override fun onResume() {
        isInForeground = true
        super.onResume()
        FixedNotiService.startServiceIfNeeded(this.applicationContext)
    }

    override fun onDestroy() {
        maxBannerAdManager.destroyAll()
        admobBannerAdManager.destroyAll()
        super.onDestroy()
    }

    override fun attachBaseContext(newBase: Context?) {
        debugLog(tag = "mainActivity") { "attachBaseContext(newBase: Context?)" }
        super.attachBaseContext(newBase)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        windowFocusChangedFlow.update { hasFocus }
        super.onWindowFocusChanged(hasFocus)
        debugLog(tag = "MainActivity") { "onWindowFocusChanged hasFocus: $hasFocus" }
    }

    companion object {
        val windowFocusChangedFlow = MutableStateFlow<Boolean?>(null)
    }

}

private fun ComponentActivity.setOwners() {
    val decorView = window.decorView
    if (decorView.findViewTreeLifecycleOwner() == null) {
        decorView.setViewTreeLifecycleOwner(this)
    }
    if (decorView.findViewTreeViewModelStoreOwner() == null) {
        decorView.setViewTreeViewModelStoreOwner(this)
    }
    if (decorView.findViewTreeSavedStateRegistryOwner() == null) {
        decorView.setViewTreeSavedStateRegistryOwner(this)
    }
}
