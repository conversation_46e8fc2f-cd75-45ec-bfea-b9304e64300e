<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.REORDER_TASKS"
        tools:node="remove" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />-->
<!--    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />-->
<!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />-->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

<!--    <uses-permission android:name="android.permission.WAKE_LOCK" />-->
<!--    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />-->

<!--    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />-->
<!--    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />-->
<!--    <uses-permission android:name="android.permission.GET_TASKS" />-->
<!--    <uses-permission android:name="android.permission.REORDER_TASKS" />-->

    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@drawable/launcher_icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@drawable/launcher_icon"
        android:supportsRtl="true"
        android:theme="@style/Theme.BpApp"
        tools:targetApi="31">

        <service
            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
            android:enabled="false"
            android:exported="false">
            <meta-data
                android:name="autoStoreLocales"
                android:value="true" />
        </service>

        <service
            android:name=".androidcomponent.fixednoti.FixedNotiService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse"
            android:persistent="true"
            android:stopWithTask="false" >
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="本应用需要 FOREGROUND_SERVICE_SPECIAL_USE 权限来提供健康监测服务，在通知栏持续展示和同步用户的心率、血压和血糖等重要生命体征数据，并提供快捷按钮直接进入相应的数据输入页面。作为前台服务运行确保用户能实时查看健康数据并方便地记录新数据，尤其对需要持续监测的慢性病患者非常有益。我们的应用专注于展示用户输入的健康测量结果，不会访问摄像头、麦克风或位置等敏感数据。这种特殊的前台服务通过提供关键健康指标的即时访问和简化的数据记录功能，有效提升用户的健康管理体验。"/>
        </service>

        <receiver
            android:name=".androidcomponent.TReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".MainActivity"
            android:configChanges="colorMode|density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:resizeableActivity="true"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name=".androidcomponent.simplefcmnoti.SimpleFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_noti_heart" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/white" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="com.nbpt.app.AppInitializer"
                android:value="androidx.startup" />
        </provider>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~**********" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />

    </application>

</manifest>
