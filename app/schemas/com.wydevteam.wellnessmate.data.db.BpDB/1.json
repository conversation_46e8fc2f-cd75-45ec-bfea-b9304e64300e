{"formatVersion": 1, "database": {"version": 1, "identityHash": "85806b40f3ad103344b69aa9452d0b2b", "entities": [{"tableName": "bp_records", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `uuid` TEXT NOT NULL, `instant` INTEGER NOT NULL, `systolic` INTEGER NOT NULL, `diastolic` INTEGER NOT NULL, `pulse` INTEGER NOT NULL, `notes` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "instant", "columnName": "instant", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "systolic", "columnName": "systolic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "diastolic", "columnName": "diastolic", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pulse", "columnName": "pulse", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_bp_records_uuid", "unique": true, "columnNames": ["uuid"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_bp_records_uuid` ON `${TABLE_NAME}` (`uuid`)"}], "foreignKeys": []}, {"tableName": "bp_records_notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `rank` REAL NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rank", "columnName": "rank", "affinity": "REAL", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_bp_records_notes_content", "unique": true, "columnNames": ["content"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_bp_records_notes_content` ON `${TABLE_NAME}` (`content`)"}], "foreignKeys": []}, {"tableName": "hr_records", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `uuid` TEXT NOT NULL, `instant` INTEGER NOT NULL, `gender` INTEGER NOT NULL, `age` INTEGER NOT NULL, `heartRateBpm` INTEGER NOT NULL, `notes` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "instant", "columnName": "instant", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "age", "columnName": "age", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "heartRateBpm", "columnName": "heartRateBpm", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_hr_records_uuid", "unique": true, "columnNames": ["uuid"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_hr_records_uuid` ON `${TABLE_NAME}` (`uuid`)"}], "foreignKeys": []}, {"tableName": "hr_records_notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `rank` REAL NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rank", "columnName": "rank", "affinity": "REAL", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_hr_records_notes_content", "unique": true, "columnNames": ["content"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_hr_records_notes_content` ON `${TABLE_NAME}` (`content`)"}], "foreignKeys": []}, {"tableName": "bs_records", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `uuid` TEXT NOT NULL, `instant` INTEGER NOT NULL, `mmolL` REAL NOT NULL, `state` INTEGER NOT NULL, `notes` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "instant", "columnName": "instant", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mmolL", "columnName": "mmolL", "affinity": "REAL", "notNull": true}, {"fieldPath": "state", "columnName": "state", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_bs_records_uuid", "unique": true, "columnNames": ["uuid"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_bs_records_uuid` ON `${TABLE_NAME}` (`uuid`)"}], "foreignKeys": []}, {"tableName": "bs_records_notes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `rank` REAL NOT NULL, `content` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "rank", "columnName": "rank", "affinity": "REAL", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_bs_records_notes_content", "unique": true, "columnNames": ["content"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_bs_records_notes_content` ON `${TABLE_NAME}` (`content`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '85806b40f3ad103344b69aa9452d0b2b')"]}}