plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id("org.jetbrains.kotlin.plugin.compose")
    id 'com.google.devtools.ksp'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id "de.jensklingenberg.ktorfit" version "2.5.1"
}

apply from: "${rootProject.projectDir}/conf4build/copyProject.gradle"
//apply from: "${rootProject.projectDir}/conf4build/copyAabWhenBundleFinish.gradle"

def confProp = new Properties()
confProp.load((new FileInputStream(file("${rootProject.projectDir}/conf4build/buildConf.properties"))))

android {
    namespace 'com.nbpt.app'
    compileSdk 35

    defaultConfig {
        applicationId "com.nbpt.app"
        minSdk 21
        targetSdk 34
        versionCode confProp.getProperty("versionCode").toInteger()
        versionName confProp.getProperty("versionName")

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }

        // -----------------------------------------------------------------------------------------
        buildConfigField("String", "feedbackEmail", "\"${confProp.getProperty("feedbackEmail")}\"")
        buildConfigField("String", "privacyPolicyUrl", "\"${confProp.getProperty("privacyPolicyUrl")}\"")
        buildConfigField("String", "TENJIN_SDK_KEY", "\"${confProp.getProperty("TENJIN_SDK_KEY")}\"")
        buildConfigField("String", "API_BASE_URL", "\"${confProp.getProperty("API_BASE_URL")}\"")
        buildConfigField("String", "SK", "\"${confProp.getProperty("SK")}\"")
    }

    buildTypes {
        internal {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlin {
        jvmToolchain 17
    }
    ksp {
        arg('room.schemaLocation', "$projectDir/schemas")
    }
    buildFeatures {
        viewBinding true
        compose true
        buildConfig true
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
        resources.merges += "META-INF/LICENSE.md"
        resources.merges += "META-INF/LICENSE-notice.md"
    }
}

dependencies {
    coreLibraryDesugaring "com.android.tools:desugar_jdk_libs:2.1.5" // desugar j11 for java.time.*

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.activity:activity-compose:1.10.1'
    implementation 'androidx.startup:startup-runtime:1.2.0'
    implementation "androidx.datastore:datastore-preferences:1.1.7"
    implementation "androidx.collection:collection-ktx:1.5.0"
    implementation "androidx.constraintlayout:constraintlayout:2.2.1"
    implementation "androidx.constraintlayout:constraintlayout-compose:1.1.1"
    implementation "androidx.cardview:cardview:1.0.0"
    implementation 'androidx.recyclerview:recyclerview:1.4.0'

    implementation 'com.tencent:mmkv:1.3.14'

    def composeBom = platform('androidx.compose:compose-bom:2025.06.01')
    implementation composeBom
    implementation "androidx.compose.material:material"
    implementation 'androidx.compose.material3:material3'
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-util"
    implementation "androidx.compose.ui:ui-viewbinding"
    implementation "androidx.compose.ui:ui-tooling"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.animation:animation"
    implementation "androidx.compose.foundation:foundation"
    implementation "androidx.compose.foundation:foundation-layout"
    implementation "androidx.compose.material:material-icons-extended"

    implementation "com.google.accompanist:accompanist-permissions:0.37.3"
    implementation "com.google.accompanist:accompanist-systemuicontroller:0.36.0"

    def kermit = '2.0.6'
    implementation "co.touchlab:kermit:$kermit"
    implementation "co.touchlab:kermit-koin:$kermit"

    def orbit_mvi_version = '10.0.0'
    implementation "org.orbit-mvi:orbit-viewmodel:$orbit_mvi_version"
    implementation "org.orbit-mvi:orbit-compose:$orbit_mvi_version"

    def koinBom = platform('io.insert-koin:koin-bom:4.1.0')
    implementation koinBom
    implementation "io.insert-koin:koin-core"
    implementation "io.insert-koin:koin-android"
    implementation "io.insert-koin:koin-androidx-compose"

    def coroutines = '1.10.2'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutines"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$coroutines"

    def compose_destinations = '1.11.9'
    implementation "io.github.raamcosta.compose-destinations:animations-core:$compose_destinations"
    ksp "io.github.raamcosta.compose-destinations:ksp:$compose_destinations"

    def room_version = "2.7.2"
    implementation "androidx.room:room-ktx:$room_version"
    implementation "androidx.room:room-runtime:$room_version"
    ksp "androidx.room:room-compiler:$room_version"

    def lifecycle_version = "2.9.1"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-process:$lifecycle_version"

    def work_version = "2.10.2"
    implementation "androidx.work:work-runtime:$work_version"

    // date
    implementation 'org.jetbrains.kotlinx:kotlinx-datetime:0.6.2'

    def moshi_version = '1.15.2'
    implementation "com.squareup.moshi:moshi:$moshi_version"
    ksp("com.squareup.moshi:moshi-kotlin-codegen:$moshi_version")
    implementation 'com.github.doyaaaaaken:kotlin-csv-jvm:1.10.0' // csv

    def andromedaVersion = "6.2.0"
    implementation "com.github.kylecorry31.andromeda:core:$andromedaVersion"
    implementation "com.github.kylecorry31.andromeda:camera:$andromedaVersion"
    implementation("com.github.kylecorry31:sol:9.4.0")

    // network
    def ktorfit = "2.5.1"
    ksp("de.jensklingenberg.ktorfit:ktorfit-ksp:$ktorfit")
    implementation("de.jensklingenberg.ktorfit:ktorfit-lib-light:$ktorfit")
    implementation("de.jensklingenberg.ktorfit:ktorfit-converters-response:$ktorfit")
    implementation("io.ktor:ktor-client-okhttp:3.1.2")

    // camera x
    def cameraxVersion = "1.4.2"
    implementation("androidx.camera:camera-camera2:$cameraxVersion")
    implementation("androidx.camera:camera-lifecycle:$cameraxVersion")
    implementation("androidx.camera:camera-view:$cameraxVersion")

    // 3rd view libs
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0' // graph & chart
    implementation 'com.marosseleng.android:compose-material3-datetime-pickers:0.7.2'
    implementation 'com.github.zj565061763:compose-wheel-picker:1.0.0-alpha14'
    implementation 'com.airbnb.android:lottie-compose:6.6.7'
    implementation 'com.github.commandiron:WheelPickerCompose:1.1.11'
    implementation 'com.github.GrenderG:Toasty:1.5.2'
    implementation 'com.zjun:rule-view:0.0.1'

    // gms
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    implementation "com.android.installreferrer:installreferrer:2.2"
    implementation 'com.google.android.gms:play-services-base:18.7.0'
    implementation 'com.google.android.play:review-ktx:2.0.2'
    implementation("com.google.android.ump:user-messaging-platform:3.2.0")

    // firebase
    implementation platform('com.google.firebase:firebase-bom:33.15.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-config'
    implementation 'com.google.firebase:firebase-messaging'

    // ad
    implementation 'com.applovin:applovin-sdk:13.3.1'
//    implementation 'com.applovin.mediation:google-adapter:+'
//    implementation 'com.applovin.mediation:facebook-adapter:+'
//    implementation 'com.applovin.mediation:mintegral-adapter:+'
//    implementation 'com.applovin.mediation:bytedance-adapter:+'
//    implementation 'com.applovin.mediation:unityads-adapter:+'
//    implementation 'com.bigossp:max-mediation:+'

    implementation 'com.google.android.gms:play-services-ads:23.6.0'

    // analytic
    implementation('com.tenjin:android-sdk:1.16.7')

    // apache commons
    implementation('commons-codec:commons-codec:1.18.0')

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation composeBom
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
